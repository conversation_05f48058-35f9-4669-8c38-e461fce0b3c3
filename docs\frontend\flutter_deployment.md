# Flutter Deployment and Build Guide

## Table of Contents
1. [Build Configuration](#build-configuration)
2. [Android Deployment](#android-deployment)
3. [iOS Deployment](#ios-deployment)
4. [Web Deployment](#web-deployment)
5. [Desktop Deployment](#desktop-deployment)
6. [Code Obfuscation](#code-obfuscation)
7. [App Signing](#app-signing)
8. [CI/CD Integration](#cicd-integration)
9. [Store Publishing](#store-publishing)
10. [React Native Migration Comparison](#react-native-migration-comparison)

## Build Configuration

### Build Modes

Flutter supports three build modes:

```bash
# Debug mode (default for development)
flutter run
flutter build apk --debug

# Profile mode (performance testing)
flutter run --profile
flutter build apk --profile

# Release mode (production)
flutter run --release
flutter build apk --release
```

### Build Flavors

Configure different app variants using flavors:

```yaml
# pubspec.yaml
flutter:
  assets:
    - assets/config/
  generate: true

# Create flavor-specific configuration
```

**Android Flavors (android/app/build.gradle.kts):**

```kotlin
android {
    flavorDimensions += "version"
    
    productFlavors {
        create("dev") {
            dimension = "version"
            applicationIdSuffix = ".dev"
            versionNameSuffix = "-dev"
            resValue("string", "app_name", "MyApp Dev")
        }
        
        create("staging") {
            dimension = "version"
            applicationIdSuffix = ".staging"
            versionNameSuffix = "-staging"
            resValue("string", "app_name", "MyApp Staging")
        }
        
        create("prod") {
            dimension = "version"
            resValue("string", "app_name", "MyApp")
        }
    }
}
```

**iOS Flavors (ios/Runner/Info.plist):**

```xml
<key>CFBundleDisplayName</key>
<string>$(APP_DISPLAY_NAME)</string>
```

**Build Commands:**

```bash
# Build specific flavors
flutter build apk --flavor dev
flutter build ipa --flavor staging
flutter run --flavor prod
```

### Environment Configuration

```dart
// lib/config/app_config.dart
class AppConfig {
  static const String _environment = String.fromEnvironment(
    'ENVIRONMENT',
    defaultValue: 'dev',
  );
  
  static const String _apiBaseUrl = String.fromEnvironment(
    'API_BASE_URL',
    defaultValue: 'https://api-dev.example.com',
  );
  
  static bool get isDevelopment => _environment == 'dev';
  static bool get isStaging => _environment == 'staging';
  static bool get isProduction => _environment == 'prod';
  
  static String get apiBaseUrl => _apiBaseUrl;
  
  static String get appName {
    switch (_environment) {
      case 'dev':
        return 'MyApp Dev';
      case 'staging':
        return 'MyApp Staging';
      default:
        return 'MyApp';
    }
  }
}

// Usage
class ApiService {
  static const baseUrl = AppConfig.apiBaseUrl;
  
  static Future<Response> get(String endpoint) async {
    final url = '$baseUrl/$endpoint';
    return await http.get(Uri.parse(url));
  }
}
```

**Build with Environment Variables:**

```bash
flutter build apk --dart-define=ENVIRONMENT=prod --dart-define=API_BASE_URL=https://api.example.com
```

## Android Deployment

### App Signing

**1. Create Keystore:**

```bash
# Generate keystore
keytool -genkey -v -keystore ~/upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload

# On Windows
keytool -genkey -v -keystore %USERPROFILE%\upload-keystore.jks -storetype JKS -keyalg RSA -keysize 2048 -validity 10000 -alias upload
```

**2. Configure Signing (android/key.properties):**

```properties
storePassword=your_keystore_password
keyPassword=your_key_password
keyAlias=upload
storeFile=/path/to/upload-keystore.jks
```

**3. Update Gradle Configuration (android/app/build.gradle.kts):**

```kotlin
import java.util.Properties
import java.io.FileInputStream

// Load keystore properties
val keystoreProperties = Properties()
val keystorePropertiesFile = rootProject.file("key.properties")
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(FileInputStream(keystorePropertiesFile))
}

android {
    // Signing configuration
    signingConfigs {
        create("release") {
            keyAlias = keystoreProperties["keyAlias"] as String
            keyPassword = keystoreProperties["keyPassword"] as String
            storeFile = keystoreProperties["storeFile"]?.let { file(it) }
            storePassword = keystoreProperties["storePassword"] as String
        }
    }
    
    buildTypes {
        release {
            signingConfig = signingConfigs.getByName("release")
            isMinifyEnabled = true
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
    }
}
```

### Build Android App

**App Bundle (Recommended):**

```bash
# Build app bundle
flutter build appbundle --release

# Build with obfuscation
flutter build appbundle --release --obfuscate --split-debug-info=build/debug-info

# Build specific flavor
flutter build appbundle --release --flavor prod
```

**APK:**

```bash
# Build APK (all architectures)
flutter build apk --release

# Build split APKs (recommended for direct distribution)
flutter build apk --release --split-per-abi

# Results in:
# build/app/outputs/apk/release/app-armeabi-v7a-release.apk
# build/app/outputs/apk/release/app-arm64-v8a-release.apk
# build/app/outputs/apk/release/app-x86_64-release.apk
```

### App Manifest Configuration

**android/app/src/main/AndroidManifest.xml:**

```xml
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <application
        android:name="${applicationName}"
        android:exported="true"
        android:label="@string/app_name"
        android:icon="@mipmap/ic_launcher"
        android:enableOnBackInvokedCallback="true">
        
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />
                
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
            
            <!-- Deep linking -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="https"
                      android:host="myapp.example.com" />
            </intent-filter>
        </activity>
        
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
    
    <!-- Permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
</manifest>
```

### Launcher Icons

**Using flutter_launcher_icons package:**

```yaml
# pubspec.yaml
dev_dependencies:
  flutter_launcher_icons: ^0.13.1

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icon/icon.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/icon/icon.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "assets/icon/icon.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/icon/icon.png"
```

```bash
# Generate icons
flutter packages pub run flutter_launcher_icons
```

## iOS Deployment

### Xcode Project Configuration

**1. Bundle Identifier:**
- Open `ios/Runner.xcworkspace` in Xcode
- Select Runner → Runner target
- Set unique Bundle Identifier (e.g., com.yourcompany.yourapp)

**2. Team and Signing:**
- Select your development team
- Enable "Automatically manage signing"
- Or configure manual signing with provisioning profiles

**3. Deployment Target:**
- Set minimum iOS version (Flutter supports iOS 12+)
- Update `ios/Flutter/AppFrameworkInfo.plist` MinimumOSVersion

### Build iOS App

**Using Flutter CLI:**

```bash
# Build iOS app bundle
flutter build ipa --release

# Build with obfuscation
flutter build ipa --release --obfuscate --split-debug-info=build/debug-info

# Build specific flavor
flutter build ipa --release --flavor prod

# Build with custom export options
flutter build ipa --release --export-options-plist=ios/ExportOptions.plist
```

**Using Xcode:**

```bash
# Open workspace
open ios/Runner.xcworkspace

# Archive from Xcode:
# Product → Archive → Distribute App
```

### App Store Connect Upload

**Method 1: Transporter App**
- Install Transporter from Mac App Store
- Drag and drop `.ipa` file
- Upload to App Store Connect

**Method 2: Command Line**

```bash
# Using xcrun altool (deprecated)
xcrun altool --upload-app --type ios -f build/ios/ipa/*.ipa --apiKey your_api_key --apiIssuer your_issuer_id

# Using xcrun notarytool (recommended)
xcrun notarytool submit build/ios/ipa/*.ipa --apple-id your_apple_id --password your_app_password --team-id your_team_id
```

**Method 3: Codemagic CLI Tools**

```bash
# Install Codemagic CLI tools
pip3 install codemagic-cli-tools

# Set environment variables
export APP_STORE_CONNECT_ISSUER_ID=your_issuer_id
export APP_STORE_CONNECT_KEY_IDENTIFIER=your_key_id
export APP_STORE_CONNECT_PRIVATE_KEY="$(cat /path/to/AuthKey_XXX.p8)"

# Upload to App Store Connect
app-store-connect publish --path build/ios/ipa/*.ipa
```

### iOS Info.plist Configuration

**ios/Runner/Info.plist:**

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleDevelopmentRegion</key>
    <string>$(DEVELOPMENT_LANGUAGE)</string>
    <key>CFBundleDisplayName</key>
    <string>My App</string>
    <key>CFBundleExecutable</key>
    <string>$(EXECUTABLE_NAME)</string>
    <key>CFBundleIdentifier</key>
    <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>myapp</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>$(FLUTTER_BUILD_NAME)</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>CFBundleVersion</key>
    <string>$(FLUTTER_BUILD_NUMBER)</string>
    <key>LSRequiresIPhoneOS</key>
    <true/>
    <key>UILaunchStoryboardName</key>
    <string>LaunchScreen</string>
    <key>UIMainStoryboardFile</key>
    <string>Main</string>
    <key>UISupportedInterfaceOrientations</key>
    <array>
        <string>UIInterfaceOrientationPortrait</string>
        <string>UIInterfaceOrientationLandscapeLeft</string>
        <string>UIInterfaceOrientationLandscapeRight</string>
    </array>
    <key>UISupportedInterfaceOrientations~ipad</key>
    <array>
        <string>UIInterfaceOrientationPortrait</string>
        <string>UIInterfaceOrientationPortraitUpsideDown</string>
        <string>UIInterfaceOrientationLandscapeLeft</string>
        <string>UIInterfaceOrientationLandscapeRight</string>
    </array>
    
    <!-- Permissions -->
    <key>NSCameraUsageDescription</key>
    <string>This app needs access to camera to take photos</string>
    <key>NSMicrophoneUsageDescription</key>
    <string>This app needs access to microphone to record audio</string>
    <key>NSLocationWhenInUseUsageDescription</key>
    <string>This app needs location access to provide location-based features</string>
    
    <!-- URL Schemes -->
    <key>CFBundleURLTypes</key>
    <array>
        <dict>
            <key>CFBundleURLName</key>
            <string>myapp.example.com</string>
            <key>CFBundleURLSchemes</key>
            <array>
                <string>myapp</string>
            </array>
        </dict>
    </array>
    
    <!-- App Transport Security -->
    <key>NSAppTransportSecurity</key>
    <dict>
        <key>NSAllowsArbitraryLoads</key>
        <false/>
        <key>NSExceptionDomains</key>
        <dict>
            <key>api.example.com</key>
            <dict>
                <key>NSExceptionAllowsInsecureHTTPLoads</key>
                <true/>
            </dict>
        </dict>
    </dict>
</dict>
</plist>
```

## Web Deployment

### Build Web App

```bash
# Build for web
flutter build web --release

# Build with specific web renderer
flutter build web --web-renderer canvaskit --release
flutter build web --web-renderer html --release

# Build with base href for subdirectory deployment
flutter build web --base-href /myapp/ --release
```

### Web Configuration

**web/index.html:**

```html
<!DOCTYPE html>
<html>
<head>
  <base href="$FLUTTER_BASE_HREF">
  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="My Flutter Web App">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="My App">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">
  <link rel="icon" type="image/png" href="favicon.png"/>
  <title>My App</title>
  <link rel="manifest" href="manifest.json">
  
  <style>
    /* Loading screen styles */
    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      background-color: #fff;
    }
  </style>
</head>
<body>
  <div id="loading" class="loading">
    <div>Loading...</div>
  </div>
  
  <script>
    window.addEventListener('load', function(ev) {
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        },
        onEntrypointLoaded: function(engineInitializer) {
          engineInitializer.initializeEngine().then(function(appRunner) {
            document.getElementById('loading').remove();
            appRunner.runApp();
          });
        }
      });
    });
  </script>
</body>
</html>
```

**Progressive Web App (web/manifest.json):**

```json
{
    "name": "My Flutter App",
    "short_name": "MyApp",
    "start_url": ".",
    "display": "standalone",
    "background_color": "#0175C2",
    "theme_color": "#0175C2",
    "description": "A new Flutter project.",
    "orientation": "portrait-primary",
    "prefer_related_applications": false,
    "icons": [
        {
            "src": "icons/Icon-192.png",
            "sizes": "192x192",
            "type": "image/png"
        },
        {
            "src": "icons/Icon-512.png",
            "sizes": "512x512",
            "type": "image/png"
        },
        {
            "src": "icons/Icon-maskable-192.png",
            "sizes": "192x192",
            "type": "image/png",
            "purpose": "maskable"
        },
        {
            "src": "icons/Icon-maskable-512.png",
            "sizes": "512x512",
            "type": "image/png",
            "purpose": "maskable"
        }
    ]
}
```

### Deployment Options

**1. Firebase Hosting:**

```bash
# Install Firebase CLI
npm install -g firebase-tools

# Initialize Firebase
firebase init hosting

# Deploy
firebase deploy --only hosting
```

**2. GitHub Pages:**

```yaml
# .github/workflows/deploy.yml
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.19.0'
        channel: 'stable'
    
    - name: Build web
      run: |
        flutter pub get
        flutter build web --base-href /your-repo-name/
    
    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./build/web
```

## Desktop Deployment

### Windows

```bash
# Build Windows app
flutter build windows --release

# Output: build/windows/x64/runner/Release/
```

**Package as MSIX:**

```yaml
# pubspec.yaml
dev_dependencies:
  msix: ^3.16.6

msix_config:
  display_name: My Flutter App
  publisher_display_name: Your Company
  identity_name: com.yourcompany.yourapp
  msix_version: 1.0.0.0
  logo_path: assets\icon\icon.png
  capabilities: 'internetClient,location,microphone,webcam'
```

```bash
flutter pub run msix:create
```

### macOS

```bash
# Build macOS app
flutter build macos --release

# Output: build/macos/Build/Products/Release/yourapp.app
```

**Package as DMG:**

```bash
# Create DMG
hdiutil create -volname "MyApp" -srcfolder build/macos/Build/Products/Release/yourapp.app -ov -format UDZO MyApp.dmg
```

### Linux

```bash
# Build Linux app
flutter build linux --release

# Output: build/linux/x64/release/bundle/
```

**Package as AppImage:**

```bash
# Install appimagetool
wget "https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage"
chmod +x appimagetool-x86_64.AppImage

# Create AppDir structure
mkdir -p MyApp.AppDir/usr/bin
cp -r build/linux/x64/release/bundle/* MyApp.AppDir/usr/bin/
cp assets/icon/icon.png MyApp.AppDir/myapp.png

# Create desktop file
cat > MyApp.AppDir/myapp.desktop << EOF
[Desktop Entry]
Name=MyApp
Exec=myapp
Icon=myapp
Type=Application
Categories=Utility;
EOF

# Create AppImage
./appimagetool-x86_64.AppImage MyApp.AppDir MyApp.AppImage
```

## Code Obfuscation

### Basic Obfuscation

```bash
# Build with obfuscation
flutter build apk --release --obfuscate --split-debug-info=build/debug-info
flutter build ipa --release --obfuscate --split-debug-info=build/debug-info
```

### Advanced Obfuscation Configuration

**Preserve specific symbols:**

```dart
// lib/obfuscation_config.dart
@pragma('vm:never-inline')
@pragma('vm:entry-point')
class ImportantClass {
  @pragma('vm:entry-point')
  static void importantMethod() {
    // This method will not be obfuscated
  }
}
```

**Create obfuscation whitelist:**

```yaml
# obfuscation.yaml
# Classes and methods to preserve
preserve:
  - class: 'MyImportantClass'
  - method: 'criticalMethod'
  - package: 'package:important_package'
```

### Symbol Map Management

```bash
# Store symbol maps securely
mkdir -p debug-symbols/android/$(date +%Y%m%d_%H%M%S)
cp build/debug-info/* debug-symbols/android/$(date +%Y%m%d_%H%M%S)/

# For crash reporting (Firebase Crashlytics)
flutter packages pub run flutter_plugin_tools symbol-upload \
  --platform android \
  --symbols-path build/debug-info
```

## CI/CD Integration

### GitHub Actions

```yaml
# .github/workflows/build-and-deploy.yml
name: Build and Deploy

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.19.0'
        channel: 'stable'
    
    - name: Install dependencies
      run: flutter pub get
    
    - name: Run tests
      run: flutter test
    
    - name: Analyze code
      run: flutter analyze

  build-android:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.19.0'
        channel: 'stable'
    
    - name: Setup Java
      uses: actions/setup-java@v3
      with:
        distribution: 'zulu'
        java-version: '17'
    
    - name: Decode keystore
      run: |
        echo "${{ secrets.KEYSTORE_BASE64 }}" | base64 --decode > android/keystore.jks
    
    - name: Create key.properties
      run: |
        echo "storePassword=${{ secrets.KEYSTORE_PASSWORD }}" > android/key.properties
        echo "keyPassword=${{ secrets.KEY_PASSWORD }}" >> android/key.properties
        echo "keyAlias=${{ secrets.KEY_ALIAS }}" >> android/key.properties
        echo "storeFile=../keystore.jks" >> android/key.properties
    
    - name: Build APK
      run: |
        flutter pub get
        flutter build apk --release --obfuscate --split-debug-info=build/debug-info
    
    - name: Upload APK
      uses: actions/upload-artifact@v3
      with:
        name: android-release
        path: build/app/outputs/apk/release/

  build-ios:
    needs: test
    runs-on: macos-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.19.0'
        channel: 'stable'
    
    - name: Install CocoaPods
      run: sudo gem install cocoapods
    
    - name: Setup certificates
      env:
        P12_PASSWORD: ${{ secrets.P12_PASSWORD }}
        PROVISIONING_PROFILE: ${{ secrets.PROVISIONING_PROFILE }}
      run: |
        # Setup signing certificates and provisioning profiles
        echo "${{ secrets.CERTIFICATE_P12 }}" | base64 --decode > certificate.p12
        echo "$PROVISIONING_PROFILE" | base64 --decode > profile.mobileprovision
    
    - name: Build IPA
      run: |
        flutter pub get
        flutter build ipa --release --obfuscate --split-debug-info=build/debug-info
    
    - name: Upload IPA
      uses: actions/upload-artifact@v3
      with:
        name: ios-release
        path: build/ios/ipa/
```

### Fastlane Integration

**Android (android/fastlane/Fastfile):**

```ruby
platform :android do
  desc "Deploy to internal track"
  lane :internal do
    gradle(
      task: "bundle",
      build_type: "Release",
      project_dir: "android/"
    )
    
    upload_to_play_store(
      track: 'internal',
      aab: '../build/app/outputs/bundle/release/app-release.aab',
      skip_upload_metadata: true,
      skip_upload_images: true,
      skip_upload_screenshots: true
    )
  end
  
  desc "Deploy to production"
  lane :production do
    gradle(
      task: "bundle",
      build_type: "Release",
      project_dir: "android/"
    )
    
    upload_to_play_store(
      track: 'production',
      aab: '../build/app/outputs/bundle/release/app-release.aab'
    )
  end
end
```

**iOS (ios/fastlane/Fastfile):**

```ruby
platform :ios do
  desc "Build and upload to TestFlight"
  lane :beta do
    build_app(
      workspace: "Runner.xcworkspace",
      scheme: "Runner",
      export_method: "app-store"
    )
    
    upload_to_testflight(
      skip_waiting_for_build_processing: true
    )
  end
  
  desc "Deploy to App Store"
  lane :release do
    build_app(
      workspace: "Runner.xcworkspace",
      scheme: "Runner",
      export_method: "app-store"
    )
    
    upload_to_app_store(
      force: true,
      submit_for_review: true,
      automatic_release: false,
      submission_information: {
        add_id_info_uses_idfa: false,
        add_id_info_serves_ads: false,
        add_id_info_tracks_install: false,
        add_id_info_tracks_action: false,
        add_id_info_limits_tracking: false,
        content_rights_has_rights: true,
        content_rights_contains_third_party_content: false,
        export_compliance_platform: 'ios',
        export_compliance_compliance_required: false,
        export_compliance_encryption_updated: false,
        export_compliance_app_type: nil,
        export_compliance_uses_encryption: false,
        export_compliance_is_exempt: false,
        export_compliance_contains_third_party_cryptography: false,
        export_compliance_contains_proprietary_cryptography: false,
        export_compliance_available_on_french_store: false
      }
    )
  end
end
```

## Store Publishing

### Google Play Store

**1. Prepare Store Listing:**
- App title and description
- Screenshots and videos
- Feature graphic
- Privacy policy URL
- Content rating

**2. Release Management:**

```bash
# Upload to internal testing
flutter build appbundle --release
# Upload via Play Console or Fastlane
```

**3. Play Console Configuration:**
- Set up closed testing tracks
- Configure pre-launch reports
- Enable crash reporting
- Set up in-app purchases (if needed)

### Apple App Store

**1. App Store Connect Setup:**
- App metadata and descriptions
- Screenshots for all device sizes
- App preview videos
- Pricing and availability
- App Review Information

**2. TestFlight Distribution:**

```bash
# Build and upload to TestFlight
flutter build ipa --release
# Upload via Transporter or Xcode
```

**3. App Store Review:**
- Submit for review
- Respond to review feedback
- Monitor review status

## React Native Migration Comparison

### Build Process Differences

| Aspect | React Native | Flutter |
|--------|--------------|---------|
| **Android** | Gradle + React Native CLI | Gradle + Flutter CLI |
| **iOS** | Xcode + React Native CLI | Xcode + Flutter CLI |
| **Build Time** | Slower (Metro bundler) | Faster (AOT compilation) |
| **Bundle Size** | Larger (JS bundle + native) | Smaller (optimized binary) |
| **Code Splitting** | Metro + Hermes | Tree shaking built-in |

### Deployment Differences

| Feature | React Native | Flutter |
|---------|--------------|---------|
| **Hot Updates** | CodePush support | No built-in support |
| **Obfuscation** | Manual setup required | Built-in support |
| **Platform Channels** | Native modules | Platform channels |
| **Build Flavors** | Manual configuration | Built-in flavor support |
| **Web Deployment** | React Native Web | Native web support |

### Migration Scripts

**Package.json to Pubspec conversion:**

```javascript
// migration-helper.js
const fs = require('fs');
const yaml = require('js-yaml');

function convertPackageJsonToPubspec(packageJsonPath, pubspecPath) {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  const pubspec = {
    name: packageJson.name.replace(/[^a-z0-9_]/g, '_'),
    description: packageJson.description || 'A new Flutter project.',
    version: packageJson.version || '1.0.0+1',
    
    environment: {
      sdk: '>=3.0.0 <4.0.0',
      flutter: '>=3.19.0'
    },
    
    dependencies: {
      flutter: { sdk: 'flutter' }
    },
    
    dev_dependencies: {
      flutter_test: { sdk: 'flutter' },
      flutter_lints: '^3.0.0'
    },
    
    flutter: {
      uses_material_design: true
    }
  };
  
  // Map common React Native dependencies to Flutter equivalents
  const dependencyMap = {
    'react-navigation': 'go_router',
    'react-native-async-storage': 'shared_preferences',
    'react-native-image-picker': 'image_picker',
    'react-native-permissions': 'permission_handler',
    'react-native-vector-icons': 'flutter_icons',
    'react-native-webview': 'webview_flutter'
  };
  
  Object.keys(packageJson.dependencies || {}).forEach(dep => {
    if (dependencyMap[dep]) {
      pubspec.dependencies[dependencyMap[dep]] = '^latest';
    }
  });
  
  const yamlContent = yaml.dump(pubspec, { indent: 2 });
  fs.writeFileSync(pubspecPath, yamlContent);
  
  console.log('Pubspec.yaml generated successfully!');
  console.log('Please update dependency versions manually.');
}

// Usage
convertPackageJsonToPubspec('./package.json', './pubspec.yaml');
```

### Build Script Migration

**React Native build script:**
```json
{
  "scripts": {
    "android": "react-native run-android",
    "ios": "react-native run-ios",
    "build:android": "cd android && ./gradlew assembleRelease",
    "build:ios": "react-native run-ios --configuration Release"
  }
}
```

**Flutter equivalent (Makefile):**
```makefile
# Makefile
.PHONY: help dev build clean

help:
	@echo "Available commands:"
	@echo "  dev-android    - Run on Android device"
	@echo "  dev-ios        - Run on iOS device"
	@echo "  build-android  - Build Android APK"
	@echo "  build-ios      - Build iOS IPA"
	@echo "  build-web      - Build web app"
	@echo "  test           - Run tests"
	@echo "  clean          - Clean build files"

dev-android:
	flutter run --flavor dev -t lib/main_dev.dart

dev-ios:
	flutter run --flavor dev -t lib/main_dev.dart

build-android:
	flutter build apk --release --flavor prod

build-ios:
	flutter build ipa --release --flavor prod

build-web:
	flutter build web --release

test:
	flutter test

clean:
	flutter clean
	cd ios && xcodebuild clean
	cd android && ./gradlew clean
```

This comprehensive deployment and build guide covers all aspects of Flutter app deployment across platforms, with detailed configuration examples and migration guidance from React Native. The documentation provides practical, production-ready solutions for building and deploying Flutter applications.
