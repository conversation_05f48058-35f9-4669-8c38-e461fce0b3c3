import 'package:json_annotation/json_annotation.dart';

part 'notification_model.g.dart';

@JsonSerializable()
class Notification {
  final String id;
  @Json<PERSON>ey(name: 'user_id')
  final String userId;
  final String title;
  final String message;
  final NotificationType type;
  final NotificationPriority priority;
  @J<PERSON><PERSON><PERSON>(name: 'is_read')
  final bool isRead;
  @Json<PERSON>ey(name: 'action_url')
  final String? actionUrl;
  final Map<String, dynamic>? metadata;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'expires_at')
  final DateTime? expiresAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt;
  @Json<PERSON>ey(name: 'updated_at')
  final DateTime updatedAt;

  const Notification({
    required this.id,
    required this.userId,
    required this.title,
    required this.message,
    required this.type,
    required this.priority,
    required this.isRead,
    this.actionUrl,
    this.metadata,
    this.expiresAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Notification.fromJson(Map<String, dynamic> json) => _$NotificationFromJson(json);
  Map<String, dynamic> toJson() => _$NotificationToJson(this);
}

@JsonEnum()
enum NotificationType {
  @JsonValue('system')
  system,
  @JsonValue('quest')
  quest,
  @JsonValue('achievement')
  achievement,
  @JsonValue('social')
  social,
  @JsonValue('marketplace')
  marketplace,
  @JsonValue('reminder')
  reminder,
  @JsonValue('update')
  update,
  @JsonValue('promotion')
  promotion,
}

@JsonEnum()
enum NotificationPriority {
  @JsonValue('low')
  low,
  @JsonValue('normal')
  normal,
  @JsonValue('high')
  high,
  @JsonValue('urgent')
  urgent,
}

extension NotificationTypeExtension on NotificationType {
  String get displayName {
    switch (this) {
      case NotificationType.system:
        return 'System';
      case NotificationType.quest:
        return 'Quest';
      case NotificationType.achievement:
        return 'Achievement';
      case NotificationType.social:
        return 'Social';
      case NotificationType.marketplace:
        return 'Marketplace';
      case NotificationType.reminder:
        return 'Reminder';
      case NotificationType.update:
        return 'Update';
      case NotificationType.promotion:
        return 'Promotion';
    }
  }

  String get iconName {
    switch (this) {
      case NotificationType.system:
        return 'settings';
      case NotificationType.quest:
        return 'assignment';
      case NotificationType.achievement:
        return 'emoji_events';
      case NotificationType.social:
        return 'people';
      case NotificationType.marketplace:
        return 'shopping_cart';
      case NotificationType.reminder:
        return 'alarm';
      case NotificationType.update:
        return 'update';
      case NotificationType.promotion:
        return 'local_offer';
    }
  }
}

extension NotificationPriorityExtension on NotificationPriority {
  String get displayName {
    switch (this) {
      case NotificationPriority.low:
        return 'Low';
      case NotificationPriority.normal:
        return 'Normal';
      case NotificationPriority.high:
        return 'High';
      case NotificationPriority.urgent:
        return 'Urgent';
    }
  }
}
