import 'package:flutter/material.dart';

/// Responsive breakpoints following Material Design 3 guidelines
/// Based on Flutter's official adaptive design documentation
class ResponsiveBreakpoints {
  // Material Design 3 window size classes
  static const double compact = 600;      // Mobile phones
  static const double medium = 840;       // Tablets, foldables
  static const double expanded = 1200;    // Desktop, large tablets
  static const double large = 1600;       // Large desktop screens

  // Legacy support
  static const double mobile = compact;
  static const double tablet = medium;
  static const double desktop = expanded;
  static const double largeDesktop = large;
}

/// Device type enumeration based on Material Design 3 window size classes
enum DeviceType {
  compact,        // Mobile phones (< 600dp)
  medium,         // Tablets, foldables (600-840dp)
  expanded,       // Desktop, large tablets (840-1200dp)
  large,          // Large desktop screens (> 1200dp)

  // Legacy aliases for backward compatibility
  mobile,
  tablet,
  desktop,
  largeDesktop,
}

/// Window size class for adaptive layouts
enum WindowSizeClass {
  compact,
  medium,
  expanded,
  large,
}

/// Responsive helper class for adaptive layouts
/// Following Flutter's official adaptive design best practices
class ResponsiveHelper {
  /// Get current device type based on screen width
  /// Uses Material Design 3 window size classes
  static DeviceType getDeviceType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    if (width < ResponsiveBreakpoints.compact) {
      return DeviceType.compact;
    } else if (width < ResponsiveBreakpoints.medium) {
      return DeviceType.medium;
    } else if (width < ResponsiveBreakpoints.expanded) {
      return DeviceType.expanded;
    } else {
      return DeviceType.large;
    }
  }

  /// Get window size class for adaptive layouts
  static WindowSizeClass getWindowSizeClass(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    if (width < ResponsiveBreakpoints.compact) {
      return WindowSizeClass.compact;
    } else if (width < ResponsiveBreakpoints.medium) {
      return WindowSizeClass.medium;
    } else if (width < ResponsiveBreakpoints.expanded) {
      return WindowSizeClass.expanded;
    } else {
      return WindowSizeClass.large;
    }
  }
  
  /// Check if current device is mobile/compact
  static bool isMobile(BuildContext context) {
    final type = getDeviceType(context);
    return type == DeviceType.compact || type == DeviceType.mobile;
  }

  /// Check if current device is tablet/medium
  static bool isTablet(BuildContext context) {
    final type = getDeviceType(context);
    return type == DeviceType.medium || type == DeviceType.tablet;
  }

  /// Check if current device is desktop/expanded or larger
  static bool isDesktop(BuildContext context) {
    final type = getDeviceType(context);
    return type == DeviceType.expanded ||
           type == DeviceType.large ||
           type == DeviceType.desktop ||
           type == DeviceType.largeDesktop;
  }

  /// Check if device supports navigation rail (tablet and above)
  static bool supportsNavigationRail(BuildContext context) {
    return !isMobile(context);
  }

  /// Check if device should use dense layout
  static bool shouldUseDenseLayout(BuildContext context) {
    return isMobile(context);
  }
  
  /// Get responsive value based on device type
  /// Supports both new and legacy device type names
  static T getResponsiveValue<T>(
    BuildContext context, {
    required T mobile,
    T? tablet,
    T? desktop,
    T? largeDesktop,
    T? compact,
    T? medium,
    T? expanded,
    T? large,
  }) {
    final deviceType = getDeviceType(context);

    switch (deviceType) {
      case DeviceType.compact:
      case DeviceType.mobile:
        return compact ?? mobile;
      case DeviceType.medium:
      case DeviceType.tablet:
        return medium ?? tablet ?? compact ?? mobile;
      case DeviceType.expanded:
      case DeviceType.desktop:
        return expanded ?? desktop ?? medium ?? tablet ?? compact ?? mobile;
      case DeviceType.large:
      case DeviceType.largeDesktop:
        return large ?? largeDesktop ?? expanded ?? desktop ?? medium ?? tablet ?? compact ?? mobile;
    }
  }
  
  /// Get responsive padding following Material Design 3 spacing
  static EdgeInsets getResponsivePadding(BuildContext context) {
    return getResponsiveValue(
      context,
      mobile: const EdgeInsets.all(16),
      tablet: const EdgeInsets.all(24),
      desktop: const EdgeInsets.all(32),
      large: const EdgeInsets.all(40),
    );
  }

  /// Get responsive margin
  static EdgeInsets getResponsiveMargin(BuildContext context) {
    return getResponsiveValue(
      context,
      mobile: const EdgeInsets.all(8),
      tablet: const EdgeInsets.all(12),
      desktop: const EdgeInsets.all(16),
      large: const EdgeInsets.all(20),
    );
  }

  /// Get responsive horizontal padding for content
  static EdgeInsets getResponsiveHorizontalPadding(BuildContext context) {
    return getResponsiveValue(
      context,
      mobile: const EdgeInsets.symmetric(horizontal: 16),
      tablet: const EdgeInsets.symmetric(horizontal: 32),
      desktop: const EdgeInsets.symmetric(horizontal: 48),
      large: const EdgeInsets.symmetric(horizontal: 64),
    );
  }
  
  /// Get responsive font size with proper scaling
  static double getResponsiveFontSize(
    BuildContext context, {
    required double baseFontSize,
  }) {
    return getResponsiveValue(
      context,
      mobile: baseFontSize,
      tablet: baseFontSize * 1.05,
      desktop: baseFontSize * 1.1,
      large: baseFontSize * 1.15,
    );
  }

  /// Get responsive grid columns based on content type
  static int getGridColumns(BuildContext context, {String contentType = 'default'}) {
    switch (contentType) {
      case 'cards':
        return getResponsiveValue(
          context,
          mobile: 1,
          tablet: 2,
          desktop: 3,
          large: 4,
        );
      case 'items':
        return getResponsiveValue(
          context,
          mobile: 2,
          tablet: 3,
          desktop: 4,
          large: 6,
        );
      default:
        return getResponsiveValue(
          context,
          mobile: 1,
          tablet: 2,
          desktop: 3,
          large: 4,
        );
    }
  }
  
  /// Get responsive card width with proper constraints
  static double getCardWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final padding = getResponsiveHorizontalPadding(context);
    final availableWidth = screenWidth - padding.horizontal;

    return getResponsiveValue(
      context,
      mobile: availableWidth,
      tablet: (availableWidth - 16) / 2,
      desktop: (availableWidth - 32) / 3,
      large: (availableWidth - 48) / 4,
    );
  }

  /// Get responsive max width for content following Material Design guidelines
  static double getMaxContentWidth(BuildContext context) {
    return getResponsiveValue(
      context,
      mobile: double.infinity,
      tablet: 840,
      desktop: 1200,
      large: 1600,
    );
  }

  /// Get adaptive navigation type
  static NavigationType getNavigationType(BuildContext context) {
    if (isMobile(context)) {
      return NavigationType.bottom;
    } else if (isTablet(context)) {
      return NavigationType.rail;
    } else {
      return NavigationType.drawer;
    }
  }

  /// Get responsive icon size
  static double getIconSize(BuildContext context, {double baseSize = 24}) {
    return getResponsiveValue(
      context,
      mobile: baseSize,
      tablet: baseSize * 1.1,
      desktop: baseSize * 1.2,
      large: baseSize * 1.3,
    );
  }
}

/// Navigation type for adaptive layouts
enum NavigationType {
  bottom,   // Bottom navigation bar for mobile
  rail,     // Navigation rail for tablet
  drawer,   // Navigation drawer for desktop
}

/// Responsive widget that adapts based on screen size
class ResponsiveWidget extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  final Widget? largeDesktop;
  
  const ResponsiveWidget({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.largeDesktop,
  });
  
  @override
  Widget build(BuildContext context) {
    return ResponsiveHelper.getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
      largeDesktop: largeDesktop,
    );
  }
}

/// Responsive layout builder
class ResponsiveLayoutBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, DeviceType deviceType) builder;
  
  const ResponsiveLayoutBuilder({
    super.key,
    required this.builder,
  });
  
  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveHelper.getDeviceType(context);
    return builder(context, deviceType);
  }
}

/// Responsive grid view
class ResponsiveGridView extends StatelessWidget {
  final List<Widget> children;
  final double spacing;
  final double runSpacing;
  final EdgeInsets? padding;
  final ScrollPhysics? physics;
  
  const ResponsiveGridView({
    super.key,
    required this.children,
    this.spacing = 16,
    this.runSpacing = 16,
    this.padding,
    this.physics,
  });
  
  @override
  Widget build(BuildContext context) {
    final columns = ResponsiveHelper.getGridColumns(context);
    
    return GridView.builder(
      padding: padding ?? ResponsiveHelper.getResponsivePadding(context),
      physics: physics,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        crossAxisSpacing: spacing,
        mainAxisSpacing: runSpacing,
        childAspectRatio: 1.0,
      ),
      itemCount: children.length,
      itemBuilder: (context, index) => children[index],
    );
  }
}

/// Responsive container with max width and adaptive constraints
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final bool centerContent;
  final double? maxWidth;
  final bool applyHorizontalPadding;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.centerContent = true,
    this.maxWidth,
    this.applyHorizontalPadding = true,
  });

  @override
  Widget build(BuildContext context) {
    final responsiveMaxWidth = maxWidth ?? ResponsiveHelper.getMaxContentWidth(context);
    final responsivePadding = applyHorizontalPadding
        ? (padding ?? ResponsiveHelper.getResponsiveHorizontalPadding(context))
        : padding;

    Widget content = Container(
      width: double.infinity,
      constraints: BoxConstraints(maxWidth: responsiveMaxWidth),
      padding: responsivePadding,
      margin: margin,
      child: child,
    );

    if (centerContent && ResponsiveHelper.isDesktop(context)) {
      content = Center(child: content);
    }

    return content;
  }
}

/// Adaptive layout builder that provides different layouts for different screen sizes
class AdaptiveLayoutBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, BoxConstraints constraints) compact;
  final Widget Function(BuildContext context, BoxConstraints constraints)? medium;
  final Widget Function(BuildContext context, BoxConstraints constraints)? expanded;
  final Widget Function(BuildContext context, BoxConstraints constraints)? large;

  const AdaptiveLayoutBuilder({
    super.key,
    required this.compact,
    this.medium,
    this.expanded,
    this.large,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final windowSizeClass = ResponsiveHelper.getWindowSizeClass(context);

        switch (windowSizeClass) {
          case WindowSizeClass.compact:
            return compact(context, constraints);
          case WindowSizeClass.medium:
            return (medium ?? compact)(context, constraints);
          case WindowSizeClass.expanded:
            return (expanded ?? medium ?? compact)(context, constraints);
          case WindowSizeClass.large:
            return (large ?? expanded ?? medium ?? compact)(context, constraints);
        }
      },
    );
  }
}
