/// Centralized asset management for the application
/// This ensures type-safe access to all assets and prevents typos
class AppAssets {
  // Private constructor to prevent instantiation
  AppAssets._();

  // Base paths
  static const String _imagesPath = 'assets/images';
  static const String _iconsPath = 'assets/icons';
  static const String _animationsPath = 'assets/animations';
  static const String _fontsPath = 'assets/fonts';

  // Images
  static const String logo = '$_imagesPath/logo.png';
  static const String logoLight = '$_imagesPath/logo_light.png';
  static const String logoDark = '$_imagesPath/logo_dark.png';
  static const String placeholder = '$_imagesPath/placeholder.png';
  static const String avatarPlaceholder = '$_imagesPath/avatar_placeholder.png';
  static const String backgroundPattern = '$_imagesPath/background_pattern.png';
  static const String onboardingImage1 = '$_imagesPath/onboarding_1.png';
  static const String onboardingImage2 = '$_imagesPath/onboarding_2.png';
  static const String onboardingImage3 = '$_imagesPath/onboarding_3.png';
  static const String emptyStateQuests = '$_imagesPath/empty_quests.png';
  static const String emptyStateAchievements = '$_imagesPath/empty_achievements.png';
  static const String emptyStateNotifications = '$_imagesPath/empty_notifications.png';

  // Icons
  static const String iconQuest = '$_iconsPath/quest.svg';
  static const String iconAchievement = '$_iconsPath/achievement.svg';
  static const String iconWallet = '$_iconsPath/wallet.svg';
  static const String iconMarketplace = '$_iconsPath/marketplace.svg';
  static const String iconLeaderboard = '$_iconsPath/leaderboard.svg';
  static const String iconProfile = '$_iconsPath/profile.svg';
  static const String iconSettings = '$_iconsPath/settings.svg';
  static const String iconNotification = '$_iconsPath/notification.svg';
  static const String iconDashboard = '$_iconsPath/dashboard.svg';
  static const String iconStats = '$_iconsPath/stats.svg';
  static const String iconReward = '$_iconsPath/reward.svg';
  static const String iconBadge = '$_iconsPath/badge.svg';
  static const String iconCoin = '$_iconsPath/coin.svg';
  static const String iconGem = '$_iconsPath/gem.svg';
  static const String iconStar = '$_iconsPath/star.svg';
  static const String iconTrophy = '$_iconsPath/trophy.svg';
  static const String iconMedal = '$_iconsPath/medal.svg';
  static const String iconCrown = '$_iconsPath/crown.svg';
  static const String iconShield = '$_iconsPath/shield.svg';
  static const String iconSword = '$_iconsPath/sword.svg';
  static const String iconMagic = '$_iconsPath/magic.svg';
  static const String iconFire = '$_iconsPath/fire.svg';
  static const String iconLightning = '$_iconsPath/lightning.svg';

  // Quest type icons
  static const String iconQuestDaily = '$_iconsPath/quest_daily.svg';
  static const String iconQuestWeekly = '$_iconsPath/quest_weekly.svg';
  static const String iconQuestMonthly = '$_iconsPath/quest_monthly.svg';
  static const String iconQuestSpecial = '$_iconsPath/quest_special.svg';
  static const String iconQuestChain = '$_iconsPath/quest_chain.svg';

  // Achievement category icons
  static const String iconAchievementBeginner = '$_iconsPath/achievement_beginner.svg';
  static const String iconAchievementExplorer = '$_iconsPath/achievement_explorer.svg';
  static const String iconAchievementCollector = '$_iconsPath/achievement_collector.svg';
  static const String iconAchievementSocial = '$_iconsPath/achievement_social.svg';
  static const String iconAchievementMaster = '$_iconsPath/achievement_master.svg';

  // Animations
  static const String animationLoading = '$_animationsPath/loading.json';
  static const String animationSuccess = '$_animationsPath/success.json';
  static const String animationError = '$_animationsPath/error.json';
  static const String animationEmpty = '$_animationsPath/empty.json';
  static const String animationCelebration = '$_animationsPath/celebration.json';
  static const String animationLevelUp = '$_animationsPath/level_up.json';
  static const String animationAchievement = '$_animationsPath/achievement.json';
  static const String animationReward = '$_animationsPath/reward.json';
  static const String animationCoins = '$_animationsPath/coins.json';
  static const String animationSplash = '$_animationsPath/splash.json';
  static const String animationOnboarding = '$_animationsPath/onboarding.json';

  // Fonts
  static const String fontPrimary = 'Inter';
  static const String fontSecondary = 'Roboto';
  static const String fontDisplay = 'Poppins';

  // Asset lists for preloading
  static const List<String> criticalImages = [
    logo,
    logoLight,
    logoDark,
    placeholder,
    avatarPlaceholder,
  ];

  static const List<String> criticalIcons = [
    iconQuest,
    iconAchievement,
    iconWallet,
    iconMarketplace,
    iconLeaderboard,
    iconProfile,
    iconSettings,
    iconNotification,
    iconDashboard,
  ];

  static const List<String> criticalAnimations = [
    animationLoading,
    animationSuccess,
    animationError,
  ];

  static const List<String> onboardingAssets = [
    onboardingImage1,
    onboardingImage2,
    onboardingImage3,
    animationOnboarding,
    animationSplash,
  ];

  static const List<String> gameplayAssets = [
    iconQuestDaily,
    iconQuestWeekly,
    iconQuestMonthly,
    iconQuestSpecial,
    iconQuestChain,
    iconAchievementBeginner,
    iconAchievementExplorer,
    iconAchievementCollector,
    iconAchievementSocial,
    iconAchievementMaster,
    animationCelebration,
    animationLevelUp,
    animationAchievement,
    animationReward,
    animationCoins,
  ];

  static const List<String> emptyStateAssets = [
    emptyStateQuests,
    emptyStateAchievements,
    emptyStateNotifications,
    animationEmpty,
  ];

  /// Get all assets for preloading
  static List<String> getAllAssets() {
    return [
      ...criticalImages,
      ...criticalIcons,
      ...criticalAnimations,
      ...onboardingAssets,
      ...gameplayAssets,
      ...emptyStateAssets,
    ];
  }

  /// Get assets by category
  static List<String> getAssetsByCategory(AssetCategory category) {
    switch (category) {
      case AssetCategory.critical:
        return [...criticalImages, ...criticalIcons, ...criticalAnimations];
      case AssetCategory.onboarding:
        return onboardingAssets;
      case AssetCategory.gameplay:
        return gameplayAssets;
      case AssetCategory.emptyStates:
        return emptyStateAssets;
    }
  }
}

/// Asset categories for organized loading
enum AssetCategory {
  critical,
  onboarding,
  gameplay,
  emptyStates,
}
