import 'package:json_annotation/json_annotation.dart';

part 'quest_model.g.dart';

@JsonSerializable()
class Quest {
  final String id;
  final String title;
  final String description;
  final String? image;
  final QuestType type;
  final QuestCategory category;
  final QuestDifficulty difficulty;
  final QuestStatus status;
  final int points;
  final int experience;
  @J<PERSON><PERSON><PERSON>(name: 'estimated_duration')
  final int estimatedDuration; // in minutes
  @<PERSON>son<PERSON>ey(name: 'max_participants')
  final int? maxParticipants;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_active')
  final bool isActive;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_featured')
  final bool isFeatured;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'start_date')
  final DateTime? startDate;
  @J<PERSON><PERSON><PERSON>(name: 'end_date')
  final DateTime? endDate;
  @Json<PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt;
  @<PERSON>son<PERSON><PERSON>(name: 'updated_at')
  final DateTime updatedAt;
  final List<QuestStep>? steps;
  final List<QuestReward>? rewards;

  const Quest({
    required this.id,
    required this.title,
    required this.description,
    this.image,
    required this.type,
    required this.category,
    required this.difficulty,
    required this.status,
    required this.points,
    required this.experience,
    required this.estimatedDuration,
    this.maxParticipants,
    required this.isActive,
    required this.isFeatured,
    this.startDate,
    this.endDate,
    required this.createdAt,
    required this.updatedAt,
    this.steps,
    this.rewards,
  });

  factory Quest.fromJson(Map<String, dynamic> json) => _$QuestFromJson(json);
  Map<String, dynamic> toJson() => _$QuestToJson(this);

  /// Create a copy of this Quest with some fields replaced
  Quest copyWith({
    String? id,
    String? title,
    String? description,
    String? image,
    QuestType? type,
    QuestCategory? category,
    QuestDifficulty? difficulty,
    QuestStatus? status,
    int? points,
    int? experience,
    int? estimatedDuration,
    int? maxParticipants,
    bool? isActive,
    bool? isFeatured,
    DateTime? startDate,
    DateTime? endDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<QuestStep>? steps,
    List<QuestReward>? rewards,
  }) {
    return Quest(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      image: image ?? this.image,
      type: type ?? this.type,
      category: category ?? this.category,
      difficulty: difficulty ?? this.difficulty,
      status: status ?? this.status,
      points: points ?? this.points,
      experience: experience ?? this.experience,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      maxParticipants: maxParticipants ?? this.maxParticipants,
      isActive: isActive ?? this.isActive,
      isFeatured: isFeatured ?? this.isFeatured,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      steps: steps ?? this.steps,
      rewards: rewards ?? this.rewards,
    );
  }
}

@JsonSerializable()
class QuestStep {
  final String id;
  final String title;
  final String description;
  final int order;
  final QuestStepType type;
  final Map<String, dynamic>? requirements;
  final bool isCompleted;
  final DateTime? completedAt;

  const QuestStep({
    required this.id,
    required this.title,
    required this.description,
    required this.order,
    required this.type,
    this.requirements,
    required this.isCompleted,
    this.completedAt,
  });

  factory QuestStep.fromJson(Map<String, dynamic> json) => _$QuestStepFromJson(json);
  Map<String, dynamic> toJson() => _$QuestStepToJson(this);
}

@JsonSerializable()
class QuestReward {
  final String id;
  final QuestRewardType type;
  final String name;
  final String description;
  final int quantity;
  final String? imageUrl;
  final Map<String, dynamic>? metadata;

  const QuestReward({
    required this.id,
    required this.type,
    required this.name,
    required this.description,
    required this.quantity,
    this.imageUrl,
    this.metadata,
  });

  factory QuestReward.fromJson(Map<String, dynamic> json) => _$QuestRewardFromJson(json);
  Map<String, dynamic> toJson() => _$QuestRewardToJson(this);
}

@JsonSerializable()
class QuestProgress {
  final String id;
  @JsonKey(name: 'user_id')
  final String userId;
  @JsonKey(name: 'quest_id')
  final String questId;
  final Quest quest;
  final QuestStatus status;
  @JsonKey(name: 'current_step')
  final int currentStep;
  @JsonKey(name: 'total_steps')
  final int totalSteps;
  @JsonKey(name: 'completed_steps')
  final List<String> completedSteps;
  final int progress; // percentage 0-100
  @JsonKey(name: 'started_at')
  final DateTime startedAt;
  @JsonKey(name: 'completed_at')
  final DateTime? completedAt;
  @JsonKey(name: 'last_activity_at')
  final DateTime lastActivityAt;
  final Map<String, dynamic>? metadata;

  const QuestProgress({
    required this.id,
    required this.userId,
    required this.questId,
    required this.quest,
    required this.status,
    required this.currentStep,
    required this.totalSteps,
    required this.completedSteps,
    required this.progress,
    required this.startedAt,
    this.completedAt,
    required this.lastActivityAt,
    this.metadata,
  });

  factory QuestProgress.fromJson(Map<String, dynamic> json) => _$QuestProgressFromJson(json);
  Map<String, dynamic> toJson() => _$QuestProgressToJson(this);

  /// Check if the quest is completed
  bool get isCompleted => status == QuestStatus.completed;

  /// Check if the quest is in progress
  bool get isInProgress => status == QuestStatus.inProgress || status == QuestStatus.active;

  /// Get progress as a percentage
  double get progressPercentage => progress / 100.0;

  /// Check if a specific step is completed
  bool isStepCompleted(String stepId) => completedSteps.contains(stepId);
}

@JsonEnum()
enum QuestType {
  @JsonValue('daily')
  daily,
  @JsonValue('weekly')
  weekly,
  @JsonValue('monthly')
  monthly,
  @JsonValue('one_time')
  oneTime,
  @JsonValue('chain')
  chain,
  @JsonValue('collaborative')
  collaborative,
}

@JsonEnum()
enum QuestCategory {
  @JsonValue('fitness')
  fitness,
  @JsonValue('learning')
  learning,
  @JsonValue('social')
  social,
  @JsonValue('creative')
  creative,
  @JsonValue('productivity')
  productivity,
  @JsonValue('health')
  health,
  @JsonValue('adventure')
  adventure,
  @JsonValue('skill')
  skill,
  @JsonValue('daily')
  daily,
  @JsonValue('weekly')
  weekly,
}

@JsonEnum()
enum QuestDifficulty {
  @JsonValue('easy')
  easy,
  @JsonValue('medium')
  medium,
  @JsonValue('hard')
  hard,
  @JsonValue('expert')
  expert,
}

@JsonEnum()
enum QuestStatus {
  @JsonValue('not_started')
  notStarted,
  @JsonValue('active')
  active,
  @JsonValue('in_progress')
  inProgress,
  @JsonValue('completed')
  completed,
  @JsonValue('failed')
  failed,
  @JsonValue('abandoned')
  abandoned,
}

@JsonEnum()
enum QuestStepType {
  @JsonValue('action')
  action,
  @JsonValue('submission')
  submission,
  @JsonValue('verification')
  verification,
  @JsonValue('timer')
  timer,
  @JsonValue('location')
  location,
  @JsonValue('social')
  social,
}

@JsonEnum()
enum QuestRewardType {
  @JsonValue('points')
  points,
  @JsonValue('badge')
  badge,
  @JsonValue('item')
  item,
  @JsonValue('currency')
  currency,
  @JsonValue('experience')
  experience,
  @JsonValue('nft')
  nft,
}

extension QuestTypeExtension on QuestType {
  String get displayName {
    switch (this) {
      case QuestType.daily:
        return 'Daily';
      case QuestType.weekly:
        return 'Weekly';
      case QuestType.monthly:
        return 'Monthly';
      case QuestType.oneTime:
        return 'One Time';
      case QuestType.chain:
        return 'Chain';
      case QuestType.collaborative:
        return 'Collaborative';
    }
  }
}

extension QuestCategoryExtension on QuestCategory {
  String get displayName {
    switch (this) {
      case QuestCategory.fitness:
        return 'Fitness';
      case QuestCategory.learning:
        return 'Learning';
      case QuestCategory.social:
        return 'Social';
      case QuestCategory.creative:
        return 'Creative';
      case QuestCategory.productivity:
        return 'Productivity';
      case QuestCategory.health:
        return 'Health';
      case QuestCategory.adventure:
        return 'Adventure';
      case QuestCategory.skill:
        return 'Skill';
      case QuestCategory.daily:
        return 'Daily';
      case QuestCategory.weekly:
        return 'Weekly';
    }
  }
}

extension QuestDifficultyExtension on QuestDifficulty {
  String get displayName {
    switch (this) {
      case QuestDifficulty.easy:
        return 'Easy';
      case QuestDifficulty.medium:
        return 'Medium';
      case QuestDifficulty.hard:
        return 'Hard';
      case QuestDifficulty.expert:
        return 'Expert';
    }
  }

  int get multiplier {
    switch (this) {
      case QuestDifficulty.easy:
        return 1;
      case QuestDifficulty.medium:
        return 2;
      case QuestDifficulty.hard:
        return 3;
      case QuestDifficulty.expert:
        return 5;
    }
  }
}

extension QuestStatusExtension on QuestStatus {
  String get displayName {
    switch (this) {
      case QuestStatus.notStarted:
        return 'Not Started';
      case QuestStatus.active:
        return 'Active';
      case QuestStatus.inProgress:
        return 'In Progress';
      case QuestStatus.completed:
        return 'Completed';
      case QuestStatus.failed:
        return 'Failed';
      case QuestStatus.abandoned:
        return 'Abandoned';
    }
  }

  bool get isActive => this == QuestStatus.active || this == QuestStatus.inProgress;
  bool get isCompleted => this == QuestStatus.completed;
  bool get canStart => this == QuestStatus.notStarted;
  bool get isFinished => this == QuestStatus.completed || this == QuestStatus.failed || this == QuestStatus.abandoned;
}
