class AppConstants {  // API Configuration
  static const String apiBaseUrl = 'http://localhost:8080/api';
  static const String baseUrl = 'http://localhost:8080/api';
  static const String websocketUrl = 'ws://localhost:8080/ws';
  static const String wsUrl = 'ws://localhost:8080/ws';
  
  // App Information
  static const String appName = 'Quester';
  static const String appVersion = '1.0.0';
  
  // Storage Keys
  static const String accessTokenKey = 'access_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  
  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 400);
  static const Duration longAnimationDuration = Duration(milliseconds: 600);
  
  // UI Constants
  static const double borderRadius = 12.0;
  static const double cardElevation = 4.0;
  static const double iconSize = 24.0;
  static const double buttonHeight = 50.0;
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Quest Types
  static const List<String> questTypes = [
    'daily',
    'weekly',
    'monthly',
    'special',
    'achievement'
  ];
  
  // Quest Status
  static const List<String> questStatus = [
    'active',
    'completed',
    'failed',
    'pending'
  ];
  
  // Achievement Categories
  static const List<String> achievementCategories = [
    'gameplay',
    'social',
    'completion',
    'streak',
    'special'
  ];
  
  // Wallet Transaction Types
  static const List<String> transactionTypes = [
    'quest_reward',
    'achievement_bonus',
    'marketplace_purchase',
    'marketplace_sale',
    'daily_bonus',
    'referral_bonus'
  ];
  
  // Error Messages
  static const String genericErrorMessage = 'Something went wrong. Please try again.';
  static const String networkErrorMessage = 'Network error. Please check your connection.';
  static const String authErrorMessage = 'Authentication failed. Please login again.';
  
  // Success Messages
  static const String loginSuccessMessage = 'Welcome back!';
  static const String questCompletedMessage = 'Quest completed successfully!';
  static const String achievementUnlockedMessage = 'Achievement unlocked!';
}
