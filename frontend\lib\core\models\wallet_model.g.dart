// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wallet_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Wallet _$WalletFromJson(Map<String, dynamic> json) => Wallet(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      balance: (json['balance'] as num).toInt(),
      currency: json['currency'] as String,
      totalEarned: (json['total_earned'] as num).toInt(),
      totalSpent: (json['total_spent'] as num).toInt(),
      transactions: (json['transactions'] as List<dynamic>)
          .map((e) => WalletTransaction.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$WalletToJson(Wallet instance) => <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'balance': instance.balance,
      'currency': instance.currency,
      'total_earned': instance.totalEarned,
      'total_spent': instance.totalSpent,
      'transactions': instance.transactions,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

WalletTransaction _$WalletTransactionFromJson(Map<String, dynamic> json) =>
    WalletTransaction(
      id: json['id'] as String,
      walletId: json['wallet_id'] as String,
      userId: json['user_id'] as String,
      type: $enumDecode(_$TransactionTypeEnumMap, json['type']),
      amount: (json['amount'] as num).toInt(),
      currency: json['currency'] as String,
      description: json['description'] as String,
      status: $enumDecode(_$WalletTransactionStatusEnumMap, json['status']),
      referenceId: json['reference_id'] as String?,
      referenceType: json['reference_type'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      balanceBefore: (json['balance_before'] as num).toInt(),
      balanceAfter: (json['balance_after'] as num).toInt(),
      createdAt: DateTime.parse(json['created_at'] as String),
    );

Map<String, dynamic> _$WalletTransactionToJson(WalletTransaction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'wallet_id': instance.walletId,
      'user_id': instance.userId,
      'type': _$TransactionTypeEnumMap[instance.type]!,
      'amount': instance.amount,
      'currency': instance.currency,
      'description': instance.description,
      'status': _$WalletTransactionStatusEnumMap[instance.status]!,
      'reference_id': instance.referenceId,
      'reference_type': instance.referenceType,
      'metadata': instance.metadata,
      'balance_before': instance.balanceBefore,
      'balance_after': instance.balanceAfter,
      'created_at': instance.createdAt.toIso8601String(),
    };

const _$TransactionTypeEnumMap = {
  TransactionType.questReward: 'quest_reward',
  TransactionType.achievementReward: 'achievement_reward',
  TransactionType.dailyBonus: 'daily_bonus',
  TransactionType.purchase: 'purchase',
  TransactionType.refund: 'refund',
  TransactionType.adminAdjustment: 'admin_adjustment',
  TransactionType.bonus: 'bonus',
  TransactionType.penalty: 'penalty',
};

const _$WalletTransactionStatusEnumMap = {
  WalletTransactionStatus.pending: 'pending',
  WalletTransactionStatus.completed: 'completed',
  WalletTransactionStatus.failed: 'failed',
  WalletTransactionStatus.cancelled: 'cancelled',
};

WalletSummary _$WalletSummaryFromJson(Map<String, dynamic> json) =>
    WalletSummary(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      wallet: Wallet.fromJson(json['wallet'] as Map<String, dynamic>),
      recentTransactions: (json['recent_transactions'] as List<dynamic>)
          .map((e) => WalletTransaction.fromJson(e as Map<String, dynamic>))
          .toList(),
      monthlyEarned: (json['monthly_earned'] as num).toInt(),
      monthlySpent: (json['monthly_spent'] as num).toInt(),
      weeklyEarned: (json['weekly_earned'] as num).toInt(),
      weeklySpent: (json['weekly_spent'] as num).toInt(),
      totalTransactions: (json['total_transactions'] as num).toInt(),
      averageTransaction: (json['average_transaction'] as num).toDouble(),
      largestTransaction: (json['largest_transaction'] as num).toInt(),
      earningStreak: (json['earning_streak'] as num).toInt(),
      lastTransactionAt: json['last_transaction_at'] == null
          ? null
          : DateTime.parse(json['last_transaction_at'] as String),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$WalletSummaryToJson(WalletSummary instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'wallet': instance.wallet,
      'recent_transactions': instance.recentTransactions,
      'monthly_earned': instance.monthlyEarned,
      'monthly_spent': instance.monthlySpent,
      'weekly_earned': instance.weeklyEarned,
      'weekly_spent': instance.weeklySpent,
      'total_transactions': instance.totalTransactions,
      'average_transaction': instance.averageTransaction,
      'largest_transaction': instance.largestTransaction,
      'earning_streak': instance.earningStreak,
      'last_transaction_at': instance.lastTransactionAt?.toIso8601String(),
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };
