import 'package:flutter/foundation.dart';

/// Log levels for the application
enum LogLevel {
  debug,
  info,
  warning,
  error,
  fatal,
}

/// Application logger with different log levels and formatting
class AppLogger {
  static const String _tag = 'Quester';
  
  /// Log debug messages (only in debug mode)
  static void debug(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    if (kDebugMode) {
      _log(LogLevel.debug, message, tag: tag, error: error, stackTrace: stackTrace);
    }
  }
  
  /// Log info messages
  static void info(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(LogLevel.info, message, tag: tag, error: error, stackTrace: stackTrace);
  }
  
  /// Log warning messages
  static void warning(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(LogLevel.warning, message, tag: tag, error: error, stackTrace: stackTrace);
  }
  
  /// Log error messages
  static void error(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(LogLevel.error, message, tag: tag, error: error, stackTrace: stackTrace);
  }
  
  /// Log fatal error messages
  static void fatal(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(LogLevel.fatal, message, tag: tag, error: error, stackTrace: stackTrace);
  }
  
  /// Internal logging method
  static void _log(
    LogLevel level,
    String message, {
    String? tag,
    Object? error,
    StackTrace? stackTrace,
  }) {
    final timestamp = DateTime.now().toIso8601String();
    final logTag = tag ?? _tag;
    final levelStr = level.name.toUpperCase().padRight(7);
    
    final logMessage = '[$timestamp] [$levelStr] [$logTag] $message';
    
    // Print to console
    debugPrint(logMessage);
    
    // Print error and stack trace if provided
    if (error != null) {
      debugPrint('Error: $error');
    }
    
    if (stackTrace != null) {
      debugPrint('StackTrace: $stackTrace');
    }
    
    // In production, you might want to send logs to a remote service
    if (!kDebugMode && (level == LogLevel.error || level == LogLevel.fatal)) {
      _sendToRemoteLogging(level, message, tag: logTag, error: error, stackTrace: stackTrace);
    }
  }
  
  /// Send logs to remote logging service (placeholder)
  static void _sendToRemoteLogging(
    LogLevel level,
    String message, {
    String? tag,
    Object? error,
    StackTrace? stackTrace,
  }) {
    // TODO: Implement remote logging service integration
    // Examples: Firebase Crashlytics, Sentry, LogRocket, etc.
  }
  
  /// Log API requests
  static void apiRequest(String method, String url, {Map<String, dynamic>? data}) {
    debug('API Request: $method $url', tag: 'API');
    if (data != null && data.isNotEmpty) {
      debug('Request Data: $data', tag: 'API');
    }
  }
  
  /// Log API responses
  static void apiResponse(String method, String url, int statusCode, {dynamic data}) {
    info('API Response: $method $url - $statusCode', tag: 'API');
    if (data != null) {
      debug('Response Data: $data', tag: 'API');
    }
  }
  
  /// Log WebSocket events
  static void websocket(String event, {String? data}) {
    info('WebSocket: $event', tag: 'WS');
    if (data != null) {
      debug('WS Data: $data', tag: 'WS');
    }
  }
  
  /// Log navigation events
  static void navigation(String route, {Map<String, dynamic>? params}) {
    info('Navigation: $route', tag: 'NAV');
    if (params != null && params.isNotEmpty) {
      debug('Nav Params: $params', tag: 'NAV');
    }
  }
  
  /// Log user actions
  static void userAction(String action, {Map<String, dynamic>? context}) {
    info('User Action: $action', tag: 'USER');
    if (context != null && context.isNotEmpty) {
      debug('Action Context: $context', tag: 'USER');
    }
  }
  
  /// Log performance metrics
  static void performance(String metric, Duration duration, {Map<String, dynamic>? context}) {
    info('Performance: $metric took ${duration.inMilliseconds}ms', tag: 'PERF');
    if (context != null && context.isNotEmpty) {
      debug('Perf Context: $context', tag: 'PERF');
    }
  }
}
