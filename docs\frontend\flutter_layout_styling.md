# Flutter Layout and Styling Guide

## Table of Contents
1. [Layout Fundamentals](#layout-fundamentals)
2. [Constraint System](#constraint-system)
3. [Layout Widgets](#layout-widgets)
4. [Styling and Theming](#styling-and-theming)
5. [Responsive Design](#responsive-design)
6. [Custom Widgets](#custom-widgets)
7. [React Native vs Flutter Styling](#react-native-vs-flutter-styling)

## Layout Fundamentals

### Flutter's Layout Model
Flutter uses a constraint-based layout system where:
- Parent widgets pass constraints down to children
- Children determine their size within those constraints
- Parent positions children based on their sizes

```dart
// Basic layout structure
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Layout Demo')),
      body: Column(
        children: [
          Container(
            width: 200,
            height: 100,
            color: Colors.blue,
            child: Center(child: Text('Header')),
          ),
          Expanded(
            child: Container(
              color: Colors.green,
              child: Center(child: Text('Content')),
            ),
          ),
        ],
      ),
    );
  }
}
```

### Layout Process
1. **Constraints Flow Down**: Parent sends constraints to child
2. **Sizes Flow Up**: Child reports its size to parent
3. **Parent Positions Child**: Based on child's size and alignment

## Constraint System

### Understanding Constraints
```dart
// BoxConstraints example
Container(
  constraints: BoxConstraints(
    minWidth: 100,
    maxWidth: 300,
    minHeight: 50,
    maxHeight: 200,
  ),
  child: Text('Constrained content'),
)

// Tight constraints (exact size)
SizedBox(
  width: 200,
  height: 100,
  child: Container(color: Colors.red),
)

// Loose constraints (maximum size)
Container(
  width: double.infinity,
  height: 50,
  color: Colors.blue,
)
```

### Common Constraint Issues
```dart
// Problem: RenderFlex overflow
// Solution: Use Flexible or Expanded
Row(
  children: [
    Expanded(
      child: Container(
        color: Colors.red,
        child: Text('Long text that might overflow'),
      ),
    ),
    Container(
      width: 50,
      height: 50,
      color: Colors.blue,
    ),
  ],
)
```

## Layout Widgets

### Single Child Layouts

#### Container
```dart
Container(
  width: 200,
  height: 100,
  padding: EdgeInsets.all(16),
  margin: EdgeInsets.symmetric(vertical: 8),
  decoration: BoxDecoration(
    color: Colors.blue,
    borderRadius: BorderRadius.circular(8),
    boxShadow: [
      BoxShadow(
        color: Colors.grey.withOpacity(0.5),
        spreadRadius: 2,
        blurRadius: 5,
        offset: Offset(0, 3),
      ),
    ],
  ),
  child: Text('Styled Container'),
)
```

#### Padding and Margin
```dart
// Padding
Padding(
  padding: EdgeInsets.only(
    top: 16,
    left: 8,
    right: 8,
    bottom: 24,
  ),
  child: Text('Padded content'),
)

// Center
Center(
  child: Text('Centered content'),
)

// Align
Align(
  alignment: Alignment.topRight,
  child: Icon(Icons.close),
)
```

### Multi-Child Layouts

#### Column and Row
```dart
// Column (vertical layout)
Column(
  crossAxisAlignment: CrossAxisAlignment.start,
  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
  children: [
    Text('Item 1'),
    Text('Item 2'),
    Text('Item 3'),
  ],
)

// Row (horizontal layout)
Row(
  mainAxisAlignment: MainAxisAlignment.spaceBetween,
  children: [
    Icon(Icons.star),
    Text('Rating'),
    Text('4.5'),
  ],
)
```

#### Stack (Layered Layout)
```dart
Stack(
  children: [
    Container(
      width: 200,
      height: 200,
      color: Colors.blue,
    ),
    Positioned(
      top: 10,
      right: 10,
      child: Icon(Icons.favorite, color: Colors.red),
    ),
    Positioned(
      bottom: 10,
      left: 10,
      child: Text('Bottom Left'),
    ),
  ],
)
```

#### Wrap (Flowing Layout)
```dart
Wrap(
  spacing: 8.0,
  runSpacing: 4.0,
  children: [
    Chip(label: Text('Tag 1')),
    Chip(label: Text('Tag 2')),
    Chip(label: Text('Long Tag Name')),
    Chip(label: Text('Tag 4')),
  ],
)
```

### Flexible Layouts

#### Expanded and Flexible
```dart
Column(
  children: [
    Container(height: 100, color: Colors.red), // Fixed height
    Expanded(
      flex: 2,
      child: Container(color: Colors.green), // Takes 2/3 of remaining space
    ),
    Expanded(
      flex: 1,
      child: Container(color: Colors.blue), // Takes 1/3 of remaining space
    ),
  ],
)

// Flexible vs Expanded
Row(
  children: [
    Flexible(
      child: Container(
        height: 50,
        color: Colors.red,
        child: Text('Flexible - can be smaller'),
      ),
    ),
    Expanded(
      child: Container(
        height: 50,
        color: Colors.blue,
        child: Text('Expanded - takes all space'),
      ),
    ),
  ],
)
```

## Styling and Theming

### Text Styling
```dart
Text(
  'Styled Text',
  style: TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: Colors.blue,
    fontFamily: 'Roboto',
    letterSpacing: 1.2,
    decoration: TextDecoration.underline,
    decorationColor: Colors.red,
  ),
)

// Using Theme
Text(
  'Themed Text',
  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
    color: Colors.green,
  ),
)
```

### Container Decoration
```dart
Container(
  decoration: BoxDecoration(
    // Background
    color: Colors.white,
    
    // Gradient
    gradient: LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [Colors.blue, Colors.purple],
    ),
    
    // Border
    border: Border.all(
      color: Colors.grey,
      width: 2,
    ),
    
    // Border radius
    borderRadius: BorderRadius.circular(12),
    
    // Shadow
    boxShadow: [
      BoxShadow(
        color: Colors.black26,
        blurRadius: 8,
        offset: Offset(0, 4),
      ),
    ],
    
    // Shape
    shape: BoxShape.rectangle,
  ),
  child: Padding(
    padding: EdgeInsets.all(16),
    child: Text('Decorated Container'),
  ),
)
```

### Material Design Styling
```dart
// Card
Card(
  elevation: 4,
  margin: EdgeInsets.all(8),
  shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.circular(12),
  ),
  child: Padding(
    padding: EdgeInsets.all(16),
    child: Text('Card Content'),
  ),
)

// Material Button
ElevatedButton(
  onPressed: () {},
  style: ElevatedButton.styleFrom(
    backgroundColor: Colors.blue,
    foregroundColor: Colors.white,
    elevation: 4,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(8),
    ),
    padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
  ),
  child: Text('Styled Button'),
)
```

### Theme Configuration
```dart
// App-wide theme
MaterialApp(
  theme: ThemeData(
    // Primary color scheme
    primarySwatch: Colors.blue,
    primaryColor: Colors.blue,
    
    // Text theme
    textTheme: TextTheme(
      headlineLarge: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
      bodyLarge: TextStyle(fontSize: 16, color: Colors.black87),
    ),
    
    // App bar theme
    appBarTheme: AppBarTheme(
      backgroundColor: Colors.blue,
      foregroundColor: Colors.white,
      elevation: 4,
    ),
    
    // Button theme
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.blue,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    ),
  ),
  home: MyHomePage(),
)

// Dark theme support
MaterialApp(
  theme: ThemeData.light(),
  darkTheme: ThemeData.dark(),
  themeMode: ThemeMode.system, // Follows system setting
  home: MyHomePage(),
)
```

## Responsive Design

### MediaQuery for Screen Information
```dart
class ResponsiveWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final orientation = MediaQuery.of(context).orientation;
    
    return Container(
      width: screenSize.width * 0.8, // 80% of screen width
      child: orientation == Orientation.portrait
          ? Column(children: _buildChildren())
          : Row(children: _buildChildren()),
    );
  }
  
  List<Widget> _buildChildren() {
    return [
      Text('Item 1'),
      Text('Item 2'),
      Text('Item 3'),
    ];
  }
}
```

### LayoutBuilder for Parent Constraints
```dart
LayoutBuilder(
  builder: (context, constraints) {
    if (constraints.maxWidth > 600) {
      // Desktop layout
      return Row(
        children: [
          Expanded(flex: 1, child: Sidebar()),
          Expanded(flex: 3, child: MainContent()),
        ],
      );
    } else {
      // Mobile layout
      return Column(
        children: [
          MainContent(),
          BottomNavigation(),
        ],
      );
    }
  },
)
```

### Flexible Grid Layout
```dart
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  
  ResponsiveGrid({required this.children});
  
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final crossAxisCount = constraints.maxWidth > 600 ? 3 : 2;
        
        return GridView.builder(
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 1,
          ),
          itemCount: children.length,
          itemBuilder: (context, index) => children[index],
        );
      },
    );
  }
}
```

## Custom Widgets

### Creating Reusable Styled Components
```dart
class CustomCard extends StatelessWidget {
  final Widget child;
  final Color? backgroundColor;
  final double? elevation;
  final EdgeInsets? padding;
  
  const CustomCard({
    Key? key,
    required this.child,
    this.backgroundColor,
    this.elevation = 4,
    this.padding = const EdgeInsets.all(16),
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Card(
      color: backgroundColor ?? Theme.of(context).cardColor,
      elevation: elevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: padding!,
        child: child,
      ),
    );
  }
}

// Usage
CustomCard(
  backgroundColor: Colors.blue.shade50,
  child: Column(
    children: [
      Text('Title', style: Theme.of(context).textTheme.headlineSmall),
      SizedBox(height: 8),
      Text('Content goes here'),
    ],
  ),
)
```

### Custom Paint for Complex Styling
```dart
class CustomShapePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.blue
      ..style = PaintingStyle.fill;
    
    final path = Path()
      ..moveTo(0, size.height * 0.7)
      ..quadraticBezierTo(
        size.width * 0.25, size.height * 0.5,
        size.width * 0.5, size.height * 0.6,
      )
      ..quadraticBezierTo(
        size.width * 0.75, size.height * 0.7,
        size.width, size.height * 0.5,
      )
      ..lineTo(size.width, size.height)
      ..lineTo(0, size.height)
      ..close();
    
    canvas.drawPath(path, paint);
  }
  
  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

// Usage
CustomPaint(
  size: Size(double.infinity, 200),
  painter: CustomShapePainter(),
)
```

## React Native vs Flutter Styling

### Styling Comparison

| React Native | Flutter | Notes |
|--------------|---------|-------|
| `StyleSheet.create()` | `TextStyle()`, `BoxDecoration()` | Flutter uses specific style classes |
| `flex: 1` | `Expanded()` or `Flexible()` | Flutter uses wrapper widgets |
| `flexDirection: 'row'` | `Row()` | Flutter uses layout widgets |
| `justifyContent: 'center'` | `MainAxisAlignment.center` | Different property names |
| `alignItems: 'center'` | `CrossAxisAlignment.center` | Different property names |
| `margin`, `padding` | `EdgeInsets` | Flutter uses EdgeInsets class |
| `backgroundColor` | `color` property | Different property names |
| `borderRadius` | `BorderRadius.circular()` | Flutter uses BorderRadius class |

### Migration Examples

#### React Native Text Styling
```javascript
// React Native
const styles = StyleSheet.create({
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 16,
  }
});

<Text style={styles.title}>Title</Text>
```

#### Flutter Text Styling
```dart
// Flutter
Text(
  'Title',
  style: TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: Color(0xFF333333),
  ),
  textAlign: TextAlign.center,
)
// Margin handled by parent widget (Container, Padding, etc.)
```

#### React Native Layout
```javascript
// React Native
<View style={{
  flex: 1,
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: 16,
}}>
  <Text>Left</Text>
  <Text>Right</Text>
</View>
```

#### Flutter Layout
```dart
// Flutter
Container(
  padding: EdgeInsets.all(16),
  child: Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    crossAxisAlignment: CrossAxisAlignment.center,
    children: [
      Text('Left'),
      Text('Right'),
    ],
  ),
)
```

## Best Practices

### Performance Tips
1. **Use const constructors** when possible
2. **Minimize widget rebuilds** with proper state management
3. **Use ListView.builder** for long lists
4. **Avoid deeply nested widgets**
5. **Use RepaintBoundary** for expensive widgets

### Layout Guidelines
1. **Understand constraints** before debugging layout issues
2. **Use Flexible/Expanded** instead of fixed sizes when possible
3. **Prefer SingleChildScrollView** for scrollable content
4. **Use SafeArea** for proper screen edge handling
5. **Test on different screen sizes** early and often

### Styling Best Practices
1. **Create a consistent theme** early in development
2. **Use Theme.of(context)** for colors and text styles
3. **Extract common styles** into reusable widgets
4. **Follow Material Design** or Cupertino guidelines
5. **Consider accessibility** in color and size choices
