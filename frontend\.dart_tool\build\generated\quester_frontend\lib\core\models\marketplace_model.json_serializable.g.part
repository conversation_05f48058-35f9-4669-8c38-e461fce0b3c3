// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MarketplaceItem _$MarketplaceItemFromJson(Map<String, dynamic> json) =>
    MarketplaceItem(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      image: json['image'] as String?,
      category: $enumDecode(_$ItemCategoryEnumMap, json['category']),
      type: $enumDecode(_$ItemTypeEnumMap, json['type']),
      rarity: $enumDecode(_$ItemRarityEnumMap, json['rarity']),
      price: (json['price'] as num).toInt(),
      currency: json['currency'] as String,
      stockQuantity: (json['stock_quantity'] as num?)?.toInt(),
      isLimited: json['is_limited'] as bool,
      isActive: json['is_active'] as bool,
      isFeatured: json['is_featured'] as bool,
      purchaseLimit: (json['purchase_limit'] as num?)?.toInt(),
      levelRequirement: (json['level_requirement'] as num?)?.toInt(),
      availableFrom: json['available_from'] == null
          ? null
          : DateTime.parse(json['available_from'] as String),
      availableUntil: json['available_until'] == null
          ? null
          : DateTime.parse(json['available_until'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$MarketplaceItemToJson(MarketplaceItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'image': instance.image,
      'category': _$ItemCategoryEnumMap[instance.category]!,
      'type': _$ItemTypeEnumMap[instance.type]!,
      'rarity': _$ItemRarityEnumMap[instance.rarity]!,
      'price': instance.price,
      'currency': instance.currency,
      'stock_quantity': instance.stockQuantity,
      'is_limited': instance.isLimited,
      'is_active': instance.isActive,
      'is_featured': instance.isFeatured,
      'purchase_limit': instance.purchaseLimit,
      'level_requirement': instance.levelRequirement,
      'available_from': instance.availableFrom?.toIso8601String(),
      'available_until': instance.availableUntil?.toIso8601String(),
      'metadata': instance.metadata,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

const _$ItemCategoryEnumMap = {
  ItemCategory.cosmetic: 'cosmetic',
  ItemCategory.boost: 'boost',
  ItemCategory.consumable: 'consumable',
  ItemCategory.collectible: 'collectible',
  ItemCategory.utility: 'utility',
  ItemCategory.premium: 'premium',
};

const _$ItemTypeEnumMap = {
  ItemType.avatar: 'avatar',
  ItemType.badge: 'badge',
  ItemType.theme: 'theme',
  ItemType.emote: 'emote',
  ItemType.title: 'title',
  ItemType.xpBoost: 'xp_boost',
  ItemType.pointBoost: 'point_boost',
  ItemType.energy: 'energy',
  ItemType.hint: 'hint',
  ItemType.skip: 'skip',
};

const _$ItemRarityEnumMap = {
  ItemRarity.common: 'common',
  ItemRarity.uncommon: 'uncommon',
  ItemRarity.rare: 'rare',
  ItemRarity.epic: 'epic',
  ItemRarity.legendary: 'legendary',
};

Purchase _$PurchaseFromJson(Map<String, dynamic> json) => Purchase(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      itemId: json['item_id'] as String,
      quantity: (json['quantity'] as num).toInt(),
      unitPrice: (json['unit_price'] as num).toInt(),
      totalPrice: (json['total_price'] as num).toInt(),
      currency: json['currency'] as String,
      status: $enumDecode(_$PurchaseStatusEnumMap, json['status']),
      transactionId: json['transaction_id'] as String?,
      purchasedAt: DateTime.parse(json['purchased_at'] as String),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      item: json['item'] == null
          ? null
          : MarketplaceItem.fromJson(json['item'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PurchaseToJson(Purchase instance) => <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'item_id': instance.itemId,
      'quantity': instance.quantity,
      'unit_price': instance.unitPrice,
      'total_price': instance.totalPrice,
      'currency': instance.currency,
      'status': _$PurchaseStatusEnumMap[instance.status]!,
      'transaction_id': instance.transactionId,
      'purchased_at': instance.purchasedAt.toIso8601String(),
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'item': instance.item,
    };

const _$PurchaseStatusEnumMap = {
  PurchaseStatus.pending: 'pending',
  PurchaseStatus.completed: 'completed',
  PurchaseStatus.failed: 'failed',
  PurchaseStatus.refunded: 'refunded',
};

Cart _$CartFromJson(Map<String, dynamic> json) => Cart(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      items: (json['items'] as List<dynamic>)
          .map((e) => CartItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalPrice: (json['total_price'] as num).toInt(),
      currency: json['currency'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$CartToJson(Cart instance) => <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'items': instance.items,
      'total_price': instance.totalPrice,
      'currency': instance.currency,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

CartItem _$CartItemFromJson(Map<String, dynamic> json) => CartItem(
      id: json['id'] as String,
      cartId: json['cart_id'] as String,
      itemId: json['item_id'] as String,
      quantity: (json['quantity'] as num).toInt(),
      unitPrice: (json['unit_price'] as num).toInt(),
      totalPrice: (json['total_price'] as num).toInt(),
      addedAt: DateTime.parse(json['added_at'] as String),
      item: json['item'] == null
          ? null
          : MarketplaceItem.fromJson(json['item'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CartItemToJson(CartItem instance) => <String, dynamic>{
      'id': instance.id,
      'cart_id': instance.cartId,
      'item_id': instance.itemId,
      'quantity': instance.quantity,
      'unit_price': instance.unitPrice,
      'total_price': instance.totalPrice,
      'added_at': instance.addedAt.toIso8601String(),
      'item': instance.item,
    };
