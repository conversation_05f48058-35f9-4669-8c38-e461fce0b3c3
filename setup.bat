@echo off
setlocal enabledelayedexpansion

:: Colors for better readability
set "GREEN=[32m"
set "YELLOW=[33m"
set "RED=[31m"
set "NC=[0m"

:: Check for Docker
where docker >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo %RED%Docker is not installed. Please install Docker first.%NC%
    exit /b 1
)

:: Enable Docker Compose Bake for better build performance
set "COMPOSE_BAKE=true"

:: Set Docker Compose command
where docker-compose >nul 2>nul
if %ERRORLEVEL% equ 0 (
    set "DOCKER_COMPOSE=docker-compose"
) else (
    docker compose version >nul 2>nul
    if %ERRORLEVEL% equ 0 (
        set "DOCKER_COMPOSE=docker compose"
    ) else (
        echo %RED%Neither docker-compose nor docker compose is available%NC%
        exit /b 1
    )
)

if "%1"=="" goto :help
if "%1"=="help" goto :help
if "%1"=="start" goto :start
if "%1"=="stop" goto :stop
if "%1"=="restart" goto :restart
if "%1"=="build" goto :build
if "%1"=="logs" goto :logs
if "%1"=="frontend" goto :frontend
if "%1"=="backend" goto :backend
if "%1"=="db" goto :db
if "%1"=="redis" goto :redis
if "%1"=="status" goto :status
if "%1"=="clean" goto :clean

:help
echo %YELLOW%Quester Docker Setup Script%NC%
echo Usage: setup.bat [command]
echo.
echo Commands:
echo   start    - Start all containers
echo   stop     - Stop all containers
echo   restart  - Restart all containers
echo   build    - Build all containers
echo   logs     - View logs from all containers
echo   frontend - Access frontend container shell
echo   backend  - Access backend container shell
echo   db       - Access database container shell
echo   redis    - Access redis container shell
echo   status   - Check status of all containers
echo   clean    - Remove all containers and volumes (caution!)
echo   help     - Show this help message
goto :eof

:start
echo %GREEN%Starting Quester containers...%NC%
%DOCKER_COMPOSE% -f docker-compose.yml up -d
echo %GREEN%Containers started successfully!%NC%
goto :eof

:stop
echo %YELLOW%Stopping Quester containers...%NC%
%DOCKER_COMPOSE% -f docker-compose.yml down
echo %GREEN%Containers stopped successfully!%NC%
goto :eof

:restart
echo %YELLOW%Restarting Quester containers...%NC%
%DOCKER_COMPOSE% -f docker-compose.yml restart
echo %GREEN%Containers restarted successfully!%NC%
goto :eof

:build
echo %GREEN%Building Quester containers...%NC%
%DOCKER_COMPOSE% -f docker-compose.yml build
echo %GREEN%Containers built successfully!%NC%
goto :eof

:logs
echo %GREEN%Viewing logs from all containers...%NC%
%DOCKER_COMPOSE% -f docker-compose.yml logs -f
goto :eof

:frontend
echo %GREEN%Accessing frontend shell...%NC%
%DOCKER_COMPOSE% -f docker-compose.yml exec frontend sh
goto :eof

:backend
echo %GREEN%Accessing backend shell...%NC%
%DOCKER_COMPOSE% -f docker-compose.yml exec backend bash
goto :eof

:db
echo %GREEN%Accessing database shell...%NC%
%DOCKER_COMPOSE% -f docker-compose.yml exec postgres sh
goto :eof

:redis
echo %GREEN%Accessing redis shell...%NC%
%DOCKER_COMPOSE% -f docker-compose.yml exec redis sh
goto :eof

:status
echo %GREEN%Checking container status...%NC%
%DOCKER_COMPOSE% -f docker-compose.yml ps
goto :eof

:clean
echo %RED%WARNING: This will remove all containers and volumes!%NC%
set /p CONFIRM="Are you sure you want to continue? (y/n) "
if /i "%CONFIRM%"=="y" (
    echo %YELLOW%Removing all containers and volumes...%NC%
    %DOCKER_COMPOSE% -f docker-compose.yml down -v
    docker system prune -af
    echo %GREEN%Cleanup completed successfully!%NC%
) else (
    echo %YELLOW%Cleanup cancelled.%NC%
)
goto :eof