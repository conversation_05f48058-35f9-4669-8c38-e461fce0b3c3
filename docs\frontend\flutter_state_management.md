# Flutter State Management Guide

## Table of Contents
1. [State Management Fundamentals](#state-management-fundamentals)
2. [Built-in State Management](#built-in-state-management)
3. [Provider Pattern](#provider-pattern)
4. [<PERSON> Pattern](#bloc-pattern)
5. [Riverpod](#riverpod)
6. [GetX](#getx)
7. [Redux](#redux)
8. [State Management Comparison](#state-management-comparison)
9. [React Native vs Flutter State Management](#react-native-vs-flutter-state-management)

## State Management Fundamentals

### Types of State
- **Local State (Ephemeral)**: Widget-specific state contained in a single widget (StatefulWidget)
- **App State (Shared)**: State shared across multiple widgets and screens
- **Ephemeral State**: Short-lived state (animations, form inputs, current page in PageView)
- **Persistent State**: Data that survives app restarts (user preferences, shopping cart)

### State vs Widget Lifecycle
**Important Concept**: In Flutter, widgets are temporary objects used to construct a presentation, while State objects persist between calls to build(), allowing them to remember information.

```dart
// Widget: Temporary, immutable configuration
class Counter extends StatefulWidget {
  const Counter({super.key});
  
  @override
  State<Counter> createState() => _CounterState(); // Creates persistent state
}

// State: Persistent, mutable data
class _CounterState extends State<Counter> {
  int _counter = 0; // This persists between rebuilds
  
  @override
  Widget build(BuildContext context) {
    // This method is called every time setState() is triggered
    // Framework optimizes by diffing the widget tree
    return Column(
      children: [
        Text('Counter: $_counter'),
        ElevatedButton(
          onPressed: () {
            setState(() {
              _counter++; // Update state and trigger rebuild
            });
          },
          child: Text('Increment'),
        ),
      ],
    );
  }
}
```dart
// StatefulWidget lifecycle
class MyWidget extends StatefulWidget {
  @override
  _MyWidgetState createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> {
  int _counter = 0;
  
  @override
  void initState() {
    super.initState();
    // Initialize state
  }
  
  @override
  void dispose() {
    // Clean up resources
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text('Counter: $_counter'),
        ElevatedButton(
          onPressed: () {
            setState(() {
              _counter++;
            });
          },
          child: Text('Increment'),
        ),
      ],
    );
  }
}
```

## Built-in State Management

### StatefulWidget and setState
```dart
class CounterWidget extends StatefulWidget {
  @override
  _CounterWidgetState createState() => _CounterWidgetState();
}

class _CounterWidgetState extends State<CounterWidget> {
  int _counter = 0;
  bool _isLoading = false;
  
  void _incrementCounter() async {
    setState(() {
      _isLoading = true;
    });
    
    // Simulate async operation
    await Future.delayed(Duration(seconds: 1));
    
    setState(() {
      _counter++;
      _isLoading = false;
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text('Counter: $_counter'),
        if (_isLoading)
          CircularProgressIndicator()
        else
          ElevatedButton(
            onPressed: _incrementCounter,
            child: Text('Increment'),
          ),
      ],
    );
  }
}
```

### InheritedWidget
```dart
class CounterInheritedWidget extends InheritedWidget {
  final int counter;
  final VoidCallback increment;
  
  const CounterInheritedWidget({
    Key? key,
    required this.counter,
    required this.increment,
    required Widget child,
  }) : super(key: key, child: child);
  
  static CounterInheritedWidget? of(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<CounterInheritedWidget>();
  }
  
  @override
  bool updateShouldNotify(CounterInheritedWidget oldWidget) {
    return oldWidget.counter != counter;
  }
}

// Usage
class CounterDisplay extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final counterWidget = CounterInheritedWidget.of(context)!;
    
    return Column(
      children: [
        Text('Counter: ${counterWidget.counter}'),
        ElevatedButton(
          onPressed: counterWidget.increment,
          child: Text('Increment'),
        ),
      ],
    );
  }
}
```

### ValueNotifier and ValueListenableBuilder
```dart
class CounterNotifier extends ValueNotifier<int> {
  CounterNotifier() : super(0);
  
  void increment() {
    value = value + 1;
  }
  
  void decrement() {
    value = value - 1;
  }
  
  void reset() {
    value = 0;
  }
}

class CounterPage extends StatefulWidget {
  @override
  _CounterPageState createState() => _CounterPageState();
}

class _CounterPageState extends State<CounterPage> {
  final CounterNotifier _counter = CounterNotifier();
  
  @override
  void dispose() {
    _counter.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('ValueNotifier Counter')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ValueListenableBuilder<int>(
              valueListenable: _counter,
              builder: (context, value, child) {
                return Text(
                  'Counter: $value',
                  style: Theme.of(context).textTheme.headlineMedium,
                );
              },
            ),
            SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: _counter.decrement,
                  child: Text('-'),
                ),
                SizedBox(width: 20),
                ElevatedButton(
                  onPressed: _counter.increment,
                  child: Text('+'),
                ),
                SizedBox(width: 20),
                ElevatedButton(
                  onPressed: _counter.reset,
                  child: Text('Reset'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
```

## Provider Pattern

### Basic Provider Setup
```dart
// pubspec.yaml
dependencies:
  provider: ^6.0.0

// Counter model
class CounterModel extends ChangeNotifier {
  int _counter = 0;
  
  int get counter => _counter;
  
  void increment() {
    _counter++;
    notifyListeners();
  }
  
  void decrement() {
    _counter--;
    notifyListeners();
  }
  
  void reset() {
    _counter = 0;
    notifyListeners();
  }
}

// App setup
void main() {
  runApp(
    ChangeNotifierProvider(
      create: (context) => CounterModel(),
      child: MyApp(),
    ),
  );
}

// Using Provider
class CounterPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Provider Counter')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Consumer<CounterModel>(
              builder: (context, counter, child) {
                return Text(
                  'Counter: ${counter.counter}',
                  style: Theme.of(context).textTheme.headlineMedium,
                );
              },
            ),
            SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: () {
                    context.read<CounterModel>().decrement();
                  },
                  child: Text('-'),
                ),
                SizedBox(width: 20),
                ElevatedButton(
                  onPressed: () {
                    context.read<CounterModel>().increment();
                  },
                  child: Text('+'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
```

### Multiple Providers
```dart
// User model
class UserModel extends ChangeNotifier {
  User? _user;
  bool _isLoading = false;
  
  User? get user => _user;
  bool get isLoading => _isLoading;
  bool get isLoggedIn => _user != null;
  
  Future<void> login(String email, String password) async {
    _isLoading = true;
    notifyListeners();
    
    try {
      _user = await AuthService.login(email, password);
    } catch (e) {
      throw e;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  void logout() {
    _user = null;
    notifyListeners();
  }
}

// Shopping cart model
class CartModel extends ChangeNotifier {
  final List<CartItem> _items = [];
  
  List<CartItem> get items => List.unmodifiable(_items);
  int get itemCount => _items.length;
  double get totalPrice => _items.fold(0, (sum, item) => sum + item.price);
  
  void addItem(Product product) {
    _items.add(CartItem(product: product));
    notifyListeners();
  }
  
  void removeItem(String productId) {
    _items.removeWhere((item) => item.product.id == productId);
    notifyListeners();
  }
  
  void clear() {
    _items.clear();
    notifyListeners();
  }
}

// App setup with multiple providers
void main() {
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => UserModel()),
        ChangeNotifierProvider(create: (context) => CartModel()),
        ChangeNotifierProvider(create: (context) => ThemeModel()),
      ],
      child: MyApp(),
    ),
  );
}
```

### Provider with Future/Stream
```dart
// API service
class ApiService {
  static Future<List<Product>> fetchProducts() async {
    // Simulate API call
    await Future.delayed(Duration(seconds: 2));
    return [
      Product(id: '1', name: 'Product 1', price: 10.0),
      Product(id: '2', name: 'Product 2', price: 20.0),
    ];
  }
}

// Products provider
class ProductsModel extends ChangeNotifier {
  List<Product> _products = [];
  bool _isLoading = false;
  String? _error;
  
  List<Product> get products => List.unmodifiable(_products);
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  Future<void> fetchProducts() async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      _products = await ApiService.fetchProducts();
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}

// Using in widget
class ProductListPage extends StatefulWidget {
  @override
  _ProductListPageState createState() => _ProductListPageState();
}

class _ProductListPageState extends State<ProductListPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ProductsModel>().fetchProducts();
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Products')),
      body: Consumer<ProductsModel>(
        builder: (context, productsModel, child) {
          if (productsModel.isLoading) {
            return Center(child: CircularProgressIndicator());
          }
          
          if (productsModel.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('Error: ${productsModel.error}'),
                  ElevatedButton(
                    onPressed: () => productsModel.fetchProducts(),
                    child: Text('Retry'),
                  ),
                ],
              ),
            );
          }
          
          return ListView.builder(
            itemCount: productsModel.products.length,
            itemBuilder: (context, index) {
              final product = productsModel.products[index];
              return ListTile(
                title: Text(product.name),
                subtitle: Text('\$${product.price}'),
                trailing: IconButton(
                  icon: Icon(Icons.add_shopping_cart),
                  onPressed: () {
                    context.read<CartModel>().addItem(product);
                  },
                ),
              );
            },
          );
        },
      ),
    );
  }
}
```

## Bloc Pattern

### Basic Bloc Setup
```dart
// pubspec.yaml
dependencies:
  flutter_bloc: ^8.0.0

// Events
abstract class CounterEvent {}

class CounterIncremented extends CounterEvent {}
class CounterDecremented extends CounterEvent {}
class CounterReset extends CounterEvent {}

// States
abstract class CounterState {
  final int counter;
  const CounterState(this.counter);
}

class CounterInitial extends CounterState {
  const CounterInitial() : super(0);
}

class CounterValue extends CounterState {
  const CounterValue(int counter) : super(counter);
}

// Bloc
class CounterBloc extends Bloc<CounterEvent, CounterState> {
  CounterBloc() : super(CounterInitial()) {
    on<CounterIncremented>((event, emit) {
      emit(CounterValue(state.counter + 1));
    });
    
    on<CounterDecremented>((event, emit) {
      emit(CounterValue(state.counter - 1));
    });
    
    on<CounterReset>((event, emit) {
      emit(CounterInitial());
    });
  }
}

// App setup
void main() {
  runApp(
    BlocProvider(
      create: (context) => CounterBloc(),
      child: MyApp(),
    ),
  );
}

// Using Bloc
class CounterPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Bloc Counter')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            BlocBuilder<CounterBloc, CounterState>(
              builder: (context, state) {
                return Text(
                  'Counter: ${state.counter}',
                  style: Theme.of(context).textTheme.headlineMedium,
                );
              },
            ),
            SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: () {
                    context.read<CounterBloc>().add(CounterDecremented());
                  },
                  child: Text('-'),
                ),
                SizedBox(width: 20),
                ElevatedButton(
                  onPressed: () {
                    context.read<CounterBloc>().add(CounterIncremented());
                  },
                  child: Text('+'),
                ),
                SizedBox(width: 20),
                ElevatedButton(
                  onPressed: () {
                    context.read<CounterBloc>().add(CounterReset());
                  },
                  child: Text('Reset'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
```

### Bloc with Repository Pattern
```dart
// Repository
class UserRepository {
  Future<User> login(String email, String password) async {
    // API call
    await Future.delayed(Duration(seconds: 2));
    if (email == '<EMAIL>' && password == 'password') {
      return User(id: '1', email: email, name: 'John Doe');
    } else {
      throw Exception('Invalid credentials');
    }
  }
  
  Future<void> logout() async {
    await Future.delayed(Duration(seconds: 1));
  }
}

// Auth Events
abstract class AuthEvent {}

class AuthLoginRequested extends AuthEvent {
  final String email;
  final String password;
  
  AuthLoginRequested({required this.email, required this.password});
}

class AuthLogoutRequested extends AuthEvent {}

// Auth States
abstract class AuthState {}

class AuthInitial extends AuthState {}

class AuthLoading extends AuthState {}

class AuthAuthenticated extends AuthState {
  final User user;
  
  AuthAuthenticated({required this.user});
}

class AuthUnauthenticated extends AuthState {}

class AuthError extends AuthState {
  final String message;
  
  AuthError({required this.message});
}

// Auth Bloc
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final UserRepository _userRepository;
  
  AuthBloc({required UserRepository userRepository})
      : _userRepository = userRepository,
        super(AuthInitial()) {
    on<AuthLoginRequested>(_onLoginRequested);
    on<AuthLogoutRequested>(_onLogoutRequested);
  }
  
  Future<void> _onLoginRequested(
    AuthLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());
    
    try {
      final user = await _userRepository.login(event.email, event.password);
      emit(AuthAuthenticated(user: user));
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }
  
  Future<void> _onLogoutRequested(
    AuthLogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());
    
    try {
      await _userRepository.logout();
      emit(AuthUnauthenticated());
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }
}
```

## Riverpod

### Basic Riverpod Setup
```dart
// pubspec.yaml
dependencies:
  flutter_riverpod: ^2.0.0

// Providers
final counterProvider = StateNotifierProvider<CounterNotifier, int>((ref) {
  return CounterNotifier();
});

class CounterNotifier extends StateNotifier<int> {
  CounterNotifier() : super(0);
  
  void increment() => state++;
  void decrement() => state--;
  void reset() => state = 0;
}

// App setup
void main() {
  runApp(
    ProviderScope(
      child: MyApp(),
    ),
  );
}

// Using Riverpod
class CounterPage extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final counter = ref.watch(counterProvider);
    
    return Scaffold(
      appBar: AppBar(title: Text('Riverpod Counter')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Counter: $counter',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: () {
                    ref.read(counterProvider.notifier).decrement();
                  },
                  child: Text('-'),
                ),
                SizedBox(width: 20),
                ElevatedButton(
                  onPressed: () {
                    ref.read(counterProvider.notifier).increment();
                  },
                  child: Text('+'),
                ),
                SizedBox(width: 20),
                ElevatedButton(
                  onPressed: () {
                    ref.read(counterProvider.notifier).reset();
                  },
                  child: Text('Reset'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
```

### Riverpod with Async Data
```dart
// API provider
final apiServiceProvider = Provider<ApiService>((ref) {
  return ApiService();
});

// Products provider
final productsProvider = FutureProvider<List<Product>>((ref) async {
  final apiService = ref.read(apiServiceProvider);
  return apiService.fetchProducts();
});

// Filter provider
final searchQueryProvider = StateProvider<String>((ref) => '');

// Filtered products provider
final filteredProductsProvider = Provider<AsyncValue<List<Product>>>((ref) {
  final productsAsync = ref.watch(productsProvider);
  final searchQuery = ref.watch(searchQueryProvider);
  
  return productsAsync.when(
    data: (products) {
      if (searchQuery.isEmpty) {
        return AsyncValue.data(products);
      } else {
        final filtered = products
            .where((product) =>
                product.name.toLowerCase().contains(searchQuery.toLowerCase()))
            .toList();
        return AsyncValue.data(filtered);
      }
    },
    loading: () => AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

// Product list widget
class ProductListPage extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filteredProductsAsync = ref.watch(filteredProductsProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: Text('Products'),
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(60),
          child: Padding(
            padding: EdgeInsets.all(8),
            child: TextField(
              onChanged: (value) {
                ref.read(searchQueryProvider.notifier).state = value;
              },
              decoration: InputDecoration(
                hintText: 'Search products...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
            ),
          ),
        ),
      ),
      body: filteredProductsAsync.when(
        data: (products) {
          if (products.isEmpty) {
            return Center(child: Text('No products found'));
          }
          
          return ListView.builder(
            itemCount: products.length,
            itemBuilder: (context, index) {
              final product = products[index];
              return ListTile(
                title: Text(product.name),
                subtitle: Text('\$${product.price}'),
                trailing: IconButton(
                  icon: Icon(Icons.add_shopping_cart),
                  onPressed: () {
                    // Add to cart logic
                  },
                ),
              );
            },
          );
        },
        loading: () => Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text('Error: $error'),
              ElevatedButton(
                onPressed: () {
                  ref.invalidate(productsProvider);
                },
                child: Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
```

## GetX

### Basic GetX Setup
```dart
// pubspec.yaml
dependencies:
  get: ^4.6.0

// Controller
class CounterController extends GetxController {
  var counter = 0.obs;
  
  void increment() => counter++;
  void decrement() => counter--;
  void reset() => counter.value = 0;
}

// App setup
void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'GetX Demo',
      home: CounterPage(),
    );
  }
}

// Using GetX
class CounterPage extends StatelessWidget {
  final CounterController controller = Get.put(CounterController());
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('GetX Counter')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Obx(() => Text(
              'Counter: ${controller.counter}',
              style: Theme.of(context).textTheme.headlineMedium,
            )),
            SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: controller.decrement,
                  child: Text('-'),
                ),
                SizedBox(width: 20),
                ElevatedButton(
                  onPressed: controller.increment,
                  child: Text('+'),
                ),
                SizedBox(width: 20),
                ElevatedButton(
                  onPressed: controller.reset,
                  child: Text('Reset'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
```

## State Management Comparison

| Solution | Complexity | Performance | Learning Curve | Community |
|----------|------------|-------------|----------------|-----------|
| setState | Low | Good | Easy | Built-in |
| Provider | Medium | Good | Medium | Large |
| Bloc | High | Excellent | Hard | Large |
| Riverpod | Medium | Excellent | Medium | Growing |
| GetX | Low | Good | Easy | Medium |
| Redux | High | Good | Hard | Medium |

### When to Use Each

**setState**
- Simple local state
- Small widgets
- Learning Flutter

**Provider**
- Medium complexity apps
- Gradual migration from setState
- When you need InheritedWidget benefits

**Bloc**
- Large, complex applications
- Team development
- Strict architecture requirements
- Testability is crucial

**Riverpod**
- Modern approach to Provider
- Compile-time safety
- Better testing support

**GetX**
- Rapid prototyping
- Small to medium apps
- Minimal boilerplate

## React Native vs Flutter State Management

### State Comparison

| React Native | Flutter | Notes |
|--------------|---------|-------|
| `useState` | `setState` | Local state management |
| Context API | Provider/InheritedWidget | Global state sharing |
| Redux | flutter_redux/Bloc | Predictable state container |
| MobX | Provider/GetX | Reactive state management |
| Zustand | Riverpod | Modern state solution |

### Migration Examples

#### React Native useState
```javascript
// React Native
import React, { useState } from 'react';

function Counter() {
  const [count, setCount] = useState(0);
  
  return (
    <View>
      <Text>Count: {count}</Text>
      <Button
        title="Increment"
        onPress={() => setCount(count + 1)}
      />
    </View>
  );
}
```

#### Flutter setState
```dart
// Flutter
class Counter extends StatefulWidget {
  @override
  _CounterState createState() => _CounterState();
}

class _CounterState extends State<Counter> {
  int count = 0;
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text('Count: $count'),
        ElevatedButton(
          onPressed: () {
            setState(() {
              count++;
            });
          },
          child: Text('Increment'),
        ),
      ],
    );
  }
}
```

## Best Practices

### General Guidelines
1. **Start simple** with setState, evolve as needed
2. **Separate business logic** from UI
3. **Use immutable state** when possible
4. **Handle loading and error states** properly
5. **Test your state management** thoroughly

### Performance Tips
1. **Minimize unnecessary rebuilds**
2. **Use const constructors** where possible
3. **Implement proper equality** for custom classes
4. **Dispose resources** properly
5. **Use lazy loading** for expensive operations

### Architecture Recommendations
1. **Repository pattern** for data access
2. **Dependency injection** for testability
3. **Clear separation of concerns**
4. **Consistent error handling**
5. **Proper state persistence** when needed
