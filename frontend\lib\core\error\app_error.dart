import 'package:flutter/foundation.dart';

/// Base class for all application errors
abstract class AppError implements Exception {
  const AppError({
    required this.message,
    this.code,
    this.details,
    this.stackTrace,
  });

  final String message;
  final String? code;
  final Map<String, dynamic>? details;
  final StackTrace? stackTrace;

  @override
  String toString() {
    return 'AppError(message: $message, code: $code, details: $details)';
  }
}

/// Network related errors
class NetworkError extends AppError {
  const NetworkError({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
    this.statusCode,
  });

  final int? statusCode;

  factory NetworkError.noConnection() {
    return const NetworkError(
      message: 'No internet connection',
      code: 'NO_CONNECTION',
    );
  }

  factory NetworkError.timeout() {
    return const NetworkError(
      message: 'Request timeout',
      code: 'TIMEOUT',
    );
  }

  factory NetworkError.serverError(int statusCode, String message) {
    return NetworkError(
      message: message,
      code: 'SERVER_ERROR',
      statusCode: statusCode,
    );
  }
}

/// Authentication related errors
class AuthError extends AppError {
  const AuthError({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
  });

  factory AuthError.invalidCredentials() {
    return const AuthError(
      message: 'Invalid email or password',
      code: 'INVALID_CREDENTIALS',
    );
  }

  factory AuthError.userNotFound() {
    return const AuthError(
      message: 'User not found',
      code: 'USER_NOT_FOUND',
    );
  }

  factory AuthError.emailAlreadyExists() {
    return const AuthError(
      message: 'Email already exists',
      code: 'EMAIL_EXISTS',
    );
  }

  factory AuthError.tokenExpired() {
    return const AuthError(
      message: 'Session expired. Please login again',
      code: 'TOKEN_EXPIRED',
    );
  }
}

/// Validation related errors
class ValidationError extends AppError {
  const ValidationError({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
    this.field,
  });

  final String? field;

  factory ValidationError.required(String field) {
    return ValidationError(
      message: '$field is required',
      code: 'REQUIRED',
      field: field,
    );
  }

  factory ValidationError.invalidFormat(String field) {
    return ValidationError(
      message: 'Invalid $field format',
      code: 'INVALID_FORMAT',
      field: field,
    );
  }
}

/// Storage related errors
class StorageError extends AppError {
  const StorageError({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
  });

  factory StorageError.notFound(String key) {
    return StorageError(
      message: 'Data not found for key: $key',
      code: 'NOT_FOUND',
    );
  }

  factory StorageError.writeError() {
    return const StorageError(
      message: 'Failed to write data to storage',
      code: 'WRITE_ERROR',
    );
  }
}

/// WebSocket related errors
class WebSocketError extends AppError {
  const WebSocketError({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
  });

  factory WebSocketError.connectionFailed() {
    return const WebSocketError(
      message: 'Failed to connect to WebSocket',
      code: 'CONNECTION_FAILED',
    );
  }

  factory WebSocketError.connectionLost() {
    return const WebSocketError(
      message: 'WebSocket connection lost',
      code: 'CONNECTION_LOST',
    );
  }
}

/// Unknown or unexpected errors
class UnknownError extends AppError {
  const UnknownError({
    required super.message,
    super.code = 'UNKNOWN',
    super.details,
    super.stackTrace,
  });

  factory UnknownError.fromException(Exception exception, [StackTrace? stackTrace]) {
    return UnknownError(
      message: exception.toString(),
      details: {'exception': exception.runtimeType.toString()},
      stackTrace: stackTrace,
    );
  }
}

/// Error handler utility
class ErrorHandler {
  static void logError(AppError error) {
    if (kDebugMode) {
      debugPrint('AppError: ${error.toString()}');
      if (error.stackTrace != null) {
        debugPrint('StackTrace: ${error.stackTrace}');
      }
    }
    
    // In production, you might want to send errors to a crash reporting service
    // like Firebase Crashlytics, Sentry, etc.
  }

  static String getDisplayMessage(AppError error) {
    switch (error.runtimeType) {
      case NetworkError:
        return _getNetworkErrorMessage(error as NetworkError);
      case AuthError:
        return error.message;
      case ValidationError:
        return error.message;
      case StorageError:
        return 'Storage error occurred. Please try again.';
      case WebSocketError:
        return 'Connection error. Please check your internet connection.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }

  static String _getNetworkErrorMessage(NetworkError error) {
    if (error.statusCode != null) {
      switch (error.statusCode!) {
        case 400:
          return 'Bad request. Please check your input.';
        case 401:
          return 'Unauthorized. Please login again.';
        case 403:
          return 'Access denied.';
        case 404:
          return 'Resource not found.';
        case 500:
          return 'Server error. Please try again later.';
        default:
          return error.message;
      }
    }
    return error.message;
  }
}
