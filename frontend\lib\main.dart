import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Core imports
import 'core/core.dart';
// Shared imports
import 'shared/shared.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize application configuration
  await AppConfig.initialize();

  runApp(
    const ProviderScope(
      child: QuestApp(),
    ),
  );
}

class QuestApp extends ConsumerStatefulWidget {
  const QuestApp({super.key});

  @override
  ConsumerState<QuestApp> createState() => _QuestAppState();
}

class _QuestAppState extends ConsumerState<QuestApp> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Initialize WebSocket connection
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(autoConnectWebSocketProvider);
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangePlatformBrightness() {
    super.didChangePlatformBrightness();
    // Update theme when system brightness changes
    final brightness = WidgetsBinding.instance.platformDispatcher.platformBrightness;
    ref.read(themeProvider.notifier).updateSystemBrightness(brightness);
  }

  @override
  Widget build(BuildContext context) {
    final router = ref.watch(routerProvider);
    final themeMode = ref.watch(themeModeProvider);

    // Initialize real-time data sync
    ref.watch(realTimeDataSyncProvider);

    return MaterialApp.router(
      title: AppConfig.appName,
      debugShowCheckedModeBanner: false,

      // Enhanced theme configuration with provider
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeMode,

      // Router configuration
      routerConfig: router,

      // Localization (can be expanded later)
      locale: const Locale('en', 'US'),

      // Builder for global responsive handling and WebSocket status
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            // Allow text scaling for accessibility, but limit extreme scaling
            textScaler: TextScaler.linear(
              MediaQuery.of(context).textScaler.scale(1.0).clamp(0.8, 1.3),
            ),
          ),
          child: Stack(
            children: [
              child ?? const SizedBox.shrink(),
              // Global WebSocket status overlay
              const FloatingWebSocketStatus(),
            ],
          ),
        );
      },
    );
  }
}
