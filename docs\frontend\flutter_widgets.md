# Flutter Widgets - Complete Guide

## Introduction

Widgets are the building blocks of Flutter applications. Everything in Flutter is a widget - from basic elements like text and buttons to complex layouts and entire screens.

## Widget Categories

### Design Systems

Flutter provides two main design systems:

#### Material Design
Material Design widgets follow Google's design guidelines:
```dart
// Material App structure
MaterialApp(
  title: 'My App',
  theme: ThemeData(
    primarySwatch: Colors.blue,
    visualDensity: VisualDensity.adaptivePlatformDensity,
  ),
  home: MyHomePage(),
)

// Common Material widgets
AppBar(title: Text('Title'))
FloatingActionButton(onPressed: () {}, child: Icon(Icons.add))
Card(child: ListTile(title: Text('Card Content')))
Snackbar(content: Text('Message'))
```

#### Cupertino (iOS Style)
Cupertino widgets provide native iOS look and feel:
```dart
// Cupertino App structure
CupertinoApp(
  title: 'My App',
  theme: CupertinoThemeData(
    primaryColor: CupertinoColors.systemBlue,
  ),
  home: MyHomePage(),
)

// Common Cupertino widgets
CupertinoNavigationBar(middle: Text('Title'))
CupertinoButton(child: Text('Button'), onPressed: () {})
CupertinoAlertDialog(title: Text('Alert'))
CupertinoSwitch(value: true, onChanged: (value) {})
```

## Core Widget Types

### StatelessWidget
Immutable widgets that don't change over time:
```dart
class GreetingWidget extends StatelessWidget {
  final String name;
  
  const GreetingWidget({Key? key, required this.name}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Text('Hello, $name!');
  }
}
```

### StatefulWidget
Widgets that can change state during their lifetime:
```dart
class CounterWidget extends StatefulWidget {
  @override
  _CounterWidgetState createState() => _CounterWidgetState();
}

class _CounterWidgetState extends State<CounterWidget> {
  int _counter = 0;
  
  void _incrementCounter() {
    setState(() {
      _counter++;
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text('Count: $_counter'),
        ElevatedButton(
          onPressed: _incrementCounter,
          child: Text('Increment'),
        ),
      ],
    );
  }
}
```

## Layout Widgets

### Container
The most commonly used widget for styling and positioning:
```dart
Container(
  width: 200,
  height: 100,
  padding: EdgeInsets.all(16),
  margin: EdgeInsets.symmetric(vertical: 8),
  decoration: BoxDecoration(
    color: Colors.blue,
    borderRadius: BorderRadius.circular(8),
    boxShadow: [
      BoxShadow(
        color: Colors.grey.withOpacity(0.3),
        spreadRadius: 2,
        blurRadius: 5,
        offset: Offset(0, 3),
      ),
    ],
  ),
  child: Text('Styled Container'),
)
```

### Row and Column
For horizontal and vertical layouts:
```dart
// Horizontal layout
Row(
  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
  crossAxisAlignment: CrossAxisAlignment.center,
  children: [
    Icon(Icons.star),
    Text('Rating'),
    Text('4.5'),
  ],
)

// Vertical layout
Column(
  mainAxisAlignment: MainAxisAlignment.center,
  crossAxisAlignment: CrossAxisAlignment.start,
  children: [
    Text('Title'),
    Text('Subtitle'),
    ElevatedButton(
      onPressed: () {},
      child: Text('Action'),
    ),
  ],
)
```

### Stack
For overlapping widgets:
```dart
Stack(
  alignment: Alignment.center,
  children: [
    CircleAvatar(
      radius: 60,
      backgroundImage: NetworkImage('https://example.com/avatar.jpg'),
    ),
    Positioned(
      bottom: 0,
      right: 0,
      child: CircleAvatar(
        radius: 20,
        backgroundColor: Colors.green,
        child: Icon(Icons.check, color: Colors.white),
      ),
    ),
  ],
)
```

### Expanded and Flexible
For responsive layouts:
```dart
Row(
  children: [
    Expanded(
      flex: 2,
      child: Container(color: Colors.red, height: 100),
    ),
    Expanded(
      flex: 1,
      child: Container(color: Colors.blue, height: 100),
    ),
    Flexible(
      child: Container(color: Colors.green, height: 100),
    ),
  ],
)
```

## Input Widgets

### TextField
For text input:
```dart
class TextInputExample extends StatefulWidget {
  @override
  _TextInputExampleState createState() => _TextInputExampleState();
}

class _TextInputExampleState extends State<TextInputExample> {
  final TextEditingController _controller = TextEditingController();
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TextField(
          controller: _controller,
          decoration: InputDecoration(
            labelText: 'Enter your name',
            hintText: 'Type here...',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.person),
          ),
          onChanged: (value) {
            print('Input changed: $value');
          },
        ),
        ElevatedButton(
          onPressed: () {
            print('Submitted: ${_controller.text}');
          },
          child: Text('Submit'),
        ),
      ],
    );
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
```

### Form Validation
```dart
class ValidatedForm extends StatefulWidget {
  @override
  _ValidatedFormState createState() => _ValidatedFormState();
}

class _ValidatedFormState extends State<ValidatedForm> {
  final _formKey = GlobalKey<FormState>();
  String? _email;
  String? _password;
  
  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          TextFormField(
            decoration: InputDecoration(labelText: 'Email'),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your email';
              }
              if (!value.contains('@')) {
                return 'Please enter a valid email';
              }
              return null;
            },
            onSaved: (value) => _email = value,
          ),
          TextFormField(
            decoration: InputDecoration(labelText: 'Password'),
            obscureText: true,
            validator: (value) {
              if (value == null || value.length < 6) {
                return 'Password must be at least 6 characters';
              }
              return null;
            },
            onSaved: (value) => _password = value,
          ),
          ElevatedButton(
            onPressed: () {
              if (_formKey.currentState!.validate()) {
                _formKey.currentState!.save();
                print('Email: $_email, Password: $_password');
              }
            },
            child: Text('Submit'),
          ),
        ],
      ),
    );
  }
}
```

## Interactive Widgets

### Buttons
```dart
// Elevated Button (Primary)
ElevatedButton(
  onPressed: () => print('Elevated button pressed'),
  child: Text('Elevated Button'),
  style: ElevatedButton.styleFrom(
    primary: Colors.blue,
    onPrimary: Colors.white,
    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
  ),
)

// Text Button (Secondary)
TextButton(
  onPressed: () => print('Text button pressed'),
  child: Text('Text Button'),
)

// Outlined Button
OutlinedButton(
  onPressed: () => print('Outlined button pressed'),
  child: Text('Outlined Button'),
)

// Icon Button
IconButton(
  icon: Icon(Icons.favorite),
  onPressed: () => print('Icon button pressed'),
)

// Floating Action Button
FloatingActionButton(
  onPressed: () => print('FAB pressed'),
  child: Icon(Icons.add),
)
```

### Checkboxes and Switches
```dart
class CheckboxExample extends StatefulWidget {
  @override
  _CheckboxExampleState createState() => _CheckboxExampleState();
}

class _CheckboxExampleState extends State<CheckboxExample> {
  bool _isChecked = false;
  bool _switchValue = false;
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CheckboxListTile(
          title: Text('Agree to terms'),
          value: _isChecked,
          onChanged: (value) {
            setState(() {
              _isChecked = value!;
            });
          },
        ),
        SwitchListTile(
          title: Text('Enable notifications'),
          value: _switchValue,
          onChanged: (value) {
            setState(() {
              _switchValue = value;
            });
          },
        ),
      ],
    );
  }
}
```

## Scrollable Widgets

### ListView
For scrollable lists:
```dart
// Basic ListView
ListView(
  children: [
    ListTile(
      leading: Icon(Icons.map),
      title: Text('Map'),
      subtitle: Text('Navigate to places'),
      trailing: Icon(Icons.arrow_forward_ios),
      onTap: () => print('Map tapped'),
    ),
    ListTile(
      leading: Icon(Icons.photo),
      title: Text('Photos'),
      subtitle: Text('View your photos'),
      trailing: Icon(Icons.arrow_forward_ios),
      onTap: () => print('Photos tapped'),
    ),
  ],
)

// ListView.builder for dynamic lists
ListView.builder(
  itemCount: items.length,
  itemBuilder: (context, index) {
    return ListTile(
      title: Text(items[index].title),
      subtitle: Text(items[index].subtitle),
      onTap: () => print('Item $index tapped'),
    );
  },
)

// ListView.separated for lists with separators
ListView.separated(
  itemCount: items.length,
  separatorBuilder: (context, index) => Divider(),
  itemBuilder: (context, index) {
    return ListTile(title: Text(items[index]));
  },
)
```

### GridView
For grid layouts:
```dart
// GridView.count - fixed number of columns
GridView.count(
  crossAxisCount: 2,
  crossAxisSpacing: 10,
  mainAxisSpacing: 10,
  children: List.generate(20, (index) {
    return Card(
      child: Center(
        child: Text('Item $index'),
      ),
    );
  }),
)

// GridView.builder for dynamic grids
GridView.builder(
  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
    crossAxisCount: 3,
    crossAxisSpacing: 4,
    mainAxisSpacing: 4,
  ),
  itemCount: photos.length,
  itemBuilder: (context, index) {
    return Image.network(photos[index].url, fit: BoxFit.cover);
  },
)
```

### SingleChildScrollView
For scrollable content:
```dart
SingleChildScrollView(
  child: Column(
    children: [
      Container(height: 200, color: Colors.red),
      Container(height: 200, color: Colors.blue),
      Container(height: 200, color: Colors.green),
      Container(height: 200, color: Colors.yellow),
    ],
  ),
)
```

## Media Widgets

### Image
```dart
// Asset images
Image.asset('assets/images/logo.png')

// Network images
Image.network(
  'https://example.com/image.jpg',
  fit: BoxFit.cover,
  width: 200,
  height: 150,
  loadingBuilder: (context, child, loadingProgress) {
    if (loadingProgress == null) return child;
    return CircularProgressIndicator();
  },
  errorBuilder: (context, error, stackTrace) {
    return Icon(Icons.error);
  },
)

// CircleAvatar for profile pictures
CircleAvatar(
  radius: 50,
  backgroundImage: NetworkImage('https://example.com/avatar.jpg'),
  backgroundColor: Colors.grey,
)
```

### Icon
```dart
// Material Icons
Icon(Icons.home, size: 24, color: Colors.blue)
Icon(Icons.favorite, color: Colors.red)

// Custom icons
ImageIcon(
  AssetImage('assets/icons/custom_icon.png'),
  size: 24,
  color: Colors.green,
)
```

## Gesture Detection

### GestureDetector
```dart
GestureDetector(
  onTap: () => print('Tapped'),
  onDoubleTap: () => print('Double tapped'),
  onLongPress: () => print('Long pressed'),
  onPanUpdate: (details) => print('Pan update: ${details.delta}'),
  child: Container(
    width: 100,
    height: 100,
    color: Colors.blue,
    child: Center(child: Text('Touch me')),
  ),
)
```

### InkWell
For material ripple effects:
```dart
InkWell(
  onTap: () => print('Ink well tapped'),
  borderRadius: BorderRadius.circular(8),
  child: Container(
    padding: EdgeInsets.all(16),
    child: Text('Tap for ripple effect'),
  ),
)
```

## Animation Widgets

### AnimatedContainer
```dart
class AnimatedExample extends StatefulWidget {
  @override
  _AnimatedExampleState createState() => _AnimatedExampleState();
}

class _AnimatedExampleState extends State<AnimatedExample> {
  bool _isExpanded = false;
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AnimatedContainer(
          duration: Duration(milliseconds: 300),
          width: _isExpanded ? 200 : 100,
          height: _isExpanded ? 200 : 100,
          color: _isExpanded ? Colors.blue : Colors.red,
          curve: Curves.easeInOut,
          child: Center(child: Text('Animated')),
        ),
        ElevatedButton(
          onPressed: () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
          },
          child: Text('Animate'),
        ),
      ],
    );
  }
}
```

### Hero Animation
For shared element transitions:
```dart
// First screen
Hero(
  tag: 'hero-image',
  child: Image.network('https://example.com/image.jpg'),
)

// Second screen
Hero(
  tag: 'hero-image', // Same tag
  child: Image.network('https://example.com/image.jpg'),
)
```

## React Native to Flutter Widget Mapping

| React Native | Flutter | Description |
|--------------|---------|-------------|
| View | Container, Column, Row | Layout container |
| Text | Text | Text display |
| Image | Image | Image display |
| TouchableOpacity | GestureDetector, InkWell | Touch handling |
| Button | ElevatedButton, TextButton | Button components |
| TextInput | TextField | Text input |
| ScrollView | SingleChildScrollView | Scrollable content |
| FlatList | ListView | Scrollable list |
| SectionList | ListView with sections | Sectioned list |
| Modal | Dialog, BottomSheet | Modal presentation |
| Switch | Switch | Toggle switch |
| Slider | Slider | Range slider |
| ActivityIndicator | CircularProgressIndicator | Loading indicator |

## Best Practices

### Widget Organization
```dart
// Break complex widgets into smaller ones
class UserProfileCard extends StatelessWidget {
  final User user;
  
  const UserProfileCard({Key? key, required this.user}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            _buildAvatar(),
            _buildUserInfo(),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }
  
  Widget _buildAvatar() {
    return CircleAvatar(
      radius: 40,
      backgroundImage: NetworkImage(user.avatarUrl),
    );
  }
  
  Widget _buildUserInfo() {
    return Column(
      children: [
        Text(user.name, style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
        Text(user.email, style: TextStyle(color: Colors.grey)),
      ],
    );
  }
  
  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        ElevatedButton(onPressed: () {}, child: Text('Follow')),
        OutlinedButton(onPressed: () {}, child: Text('Message')),
      ],
    );
  }
}
```

### Performance Tips
1. Use `const` constructors when possible
2. Avoid rebuilding expensive widgets
3. Use `ListView.builder` for large lists
4. Implement proper `dispose()` methods
5. Use `AutomaticKeepAliveClientMixin` for tabs

### State Management
1. Use `setState` for simple local state
2. Consider Provider, Bloc, or Riverpod for complex state
3. Lift state up when multiple widgets need it
4. Use `ValueNotifier` for simple reactive values

---

*This guide covers the essential widgets needed to build Flutter applications. For more detailed information, refer to the [Flutter Widget Catalog](https://docs.flutter.dev/ui/widgets).*
