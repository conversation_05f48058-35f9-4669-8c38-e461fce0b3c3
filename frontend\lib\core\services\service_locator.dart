import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Services
import 'auth_service.dart';
import 'api_service.dart';
import 'quest_service.dart';
import 'achievement_service.dart';
import 'marketplace_service.dart';
import 'api_notification_service.dart';
import 'wallet_service.dart';
import 'user_service.dart';
import 'storage_service.dart';
import 'websocket_service.dart';
import 'notification_service.dart';

class ServiceLocator {
  static late Dio _dio;
  static late AuthService _authService;
  static late ApiService _apiService;
  static late QuestService _questService;
  static late AchievementService _achievementService;
  static late MarketplaceService _marketplaceService;
  static late ApiNotificationService _apiNotificationService;
  static late WalletService _walletService;
  static late UserService _userService;
  static late StorageService _storageService;
  static late WebSocketService _webSocketService;
  static late NotificationService _notificationService;

  static bool _isInitialized = false;

  static Future<void> setupServices() async {
    if (_isInitialized) return;

    // Storage
    _storageService = StorageService();

    // HTTP Client
    _dio = Dio();

    // Base configuration
    _dio.options.baseUrl = const String.fromEnvironment(
      'API_BASE_URL',
      defaultValue: 'http://localhost:8080/api',
    );

    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
    _dio.options.sendTimeout = const Duration(seconds: 30);

    // Request/Response interceptors
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // Add auth token if available
          final token = await StorageService.getAccessToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }

          // Add content type
          options.headers['Content-Type'] = 'application/json';
          options.headers['Accept'] = 'application/json';

          handler.next(options);
        },
        onResponse: (response, handler) {
          handler.next(response);
        },
        onError: (error, handler) async {
          // Handle 401 Unauthorized
          if (error.response?.statusCode == 401) {
            await StorageService.clearTokens();
            // Navigate to login screen
            // This should be handled by the app's navigation logic
          }
          handler.next(error);
        },
      ),
    );

    // Logging interceptor (only in debug mode)
    if (const bool.fromEnvironment('dart.vm.product') == false) {
      _dio.interceptors.add(
        LogInterceptor(
          requestBody: true,
          responseBody: true,
          requestHeader: true,
          responseHeader: false,
          error: true,
        ),
      );
    }

    // API Services
    _authService = AuthService();
    _apiService = ApiService(_authService);
    _questService = QuestService(_dio);
    _achievementService = AchievementService(_dio);
    _marketplaceService = MarketplaceService(_dio);
    _apiNotificationService = ApiNotificationService(_dio);
    _walletService = WalletService(_dio);
    _userService = UserService(_dio);

    // WebSocket Service
    _webSocketService = WebSocketService.instance;

    // Local Notification Service
    _notificationService = NotificationService.instance;

    _isInitialized = true;
  }

  static void reset() {
    _isInitialized = false;
  }

  // Convenience getters
  static AuthService get authService => _authService;
  static ApiService get apiService => _apiService;
  static QuestService get questService => _questService;
  static AchievementService get achievementService => _achievementService;
  static MarketplaceService get marketplaceService => _marketplaceService;
  static ApiNotificationService get apiNotificationService => _apiNotificationService;
  static WalletService get walletService => _walletService;
  static UserService get userService => _userService;
  static StorageService get storageService => _storageService;
  static WebSocketService get webSocketService => _webSocketService;
  static NotificationService get notificationService => _notificationService;
  static Dio get dio => _dio;
}

// Extension for easy access in widgets
extension ServiceLocatorExtension on Object {
  AuthService get authService => ServiceLocator.authService;
  ApiService get apiService => ServiceLocator.apiService;
  QuestService get questService => ServiceLocator.questService;
  AchievementService get achievementService => ServiceLocator.achievementService;
  MarketplaceService get marketplaceService => ServiceLocator.marketplaceService;
  ApiNotificationService get apiNotificationService => ServiceLocator.apiNotificationService;
  WalletService get walletService => ServiceLocator.walletService;
  UserService get userService => ServiceLocator.userService;
  StorageService get storageService => ServiceLocator.storageService;
  WebSocketService get webSocketService => ServiceLocator.webSocketService;
  NotificationService get notificationService => ServiceLocator.notificationService;
}

// Service providers
final authServiceProvider = Provider<AuthService>((ref) => ServiceLocator.authService);
final apiServiceProvider = Provider<ApiService>((ref) => ServiceLocator.apiService);
final questServiceProvider = Provider<QuestService>((ref) => ServiceLocator.questService);
final achievementServiceProvider = Provider<AchievementService>((ref) => ServiceLocator.achievementService);
final marketplaceServiceProvider = Provider<MarketplaceService>((ref) => ServiceLocator.marketplaceService);
final apiNotificationServiceProvider = Provider<ApiNotificationService>((ref) => ServiceLocator.apiNotificationService);
final walletServiceProvider = Provider<WalletService>((ref) => ServiceLocator.walletService);
final userServiceProvider = Provider<UserService>((ref) => ServiceLocator.userService);
final storageServiceProvider = Provider<StorageService>((ref) => ServiceLocator.storageService);
final webSocketServiceProvider = Provider<WebSocketService>((ref) => ServiceLocator.webSocketService);
final notificationServiceProvider = Provider<NotificationService>((ref) => ServiceLocator.notificationService);

// Dio provider
final dioProvider = Provider<Dio>((ref) => ServiceLocator.dio);

// Combined service provider for easy access
final servicesProvider = Provider<Type>((ref) => ServiceLocator);

// Environment configuration
class AppConfig {
  static const String apiBaseUrl = String.fromEnvironment(
    'API_BASE_URL',
    defaultValue: 'http://localhost:8080/api',
  );
  
  static const String wsBaseUrl = String.fromEnvironment(
    'WS_BASE_URL',
    defaultValue: 'ws://localhost:8080/ws',
  );
  
  static const bool isProduction = bool.fromEnvironment('dart.vm.product');
  static const bool enableLogging = !isProduction;
  
  static const Duration apiTimeout = Duration(seconds: 30);
  static const Duration wsReconnectDelay = Duration(seconds: 5);
  static const int wsMaxReconnectAttempts = 5;
}

// Error handling utilities
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final String? errorCode;
  final Map<String, dynamic>? details;

  const ApiException({
    required this.message,
    this.statusCode,
    this.errorCode,
    this.details,
  });

  @override
  String toString() {
    return 'ApiException: $message (Status: $statusCode, Code: $errorCode)';
  }
}

class NetworkException implements Exception {
  final String message;
  
  const NetworkException(this.message);
  
  @override
  String toString() => 'NetworkException: $message';
}

class ValidationException implements Exception {
  final String message;
  final Map<String, List<String>>? fieldErrors;
  
  const ValidationException(this.message, {this.fieldErrors});
  
  @override
  String toString() => 'ValidationException: $message';
}

// Service initialization helper
class ServiceInitializer {
  static Future<void> initialize() async {
    await ServiceLocator.setupServices();
    
    // Initialize WebSocket connection if user is authenticated
    final token = await StorageService.getAccessToken();
    
    if (token != null) {
      final webSocketService = ServiceLocator.webSocketService;
      await webSocketService.connect();
    }
  }
  
  static Future<void> cleanup() async {
    final webSocketService = ServiceLocator.webSocketService;
    await webSocketService.disconnect();
    
    ServiceLocator.reset();
  }
}
