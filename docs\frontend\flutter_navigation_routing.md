# Flutter Navigation and Routing Guide

## Table of Contents
1. [Navigation Fundamentals](#navigation-fundamentals)
2. [Basic Navigation](#basic-navigation)
3. [Named Routes](#named-routes)
4. [Advanced Routing](#advanced-routing)
5. [Navigation 2.0](#navigation-20)
6. [Tab Navigation](#tab-navigation)
7. [Drawer Navigation](#drawer-navigation)
8. [Deep Linking](#deep-linking)
9. [React Native vs Flutter Navigation](#react-native-vs-flutter-navigation)

## Navigation Fundamentals

### Navigation Stack Concept
Flutter uses a stack-based navigation system where:
- Routes are pushed onto a navigation stack managed by Navigator widget
- Users can pop routes to go back
- Each route is a widget (usually a Scaffold)
- Navigator manages a stack of Route objects and provides methods for managing the stack

### The Navigator Widget
The Navigator widget is the core of Flutter's navigation system. It:
- Maintains a stack of Route objects
- Provides methods like push(), pop(), pushReplacement()
- Handles platform-specific back button behavior
- Manages page transitions and animations

```dart
// Basic navigation flow
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => SecondPage()),
);

// Going back
Navigator.pop(context);

// Replace current route
Navigator.pushReplacement(
  context,
  MaterialPageRoute(builder: (context) => NewPage()),
);
```

### Route Types
- **MaterialPageRoute**: Material Design transitions with platform-appropriate animations
- **CupertinoPageRoute**: iOS-style transitions
- **PageRouteBuilder**: Custom transitions with full control over animation
- **ModalRoute**: Full-screen modal overlays

## Basic Navigation

### Simple Push/Pop Navigation
```dart
class FirstPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('First Page')),
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => SecondPage(),
              ),
            );
          },
          child: Text('Go to Second Page'),
        ),
      ),
    );
  }
}

class SecondPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Second Page')),
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: Text('Go Back'),
        ),
      ),
    );
  }
}
```

### Passing Data Between Routes
```dart
// Passing data to a route
class UserProfile extends StatelessWidget {
  final String userId;
  final String userName;
  
  const UserProfile({
    Key? key,
    required this.userId,
    required this.userName,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Profile: $userName')),
      body: Center(
        child: Text('User ID: $userId'),
      ),
    );
  }
}

// Navigation with data
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => UserProfile(
      userId: '123',
      userName: 'John Doe',
    ),
  ),
);
```

### Returning Data from Routes
```dart
// Route that returns data
class DataInputPage extends StatefulWidget {
  @override
  _DataInputPageState createState() => _DataInputPageState();
}

class _DataInputPageState extends State<DataInputPage> {
  final _controller = TextEditingController();
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Enter Data')),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            TextField(
              controller: _controller,
              decoration: InputDecoration(labelText: 'Enter text'),
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context, _controller.text);
              },
              child: Text('Save'),
            ),
          ],
        ),
      ),
    );
  }
}

// Receiving returned data
void _navigateToDataInput() async {
  final result = await Navigator.push(
    context,
    MaterialPageRoute(builder: (context) => DataInputPage()),
  );
  
  if (result != null) {
    print('Received: $result');
    // Handle the returned data
  }
}
```

## Named Routes

### Defining Named Routes
```dart
class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Navigation Demo',
      initialRoute: '/',
      routes: {
        '/': (context) => HomePage(),
        '/profile': (context) => ProfilePage(),
        '/settings': (context) => SettingsPage(),
        '/profile/edit': (context) => EditProfilePage(),
      },
    );
  }
}
```

### Navigating with Named Routes
```dart
// Simple navigation
Navigator.pushNamed(context, '/profile');

// Replace current route
Navigator.pushReplacementNamed(context, '/login');

// Clear stack and navigate
Navigator.pushNamedAndRemoveUntil(
  context,
  '/home',
  (route) => false, // Remove all previous routes
);
```

### Passing Arguments to Named Routes
```dart
// Define arguments class
class ProfileArguments {
  final String userId;
  final String userName;
  
  ProfileArguments({required this.userId, required this.userName});
}

// Navigation with arguments
Navigator.pushNamed(
  context,
  '/profile',
  arguments: ProfileArguments(
    userId: '123',
    userName: 'John Doe',
  ),
);

// Receiving arguments in the route
class ProfilePage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)!.settings.arguments as ProfileArguments;
    
    return Scaffold(
      appBar: AppBar(title: Text('Profile: ${args.userName}')),
      body: Center(
        child: Text('User ID: ${args.userId}'),
      ),
    );
  }
}
```

### Dynamic Route Generation
```dart
class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      onGenerateRoute: (settings) {
        switch (settings.name) {
          case '/':
            return MaterialPageRoute(builder: (context) => HomePage());
          case '/profile':
            final args = settings.arguments as ProfileArguments;
            return MaterialPageRoute(
              builder: (context) => ProfilePage(
                userId: args.userId,
                userName: args.userName,
              ),
            );
          case '/user':
            // Handle dynamic routes like /user/123
            final uri = Uri.parse(settings.name!);
            if (uri.pathSegments.length == 2) {
              final userId = uri.pathSegments[1];
              return MaterialPageRoute(
                builder: (context) => UserPage(userId: userId),
              );
            }
            break;
          default:
            return MaterialPageRoute(
              builder: (context) => NotFoundPage(),
            );
        }
        return null;
      },
    );
  }
}
```

## Advanced Routing

### Custom Page Transitions
```dart
class SlidePageRoute<T> extends PageRouteBuilder<T> {
  final Widget child;
  
  SlidePageRoute({required this.child})
      : super(
          pageBuilder: (context, animation, secondaryAnimation) => child,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(1.0, 0.0);
            const end = Offset.zero;
            final tween = Tween(begin: begin, end: end);
            final offsetAnimation = animation.drive(tween);
            
            return SlideTransition(
              position: offsetAnimation,
              child: child,
            );
          },
          transitionDuration: Duration(milliseconds: 300),
        );
}

// Usage
Navigator.push(
  context,
  SlidePageRoute(child: NextPage()),
);
```

### Modal Routes
```dart
// Full-screen modal
showDialog(
  context: context,
  builder: (context) => AlertDialog(
    title: Text('Confirm'),
    content: Text('Are you sure?'),
    actions: [
      TextButton(
        onPressed: () => Navigator.pop(context, false),
        child: Text('Cancel'),
      ),
      TextButton(
        onPressed: () => Navigator.pop(context, true),
        child: Text('Confirm'),
      ),
    ],
  ),
);

// Bottom sheet modal
showModalBottomSheet(
  context: context,
  isScrollControlled: true,
  shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
  ),
  builder: (context) => Container(
    height: MediaQuery.of(context).size.height * 0.7,
    padding: EdgeInsets.all(16),
    child: Column(
      children: [
        Text('Modal Content'),
        // ... more content
      ],
    ),
  ),
);
```

### Route Guards and Authentication
```dart
class AuthGuard {
  static bool isAuthenticated = false;
  
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case '/login':
        return MaterialPageRoute(builder: (_) => LoginPage());
      case '/profile':
        if (isAuthenticated) {
          return MaterialPageRoute(builder: (_) => ProfilePage());
        } else {
          return MaterialPageRoute(builder: (_) => LoginPage());
        }
      default:
        return MaterialPageRoute(builder: (_) => HomePage());
    }
  }
}

// Usage in MaterialApp
MaterialApp(
  onGenerateRoute: AuthGuard.generateRoute,
)
```

## Navigation 2.0

### Router Configuration
```dart
class AppRouter {
  static final _router = GoRouter(
    initialLocation: '/',
    routes: [
      GoRoute(
        path: '/',
        builder: (context, state) => HomePage(),
      ),
      GoRoute(
        path: '/profile/:userId',
        builder: (context, state) {
          final userId = state.pathParameters['userId']!;
          return ProfilePage(userId: userId);
        },
      ),
      GoRoute(
        path: '/settings',
        builder: (context, state) => SettingsPage(),
        routes: [
          GoRoute(
            path: 'account',
            builder: (context, state) => AccountSettingsPage(),
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => ErrorPage(error: state.error),
  );
  
  static GoRouter get router => _router;
}

// App setup
class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      routerConfig: AppRouter.router,
    );
  }
}
```

### Declarative Navigation
```dart
// Navigation with go_router
void navigateToProfile(String userId) {
  context.go('/profile/$userId');
}

void navigateToSettings() {
  context.push('/settings');
}

// Navigation with query parameters
void navigateWithQuery() {
  context.go('/search?query=flutter&category=mobile');
}
```

## Tab Navigation

### Bottom Tab Navigation
```dart
class TabNavigation extends StatefulWidget {
  @override
  _TabNavigationState createState() => _TabNavigationState();
}

class _TabNavigationState extends State<TabNavigation> {
  int _selectedIndex = 0;
  
  final List<Widget> _pages = [
    HomePage(),
    SearchPage(),
    ProfilePage(),
    SettingsPage(),
  ];
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _pages[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        items: [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.search),
            label: 'Search',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'Profile',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'Settings',
          ),
        ],
      ),
    );
  }
}
```

### Tab Bar (Top Tabs)
```dart
class TopTabNavigation extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        appBar: AppBar(
          title: Text('Tab Navigation'),
          bottom: TabBar(
            tabs: [
              Tab(icon: Icon(Icons.chat), text: 'Chats'),
              Tab(icon: Icon(Icons.contacts), text: 'Contacts'),
              Tab(icon: Icon(Icons.settings), text: 'Settings'),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            ChatsPage(),
            ContactsPage(),
            SettingsPage(),
          ],
        ),
      ),
    );
  }
}
```

### Persistent Tab Navigation with Nested Routes
```dart
class PersistentTabNavigation extends StatefulWidget {
  @override
  _PersistentTabNavigationState createState() => _PersistentTabNavigationState();
}

class _PersistentTabNavigationState extends State<PersistentTabNavigation> {
  int _selectedIndex = 0;
  final PageController _pageController = PageController();
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageView(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        children: [
          Navigator(
            onGenerateRoute: (settings) {
              return MaterialPageRoute(
                builder: (context) => HomeFlow(),
              );
            },
          ),
          Navigator(
            onGenerateRoute: (settings) {
              return MaterialPageRoute(
                builder: (context) => SearchFlow(),
              );
            },
          ),
          Navigator(
            onGenerateRoute: (settings) {
              return MaterialPageRoute(
                builder: (context) => ProfileFlow(),
              );
            },
          ),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: (index) {
          _pageController.animateToPage(
            index,
            duration: Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        },
        items: [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(icon: Icon(Icons.search), label: 'Search'),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
        ],
      ),
    );
  }
}
```

## Drawer Navigation

### Basic Drawer
```dart
class DrawerNavigation extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Drawer Navigation')),
      drawer: Drawer(
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            DrawerHeader(
              decoration: BoxDecoration(color: Colors.blue),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundImage: NetworkImage('https://example.com/avatar.jpg'),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'John Doe',
                    style: TextStyle(color: Colors.white, fontSize: 18),
                  ),
                  Text(
                    '<EMAIL>',
                    style: TextStyle(color: Colors.white70),
                  ),
                ],
              ),
            ),
            ListTile(
              leading: Icon(Icons.home),
              title: Text('Home'),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushReplacementNamed(context, '/home');
              },
            ),
            ListTile(
              leading: Icon(Icons.person),
              title: Text('Profile'),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/profile');
              },
            ),
            ListTile(
              leading: Icon(Icons.settings),
              title: Text('Settings'),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/settings');
              },
            ),
            Divider(),
            ListTile(
              leading: Icon(Icons.logout),
              title: Text('Logout'),
              onTap: () {
                Navigator.pop(context);
                _handleLogout(context);
              },
            ),
          ],
        ),
      ),
      body: Center(child: Text('Main Content')),
    );
  }
  
  void _handleLogout(BuildContext context) {
    // Handle logout logic
    Navigator.pushNamedAndRemoveUntil(
      context,
      '/login',
      (route) => false,
    );
  }
}
```

## Deep Linking

### URL-based Navigation
```dart
// go_router with deep linking
final router = GoRouter(
  routes: [
    GoRoute(
      path: '/',
      builder: (context, state) => HomePage(),
    ),
    GoRoute(
      path: '/product/:id',
      builder: (context, state) {
        final productId = state.pathParameters['id']!;
        final category = state.uri.queryParameters['category'];
        return ProductPage(
          productId: productId,
          category: category,
        );
      },
    ),
    GoRoute(
      path: '/user/:userId/orders',
      builder: (context, state) {
        final userId = state.pathParameters['userId']!;
        return OrdersPage(userId: userId);
      },
    ),
  ],
);

// Handle incoming URLs
void handleIncomingUrl(String url) {
  final uri = Uri.parse(url);
  router.go(uri.path + (uri.query.isNotEmpty ? '?${uri.query}' : ''));
}
```

### Custom URL Scheme Handling
```dart
// In main.dart
void main() {
  runApp(MyApp());
  
  // Listen for incoming URLs
  _linkStream = linkStream.listen((String uri) {
    handleIncomingUrl(uri);
  });
}

// URL handling
void handleIncomingUrl(String url) {
  final uri = Uri.parse(url);
  
  switch (uri.host) {
    case 'product':
      final productId = uri.queryParameters['id'];
      if (productId != null) {
        context.go('/product/$productId');
      }
      break;
    case 'user':
      final userId = uri.queryParameters['id'];
      if (userId != null) {
        context.go('/profile/$userId');
      }
      break;
  }
}
```

## React Native vs Flutter Navigation

### Navigation Comparison

| React Native | Flutter | Notes |
|--------------|---------|-------|
| `@react-navigation/native` | Built-in Navigator | Flutter has built-in navigation |
| `navigation.navigate('Screen')` | `Navigator.pushNamed('/route')` | Different method names |
| `navigation.goBack()` | `Navigator.pop(context)` | Similar functionality |
| Stack Navigator | Navigator widget | Similar stack-based approach |
| Tab Navigator | BottomNavigationBar | Different implementation |
| Drawer Navigator | Drawer widget | Similar side menu |
| `useFocusEffect` | Route lifecycle methods | Different lifecycle handling |

### Migration Examples

#### React Native Stack Navigation
```javascript
// React Native
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';

const Stack = createStackNavigator();

function App() {
  return (
    <NavigationContainer>
      <Stack.Navigator initialRouteName="Home">
        <Stack.Screen name="Home" component={HomeScreen} />
        <Stack.Screen name="Profile" component={ProfileScreen} />
      </Stack.Navigator>
    </NavigationContainer>
  );
}

// Navigation
navigation.navigate('Profile', { userId: '123' });
```

#### Flutter Navigation
```dart
// Flutter
MaterialApp(
  initialRoute: '/',
  routes: {
    '/': (context) => HomeScreen(),
    '/profile': (context) => ProfileScreen(),
  },
)

// Navigation
Navigator.pushNamed(
  context,
  '/profile',
  arguments: {'userId': '123'},
);
```

#### React Native Tab Navigation
```javascript
// React Native
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';

const Tab = createBottomTabNavigator();

function TabNavigator() {
  return (
    <Tab.Navigator>
      <Tab.Screen name="Home" component={HomeScreen} />
      <Tab.Screen name="Profile" component={ProfileScreen} />
    </Tab.Navigator>
  );
}
```

#### Flutter Tab Navigation
```dart
// Flutter
class TabNavigator extends StatefulWidget {
  @override
  _TabNavigatorState createState() => _TabNavigatorState();
}

class _TabNavigatorState extends State<TabNavigator> {
  int _selectedIndex = 0;
  
  final List<Widget> _pages = [HomeScreen(), ProfileScreen()];
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _pages[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: (index) => setState(() => _selectedIndex = index),
        items: [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
        ],
      ),
    );
  }
}
```

## Best Practices

### Navigation Best Practices
1. **Use named routes** for better organization
2. **Handle navigation state** properly in stateful widgets
3. **Implement route guards** for authentication
4. **Test deep linking** thoroughly
5. **Consider user experience** when designing navigation flow

### Performance Tips
1. **Lazy load routes** when possible
2. **Dispose of resources** when routes are popped
3. **Use Hero animations** for smooth transitions
4. **Minimize rebuild frequency** in navigation widgets
5. **Cache route data** when appropriate

### Accessibility
1. **Provide proper semantic labels** for navigation elements
2. **Support keyboard navigation** where applicable
3. **Ensure proper focus management** during navigation
4. **Test with screen readers**
5. **Maintain logical navigation order**
