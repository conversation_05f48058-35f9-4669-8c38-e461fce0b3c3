Flutter crash report.
Please report a bug at https://github.com/flutter/flutter/issues.

## command

flutter pub get

## exception

PathExistsException: PathExistsException: Cannot create link, path = '/app/linux/flutter/ephemeral/.plugin_symlinks/shared_preferences_linux' (OS Error: File exists, errno = 17)

```
#0      _Link.throwIfError (dart:io/link.dart:357:7)
#1      _Link.createSync (dart:io/link.dart:275:5)
#2      ForwardingLink.createSync (package:file/src/forwarding/forwarding_link.dart:20:16)
#3      ForwardingLink.createSync (package:file/src/forwarding/forwarding_link.dart:20:16)
#4      _createPlatformPluginSymlinks (package:flutter_tools/src/flutter_plugins.dart:1132:12)
#5      createPluginSymlinks (package:flutter_tools/src/flutter_plugins.dart:1055:5)
#6      refreshPluginsList (package:flutter_tools/src/flutter_plugins.dart:1189:5)
<asynchronous suspension>
#7      FlutterProject.ensureReadyForPlatformSpecificTooling (package:flutter_tools/src/project.dart:379:5)
<asynchronous suspension>
#8      PackagesGetCommand.runCommand (package:flutter_tools/src/commands/packages.dart:383:7)
<asynchronous suspension>
#9      FlutterCommand.run.<anonymous closure> (package:flutter_tools/src/runner/flutter_command.dart:1563:27)
<asynchronous suspension>
#10     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
<asynchronous suspension>
#11     CommandRunner.runCommand (package:args/command_runner.dart:212:13)
<asynchronous suspension>
#12     FlutterCommandRunner.runCommand.<anonymous closure> (package:flutter_tools/src/runner/flutter_command_runner.dart:494:9)
<asynchronous suspension>
#13     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
<asynchronous suspension>
#14     FlutterCommandRunner.runCommand (package:flutter_tools/src/runner/flutter_command_runner.dart:431:5)
<asynchronous suspension>
#15     run.<anonymous closure>.<anonymous closure> (package:flutter_tools/runner.dart:98:11)
<asynchronous suspension>
#16     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
<asynchronous suspension>
#17     main (package:flutter_tools/executable.dart:102:3)
<asynchronous suspension>
```

## flutter doctor

```
[✓] Flutter (Channel stable, 3.32.1, on Ubuntu 22.04.5 LTS 5.15.167.4-microsoft-standard-WSL2, locale en_US) [181ms]
    • Flutter version 3.32.1 on channel stable at /opt/flutter
    • Upstream repository https://github.com/flutter/flutter.git
    • Framework revision b25305a883 (6 days ago), 2025-05-29 10:40:06 -0700
    • Engine revision 1425e5e9ec
    • Dart version 3.8.1
    • DevTools version 2.45.1

[✗] Android toolchain - develop for Android devices [97ms]
    ✗ Unable to locate Android SDK.
      Install Android Studio from: https://developer.android.com/studio/index.html
      On first launch it will assist you in installing the Android SDK components.
      (or visit https://flutter.dev/to/linux-android-setup for detailed instructions).
      If the Android SDK has been installed to a custom location, please use
      `flutter config --android-sdk` to update to that location.


[✗] Chrome - develop for the web (Cannot find Chrome executable at google-chrome) [53ms]
    ! Cannot find Chrome. Try setting CHROME_EXECUTABLE to a Chrome executable.

[✗] Linux toolchain - develop for Linux desktop [150ms]
    ✗ clang++ is required for Linux development.
      It is likely available from your distribution (e.g.: apt install clang), or can be downloaded from https://releases.llvm.org/
    ✗ CMake is required for Linux development.
      It is likely available from your distribution (e.g.: apt install cmake), or can be downloaded from https://cmake.org/download/
    ✗ ninja is required for Linux development.
      It is likely available from your distribution (e.g.: apt install ninja-build), or can be downloaded from https://github.com/ninja-build/ninja/releases
    ✗ pkg-config is required for Linux development.
      It is likely available from your distribution (e.g.: apt install pkg-config), or can be downloaded from https://www.freedesktop.org/wiki/Software/pkg-config/

[!] Android Studio (not installed) [43ms]
    • Android Studio not found; download from https://developer.android.com/studio/index.html
      (or visit https://flutter.dev/to/linux-android-setup for detailed instructions).

[✓] Connected device (1 available) [191ms]
    • Linux (desktop) • linux • linux-x64 • Ubuntu 22.04.5 LTS 5.15.167.4-microsoft-standard-WSL2

[✓] Network resources [1,835ms]
    • All expected network resources are available.

! Doctor found issues in 4 categories.
```
