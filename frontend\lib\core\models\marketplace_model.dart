import 'package:json_annotation/json_annotation.dart';

part 'marketplace_model.g.dart';

@JsonSerializable()
class MarketplaceItem {
  final String id;
  final String name;
  final String description;
  final String? image;
  final ItemCategory category;
  final ItemType type;
  final ItemRarity rarity;
  final int price;
  final String currency;
  @JsonKey(name: 'stock_quantity')
  final int? stockQuantity;
  @<PERSON>sonKey(name: 'is_limited')
  final bool isLimited;
  @JsonKey(name: 'is_active')
  final bool isActive;
  @JsonKey(name: 'is_featured')
  final bool isFeatured;
  @JsonKey(name: 'purchase_limit')
  final int? purchaseLimit;
  @<PERSON>son<PERSON>ey(name: 'level_requirement')
  final int? levelRequirement;
  @<PERSON>son<PERSON>ey(name: 'available_from')
  final DateTime? availableFrom;
  @JsonKey(name: 'available_until')
  final DateTime? availableUntil;
  final Map<String, dynamic>? metadata;
  @<PERSON>son<PERSON>ey(name: 'created_at')
  final DateTime createdAt;
  @<PERSON>son<PERSON>ey(name: 'updated_at')
  final DateTime updatedAt;

  const MarketplaceItem({
    required this.id,
    required this.name,
    required this.description,
    this.image,
    required this.category,
    required this.type,
    required this.rarity,
    required this.price,
    required this.currency,
    this.stockQuantity,
    required this.isLimited,
    required this.isActive,
    required this.isFeatured,
    this.purchaseLimit,
    this.levelRequirement,
    this.availableFrom,
    this.availableUntil,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
  });

  factory MarketplaceItem.fromJson(Map<String, dynamic> json) => _$MarketplaceItemFromJson(json);
  Map<String, dynamic> toJson() => _$MarketplaceItemToJson(this);

  MarketplaceItem copyWith({
    String? id,
    String? name,
    String? description,
    String? image,
    ItemCategory? category,
    ItemType? type,
    ItemRarity? rarity,
    int? price,
    String? currency,
    int? stockQuantity,
    bool? isLimited,
    bool? isActive,
    bool? isFeatured,
    int? purchaseLimit,
    int? levelRequirement,
    DateTime? availableFrom,
    DateTime? availableUntil,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return MarketplaceItem(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      image: image ?? this.image,
      category: category ?? this.category,
      type: type ?? this.type,
      rarity: rarity ?? this.rarity,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      stockQuantity: stockQuantity ?? this.stockQuantity,
      isLimited: isLimited ?? this.isLimited,
      isActive: isActive ?? this.isActive,
      isFeatured: isFeatured ?? this.isFeatured,
      purchaseLimit: purchaseLimit ?? this.purchaseLimit,
      levelRequirement: levelRequirement ?? this.levelRequirement,
      availableFrom: availableFrom ?? this.availableFrom,
      availableUntil: availableUntil ?? this.availableUntil,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper getters
  bool get isAvailable {
    final now = DateTime.now();
    if (availableFrom != null && now.isBefore(availableFrom!)) return false;
    if (availableUntil != null && now.isAfter(availableUntil!)) return false;
    return isActive;
  }
  
  bool get isInStock => !isLimited || (stockQuantity != null && stockQuantity! > 0);
  bool get canPurchase => isAvailable && isInStock;
  String get formattedPrice => '$price $currency';
}

@JsonSerializable()
class Purchase {
  final String id;
  @JsonKey(name: 'user_id')
  final String userId;
  @JsonKey(name: 'item_id')
  final String itemId;
  final int quantity;
  @JsonKey(name: 'unit_price')
  final int unitPrice;
  @JsonKey(name: 'total_price')
  final int totalPrice;
  final String currency;
  final PurchaseStatus status;
  @JsonKey(name: 'transaction_id')
  final String? transactionId;
  @JsonKey(name: 'purchased_at')
  final DateTime purchasedAt;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;
  final MarketplaceItem? item;

  const Purchase({
    required this.id,
    required this.userId,
    required this.itemId,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    required this.currency,
    required this.status,
    this.transactionId,
    required this.purchasedAt,
    required this.createdAt,
    required this.updatedAt,
    this.item,
  });

  factory Purchase.fromJson(Map<String, dynamic> json) => _$PurchaseFromJson(json);
  Map<String, dynamic> toJson() => _$PurchaseToJson(this);

  // Helper getters
  String get formattedTotalPrice => '$totalPrice $currency';
  bool get isCompleted => status == PurchaseStatus.completed;
  bool get isPending => status == PurchaseStatus.pending;
  bool get isFailed => status == PurchaseStatus.failed;
}

@JsonSerializable()
class Cart {
  final String id;
  @JsonKey(name: 'user_id')
  final String userId;
  final List<CartItem> items;
  @JsonKey(name: 'total_price')
  final int totalPrice;
  final String currency;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  const Cart({
    required this.id,
    required this.userId,
    required this.items,
    required this.totalPrice,
    required this.currency,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Cart.fromJson(Map<String, dynamic> json) => _$CartFromJson(json);
  Map<String, dynamic> toJson() => _$CartToJson(this);

  // Helper getters
  int get itemCount => items.fold(0, (sum, item) => sum + item.quantity);
  bool get isEmpty => items.isEmpty;
  bool get isNotEmpty => items.isNotEmpty;
  String get formattedTotalPrice => '$totalPrice $currency';
}

@JsonSerializable()
class CartItem {
  final String id;
  @JsonKey(name: 'cart_id')
  final String cartId;
  @JsonKey(name: 'item_id')
  final String itemId;
  final int quantity;
  @JsonKey(name: 'unit_price')
  final int unitPrice;
  @JsonKey(name: 'total_price')
  final int totalPrice;
  @JsonKey(name: 'added_at')
  final DateTime addedAt;
  final MarketplaceItem? item;

  const CartItem({
    required this.id,
    required this.cartId,
    required this.itemId,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    required this.addedAt,
    this.item,
  });

  factory CartItem.fromJson(Map<String, dynamic> json) => _$CartItemFromJson(json);
  Map<String, dynamic> toJson() => _$CartItemToJson(this);

  // Helper getters
  String get formattedTotalPrice => '$totalPrice ${item?.currency ?? 'coins'}';
}

// Enums
enum ItemCategory {
  @JsonValue('cosmetic')
  cosmetic,
  @JsonValue('boost')
  boost,
  @JsonValue('consumable')
  consumable,
  @JsonValue('collectible')
  collectible,
  @JsonValue('utility')
  utility,
  @JsonValue('premium')
  premium,
}

enum ItemType {
  @JsonValue('avatar')
  avatar,
  @JsonValue('badge')
  badge,
  @JsonValue('theme')
  theme,
  @JsonValue('emote')
  emote,
  @JsonValue('title')
  title,
  @JsonValue('xp_boost')
  xpBoost,
  @JsonValue('point_boost')
  pointBoost,
  @JsonValue('energy')
  energy,
  @JsonValue('hint')
  hint,
  @JsonValue('skip')
  skip,
}

enum ItemRarity {
  @JsonValue('common')
  common,
  @JsonValue('uncommon')
  uncommon,
  @JsonValue('rare')
  rare,
  @JsonValue('epic')
  epic,
  @JsonValue('legendary')
  legendary,
}

enum PurchaseStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('completed')
  completed,
  @JsonValue('failed')
  failed,
  @JsonValue('refunded')
  refunded,
}

// Extensions
extension ItemCategoryExtension on ItemCategory {
  String get displayName {
    switch (this) {
      case ItemCategory.cosmetic:
        return 'Cosmetic';
      case ItemCategory.boost:
        return 'Boost';
      case ItemCategory.consumable:
        return 'Consumable';
      case ItemCategory.collectible:
        return 'Collectible';
      case ItemCategory.utility:
        return 'Utility';
      case ItemCategory.premium:
        return 'Premium';
    }
  }
}

extension ItemTypeExtension on ItemType {
  String get displayName {
    switch (this) {
      case ItemType.avatar:
        return 'Avatar';
      case ItemType.badge:
        return 'Badge';
      case ItemType.theme:
        return 'Theme';
      case ItemType.emote:
        return 'Emote';
      case ItemType.title:
        return 'Title';
      case ItemType.xpBoost:
        return 'XP Boost';
      case ItemType.pointBoost:
        return 'Point Boost';
      case ItemType.energy:
        return 'Energy';
      case ItemType.hint:
        return 'Hint';
      case ItemType.skip:
        return 'Skip';
    }
  }
}
