# Flutter Testing Guide

## Table of Contents
1. [Testing Fundamentals](#testing-fundamentals)
2. [Unit Testing](#unit-testing)
3. [Widget Testing](#widget-testing)
4. [Integration Testing](#integration-testing)
5. [Golden File Testing](#golden-file-testing)
6. [Mocking and Test Doubles](#mocking-and-test-doubles)
7. [Testing State Management](#testing-state-management)
8. [Testing Async Code](#testing-async-code)
9. [Testing Navigation](#testing-navigation)
10. [Performance Testing](#performance-testing)
11. [React Native vs Flutter Testing](#react-native-vs-flutter-testing)

## Testing Fundamentals

### Test Types in Flutter
- **Unit Tests**: Test individual functions, methods, and classes
- **Widget Tests**: Test individual widgets and their interactions
- **Integration Tests**: Test complete app flows and features
- **Golden Tests**: Visual regression testing

### Test Setup
```yaml
# pubspec.yaml
dev_dependencies:
  flutter_test:
    sdk: flutter
  mockito: ^5.0.0
  build_runner: ^2.0.0
  integration_test:
    sdk: flutter
```

### Basic Test Structure
```dart
// test/example_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/calculator.dart';

void main() {
  group('Calculator Tests', () {
    late Calculator calculator;
    
    setUp(() {
      calculator = Calculator();
    });
    
    tearDown(() {
      // Clean up after each test
    });
    
    test('should add two numbers correctly', () {
      // Arrange
      const a = 5;
      const b = 3;
      
      // Act
      final result = calculator.add(a, b);
      
      // Assert
      expect(result, equals(8));
    });
    
    test('should throw exception for invalid input', () {
      expect(() => calculator.divide(10, 0), throwsA(isA<ArgumentError>()));
    });
  });
}
```

## Unit Testing

### Testing Simple Classes
```dart
// lib/models/user.dart
class User {
  final String id;
  final String name;
  final String email;
  final DateTime createdAt;
  
  User({
    required this.id,
    required this.name,
    required this.email,
    required this.createdAt,
  });
  
  bool get isNewUser => DateTime.now().difference(createdAt).inDays < 7;
  
  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'email': email,
    'createdAt': createdAt.toIso8601String(),
  };
  
  factory User.fromJson(Map<String, dynamic> json) => User(
    id: json['id'],
    name: json['name'],
    email: json['email'],
    createdAt: DateTime.parse(json['createdAt']),
  );
}

// test/models/user_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/models/user.dart';

void main() {
  group('User Model Tests', () {
    test('should create user with correct properties', () {
      final createdAt = DateTime(2023, 1, 1);
      final user = User(
        id: '123',
        name: 'John Doe',
        email: '<EMAIL>',
        createdAt: createdAt,
      );
      
      expect(user.id, equals('123'));
      expect(user.name, equals('John Doe'));
      expect(user.email, equals('<EMAIL>'));
      expect(user.createdAt, equals(createdAt));
    });
    
    test('should identify new users correctly', () {
      final recentUser = User(
        id: '1',
        name: 'New User',
        email: '<EMAIL>',
        createdAt: DateTime.now().subtract(Duration(days: 3)),
      );
      
      final oldUser = User(
        id: '2',
        name: 'Old User',
        email: '<EMAIL>',
        createdAt: DateTime.now().subtract(Duration(days: 10)),
      );
      
      expect(recentUser.isNewUser, isTrue);
      expect(oldUser.isNewUser, isFalse);
    });
    
    test('should serialize to JSON correctly', () {
      final user = User(
        id: '123',
        name: 'John Doe',
        email: '<EMAIL>',
        createdAt: DateTime(2023, 1, 1),
      );
      
      final json = user.toJson();
      
      expect(json['id'], equals('123'));
      expect(json['name'], equals('John Doe'));
      expect(json['email'], equals('<EMAIL>'));
      expect(json['createdAt'], equals('2023-01-01T00:00:00.000'));
    });
    
    test('should deserialize from JSON correctly', () {
      final json = {
        'id': '123',
        'name': 'John Doe',
        'email': '<EMAIL>',
        'createdAt': '2023-01-01T00:00:00.000',
      };
      
      final user = User.fromJson(json);
      
      expect(user.id, equals('123'));
      expect(user.name, equals('John Doe'));
      expect(user.email, equals('<EMAIL>'));
      expect(user.createdAt, equals(DateTime(2023, 1, 1)));
    });
  });
}
```

### Testing Business Logic
```dart
// lib/services/cart_service.dart
class CartService {
  final List<CartItem> _items = [];
  
  List<CartItem> get items => List.unmodifiable(_items);
  
  void addItem(Product product, int quantity) {
    if (quantity <= 0) {
      throw ArgumentError('Quantity must be positive');
    }
    
    final existingIndex = _items.indexWhere(
      (item) => item.product.id == product.id,
    );
    
    if (existingIndex >= 0) {
      _items[existingIndex] = _items[existingIndex].copyWith(
        quantity: _items[existingIndex].quantity + quantity,
      );
    } else {
      _items.add(CartItem(product: product, quantity: quantity));
    }
  }
  
  void removeItem(String productId) {
    _items.removeWhere((item) => item.product.id == productId);
  }
  
  double getTotalPrice() {
    return _items.fold(0.0, (sum, item) => sum + (item.product.price * item.quantity));
  }
  
  void clear() {
    _items.clear();
  }
}

// test/services/cart_service_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/services/cart_service.dart';
import 'package:myapp/models/product.dart';
import 'package:myapp/models/cart_item.dart';

void main() {
  group('CartService Tests', () {
    late CartService cartService;
    late Product testProduct;
    
    setUp(() {
      cartService = CartService();
      testProduct = Product(
        id: '1',
        name: 'Test Product',
        price: 10.0,
      );
    });
    
    test('should start with empty cart', () {
      expect(cartService.items, isEmpty);
      expect(cartService.getTotalPrice(), equals(0.0));
    });
    
    test('should add item to cart', () {
      cartService.addItem(testProduct, 2);
      
      expect(cartService.items.length, equals(1));
      expect(cartService.items.first.product.id, equals('1'));
      expect(cartService.items.first.quantity, equals(2));
    });
    
    test('should increase quantity when adding existing item', () {
      cartService.addItem(testProduct, 1);
      cartService.addItem(testProduct, 2);
      
      expect(cartService.items.length, equals(1));
      expect(cartService.items.first.quantity, equals(3));
    });
    
    test('should throw error for invalid quantity', () {
      expect(
        () => cartService.addItem(testProduct, 0),
        throwsA(isA<ArgumentError>()),
      );
      
      expect(
        () => cartService.addItem(testProduct, -1),
        throwsA(isA<ArgumentError>()),
      );
    });
    
    test('should calculate total price correctly', () {
      final product1 = Product(id: '1', name: 'Product 1', price: 10.0);
      final product2 = Product(id: '2', name: 'Product 2', price: 15.0);
      
      cartService.addItem(product1, 2); // 2 * 10 = 20
      cartService.addItem(product2, 1); // 1 * 15 = 15
      
      expect(cartService.getTotalPrice(), equals(35.0));
    });
    
    test('should remove item from cart', () {
      cartService.addItem(testProduct, 1);
      expect(cartService.items.length, equals(1));
      
      cartService.removeItem('1');
      expect(cartService.items, isEmpty);
    });
    
    test('should clear all items', () {
      cartService.addItem(testProduct, 1);
      cartService.addItem(
        Product(id: '2', name: 'Product 2', price: 20.0),
        2,
      );
      
      expect(cartService.items.length, equals(2));
      
      cartService.clear();
      expect(cartService.items, isEmpty);
    });
  });
}
```

## Widget Testing

### Basic Widget Tests
```dart
// lib/widgets/counter_widget.dart
class CounterWidget extends StatefulWidget {
  final int initialValue;
  
  const CounterWidget({Key? key, this.initialValue = 0}) : super(key: key);
  
  @override
  _CounterWidgetState createState() => _CounterWidgetState();
}

class _CounterWidgetState extends State<CounterWidget> {
  late int counter;
  
  @override
  void initState() {
    super.initState();
    counter = widget.initialValue;
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          'Counter: $counter',
          key: const Key('counter_text'),
        ),
        ElevatedButton(
          key: const Key('increment_button'),
          onPressed: () {
            setState(() {
              counter++;
            });
          },
          child: const Text('Increment'),
        ),
        ElevatedButton(
          key: const Key('decrement_button'),
          onPressed: () {
            setState(() {
              counter--;
            });
          },
          child: const Text('Decrement'),
        ),
      ],
    );
  }
}

// test/widgets/counter_widget_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/widgets/counter_widget.dart';

void main() {
  group('CounterWidget Tests', () {
    testWidgets('should display initial counter value', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CounterWidget(initialValue: 5),
          ),
        ),
      );
      
      expect(find.text('Counter: 5'), findsOneWidget);
    });
    
    testWidgets('should increment counter when button pressed', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CounterWidget(),
          ),
        ),
      );
      
      // Verify initial state
      expect(find.text('Counter: 0'), findsOneWidget);
      
      // Tap increment button
      await tester.tap(find.byKey(const Key('increment_button')));
      await tester.pump();
      
      // Verify counter incremented
      expect(find.text('Counter: 1'), findsOneWidget);
      expect(find.text('Counter: 0'), findsNothing);
    });
    
    testWidgets('should decrement counter when button pressed', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CounterWidget(initialValue: 5),
          ),
        ),
      );
      
      // Tap decrement button
      await tester.tap(find.byKey(const Key('decrement_button')));
      await tester.pump();
      
      // Verify counter decremented
      expect(find.text('Counter: 4'), findsOneWidget);
    });
    
    testWidgets('should handle multiple increments', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CounterWidget(),
          ),
        ),
      );
      
      // Tap increment button multiple times
      for (int i = 0; i < 3; i++) {
        await tester.tap(find.byKey(const Key('increment_button')));
        await tester.pump();
      }
      
      expect(find.text('Counter: 3'), findsOneWidget);
    });
  });
}
```

### Testing Complex Widgets
```dart
// lib/widgets/user_profile.dart
class UserProfile extends StatelessWidget {
  final User user;
  final VoidCallback? onEditPressed;
  
  const UserProfile({
    Key? key,
    required this.user,
    this.onEditPressed,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundImage: NetworkImage(user.avatarUrl),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user.name,
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      Text(
                        user.email,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
                if (onEditPressed != null)
                  IconButton(
                    onPressed: onEditPressed,
                    icon: const Icon(Icons.edit),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            Text('Member since: ${_formatDate(user.createdAt)}'),
            if (user.isNewUser)
              Container(
                margin: const EdgeInsets.only(top: 8),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Text(
                  'New User',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
              ),
          ],
        ),
      ),
    );
  }
  
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

// test/widgets/user_profile_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/widgets/user_profile.dart';
import 'package:myapp/models/user.dart';

void main() {
  group('UserProfile Widget Tests', () {
    late User testUser;
    
    setUp(() {
      testUser = User(
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        avatarUrl: 'https://example.com/avatar.jpg',
        createdAt: DateTime(2023, 1, 1),
      );
    });
    
    testWidgets('should display user information', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: UserProfile(user: testUser),
          ),
        ),
      );
      
      expect(find.text('John Doe'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);
      expect(find.text('Member since: 1/1/2023'), findsOneWidget);
    });
    
    testWidgets('should show edit button when callback provided', (tester) async {
      bool editPressed = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: UserProfile(
              user: testUser,
              onEditPressed: () {
                editPressed = true;
              },
            ),
          ),
        ),
      );
      
      expect(find.byIcon(Icons.edit), findsOneWidget);
      
      await tester.tap(find.byIcon(Icons.edit));
      await tester.pump();
      
      expect(editPressed, isTrue);
    });
    
    testWidgets('should not show edit button when no callback', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: UserProfile(user: testUser),
          ),
        ),
      );
      
      expect(find.byIcon(Icons.edit), findsNothing);
    });
    
    testWidgets('should show new user badge for recent users', (tester) async {
      final newUser = User(
        id: '2',
        name: 'New User',
        email: '<EMAIL>',
        avatarUrl: 'https://example.com/avatar.jpg',
        createdAt: DateTime.now().subtract(Duration(days: 3)),
      );
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: UserProfile(user: newUser),
          ),
        ),
      );
      
      expect(find.text('New User'), findsOneWidget);
      expect(find.text('New User'), findsNWidgets(2)); // Name + badge
    });
    
    testWidgets('should not show new user badge for old users', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: UserProfile(user: testUser),
          ),
        ),
      );
      
      expect(find.text('New User'), findsNothing);
    });
  });
}
```

### Testing Forms and Input
```dart
// test/widgets/login_form_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/widgets/login_form.dart';

void main() {
  group('LoginForm Widget Tests', () {
    testWidgets('should validate email format', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: LoginForm(),
          ),
        ),
      );
      
      // Enter invalid email
      await tester.enterText(find.byKey(const Key('email_field')), 'invalid-email');
      await tester.tap(find.byKey(const Key('login_button')));
      await tester.pump();
      
      expect(find.text('Please enter a valid email'), findsOneWidget);
    });
    
    testWidgets('should validate password length', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: LoginForm(),
          ),
        ),
      );
      
      // Enter short password
      await tester.enterText(find.byKey(const Key('password_field')), '123');
      await tester.tap(find.byKey(const Key('login_button')));
      await tester.pump();
      
      expect(find.text('Password must be at least 6 characters'), findsOneWidget);
    });
    
    testWidgets('should call onLogin with valid credentials', (tester) async {
      String? capturedEmail;
      String? capturedPassword;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LoginForm(
              onLogin: (email, password) {
                capturedEmail = email;
                capturedPassword = password;
              },
            ),
          ),
        ),
      );
      
      // Enter valid credentials
      await tester.enterText(find.byKey(const Key('email_field')), '<EMAIL>');
      await tester.enterText(find.byKey(const Key('password_field')), 'password123');
      await tester.tap(find.byKey(const Key('login_button')));
      await tester.pump();
      
      expect(capturedEmail, equals('<EMAIL>'));
      expect(capturedPassword, equals('password123'));
    });
  });
}
```

## Integration Testing

### Basic Integration Test
```dart
// integration_test/app_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:myapp/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  group('App Integration Tests', () {
    testWidgets('complete user login flow', (tester) async {
      app.main();
      await tester.pumpAndSettle();
      
      // Navigate to login page
      await tester.tap(find.byKey(const Key('login_button')));
      await tester.pumpAndSettle();
      
      // Enter credentials
      await tester.enterText(find.byKey(const Key('email_field')), '<EMAIL>');
      await tester.enterText(find.byKey(const Key('password_field')), 'password123');
      
      // Submit login form
      await tester.tap(find.byKey(const Key('submit_button')));
      await tester.pumpAndSettle();
      
      // Verify successful login
      expect(find.text('Welcome back!'), findsOneWidget);
      expect(find.byKey(const Key('home_page')), findsOneWidget);
    });
    
    testWidgets('shopping cart flow', (tester) async {
      app.main();
      await tester.pumpAndSettle();
      
      // Navigate to products
      await tester.tap(find.byKey(const Key('products_tab')));
      await tester.pumpAndSettle();
      
      // Add item to cart
      await tester.tap(find.byKey(const Key('add_to_cart_0')));
      await tester.pumpAndSettle();
      
      // Verify cart badge
      expect(find.text('1'), findsOneWidget);
      
      // Navigate to cart
      await tester.tap(find.byKey(const Key('cart_button')));
      await tester.pumpAndSettle();
      
      // Verify item in cart
      expect(find.byKey(const Key('cart_item_0')), findsOneWidget);
      
      // Proceed to checkout
      await tester.tap(find.byKey(const Key('checkout_button')));
      await tester.pumpAndSettle();
      
      // Verify checkout page
      expect(find.byKey(const Key('checkout_page')), findsOneWidget);
    });
  });
}
```

### Performance Integration Tests
```dart
// integration_test/performance_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:myapp/main.dart' as app;

void main() {
  final binding = IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  group('Performance Tests', () {
    testWidgets('list scrolling performance', (tester) async {
      app.main();
      await tester.pumpAndSettle();
      
      // Navigate to list page
      await tester.tap(find.byKey(const Key('products_tab')));
      await tester.pumpAndSettle();
      
      // Start performance tracking
      await binding.traceAction(() async {
        // Scroll through the list
        final listFinder = find.byType(ListView);
        
        for (int i = 0; i < 10; i++) {
          await tester.fling(listFinder, const Offset(0, -300), 1000);
          await tester.pumpAndSettle();
        }
      });
    });
    
    testWidgets('app startup performance', (tester) async {
      await binding.traceAction(() async {
        app.main();
        await tester.pumpAndSettle();
      });
    });
  });
}
```

## Golden File Testing

### Creating Golden Tests
```dart
// test/golden/widget_golden_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/widgets/user_card.dart';
import 'package:myapp/models/user.dart';

void main() {
  group('UserCard Golden Tests', () {
    testWidgets('user card default appearance', (tester) async {
      final user = User(
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        avatarUrl: 'https://example.com/avatar.jpg',
        createdAt: DateTime(2023, 1, 1),
      );
      
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Scaffold(
            body: Center(
              child: UserCard(user: user),
            ),
          ),
        ),
      );
      
      await expectLater(
        find.byType(UserCard),
        matchesGoldenFile('golden/user_card_default.png'),
      );
    });
    
    testWidgets('user card dark theme', (tester) async {
      final user = User(
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        avatarUrl: 'https://example.com/avatar.jpg',
        createdAt: DateTime(2023, 1, 1),
      );
      
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.dark(),
          home: Scaffold(
            body: Center(
              child: UserCard(user: user),
            ),
          ),
        ),
      );
      
      await expectLater(
        find.byType(UserCard),
        matchesGoldenFile('golden/user_card_dark.png'),
      );
    });
    
    testWidgets('user card with new user badge', (tester) async {
      final newUser = User(
        id: '2',
        name: 'New User',
        email: '<EMAIL>',
        avatarUrl: 'https://example.com/avatar.jpg',
        createdAt: DateTime.now().subtract(Duration(days: 3)),
      );
      
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Scaffold(
            body: Center(
              child: UserCard(user: newUser),
            ),
          ),
        ),
      );
      
      await expectLater(
        find.byType(UserCard),
        matchesGoldenFile('golden/user_card_new_user.png'),
      );
    });
  });
}
```

### Running Golden Tests
```bash
# Generate golden files
flutter test --update-goldens

# Run golden tests
flutter test test/golden/

# Compare specific golden file
flutter test test/golden/widget_golden_test.dart --update-goldens
```

## Mocking and Test Doubles

### Using Mockito
```dart
// lib/services/api_service.dart
abstract class ApiService {
  Future<List<User>> getUsers();
  Future<User> getUser(String id);
  Future<User> createUser(User user);
  Future<void> deleteUser(String id);
}

// Generate mocks
// test/mocks.dart
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:myapp/services/api_service.dart';

@GenerateMocks([ApiService])
import 'mocks.mocks.dart';

// test/services/user_repository_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:myapp/services/user_repository.dart';
import 'package:myapp/models/user.dart';
import 'mocks.mocks.dart';

void main() {
  group('UserRepository Tests', () {
    late MockApiService mockApiService;
    late UserRepository userRepository;
    
    setUp(() {
      mockApiService = MockApiService();
      userRepository = UserRepository(apiService: mockApiService);
    });
    
    test('should return users from API', () async {
      // Arrange
      final expectedUsers = [
        User(id: '1', name: 'User 1', email: '<EMAIL>', createdAt: DateTime.now()),
        User(id: '2', name: 'User 2', email: '<EMAIL>', createdAt: DateTime.now()),
      ];
      
      when(mockApiService.getUsers()).thenAnswer((_) async => expectedUsers);
      
      // Act
      final users = await userRepository.getAllUsers();
      
      // Assert
      expect(users, equals(expectedUsers));
      verify(mockApiService.getUsers()).called(1);
    });
    
    test('should handle API errors gracefully', () async {
      // Arrange
      when(mockApiService.getUsers()).thenThrow(Exception('API Error'));
      
      // Act & Assert
      expect(
        () => userRepository.getAllUsers(),
        throwsA(isA<Exception>()),
      );
    });
    
    test('should cache users after first fetch', () async {
      // Arrange
      final users = [
        User(id: '1', name: 'User 1', email: '<EMAIL>', createdAt: DateTime.now()),
      ];
      
      when(mockApiService.getUsers()).thenAnswer((_) async => users);
      
      // Act
      await userRepository.getAllUsers();
      await userRepository.getAllUsers();
      
      // Assert
      verify(mockApiService.getUsers()).called(1); // Called only once due to caching
    });
  });
}
```

### Manual Test Doubles
```dart
// test/fakes/fake_api_service.dart
class FakeApiService implements ApiService {
  final List<User> _users = [];
  bool shouldThrowError = false;
  
  @override
  Future<List<User>> getUsers() async {
    if (shouldThrowError) {
      throw Exception('Network error');
    }
    
    await Future.delayed(Duration(milliseconds: 100)); // Simulate network delay
    return List.from(_users);
  }
  
  @override
  Future<User> getUser(String id) async {
    if (shouldThrowError) {
      throw Exception('Network error');
    }
    
    final user = _users.firstWhere(
      (u) => u.id == id,
      orElse: () => throw Exception('User not found'),
    );
    
    return user;
  }
  
  @override
  Future<User> createUser(User user) async {
    if (shouldThrowError) {
      throw Exception('Network error');
    }
    
    _users.add(user);
    return user;
  }
  
  @override
  Future<void> deleteUser(String id) async {
    if (shouldThrowError) {
      throw Exception('Network error');
    }
    
    _users.removeWhere((u) => u.id == id);
  }
  
  // Test helper methods
  void addUser(User user) {
    _users.add(user);
  }
  
  void clearUsers() {
    _users.clear();
  }
  
  void simulateError() {
    shouldThrowError = true;
  }
  
  void clearError() {
    shouldThrowError = false;
  }
}
```

## Testing State Management

### Testing Provider
```dart
// test/providers/counter_provider_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/providers/counter_provider.dart';

void main() {
  group('CounterProvider Tests', () {
    late CounterProvider counterProvider;
    
    setUp(() {
      counterProvider = CounterProvider();
    });
    
    tearDown(() {
      counterProvider.dispose();
    });
    
    test('should start with initial value', () {
      expect(counterProvider.counter, equals(0));
    });
    
    test('should increment counter', () {
      counterProvider.increment();
      expect(counterProvider.counter, equals(1));
    });
    
    test('should notify listeners on increment', () {
      bool notified = false;
      counterProvider.addListener(() {
        notified = true;
      });
      
      counterProvider.increment();
      expect(notified, isTrue);
    });
    
    test('should reset counter', () {
      counterProvider.increment();
      counterProvider.increment();
      expect(counterProvider.counter, equals(2));
      
      counterProvider.reset();
      expect(counterProvider.counter, equals(0));
    });
  });
}
```

### Testing Bloc
```dart
// test/blocs/auth_bloc_test.dart
import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:myapp/blocs/auth_bloc.dart';
import '../mocks.mocks.dart';

void main() {
  group('AuthBloc Tests', () {
    late MockUserRepository mockUserRepository;
    late AuthBloc authBloc;
    
    setUp(() {
      mockUserRepository = MockUserRepository();
      authBloc = AuthBloc(userRepository: mockUserRepository);
    });
    
    tearDown(() {
      authBloc.close();
    });
    
    test('initial state is AuthInitial', () {
      expect(authBloc.state, equals(AuthInitial()));
    });
    
    blocTest<AuthBloc, AuthState>(
      'emits [AuthLoading, AuthAuthenticated] when login succeeds',
      build: () {
        when(mockUserRepository.login(any, any)).thenAnswer(
          (_) async => User(id: '1', name: 'Test User', email: '<EMAIL>', createdAt: DateTime.now()),
        );
        return authBloc;
      },
      act: (bloc) => bloc.add(AuthLoginRequested(
        email: '<EMAIL>',
        password: 'password123',
      )),
      expect: () => [
        AuthLoading(),
        isA<AuthAuthenticated>(),
      ],
    );
    
    blocTest<AuthBloc, AuthState>(
      'emits [AuthLoading, AuthError] when login fails',
      build: () {
        when(mockUserRepository.login(any, any)).thenThrow(
          Exception('Invalid credentials'),
        );
        return authBloc;
      },
      act: (bloc) => bloc.add(AuthLoginRequested(
        email: '<EMAIL>',
        password: 'wrongpassword',
      )),
      expect: () => [
        AuthLoading(),
        isA<AuthError>(),
      ],
    );
  });
}
```

## Testing Async Code

### Testing Futures
```dart
// test/services/async_service_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/services/data_service.dart';

void main() {
  group('DataService Async Tests', () {
    late DataService dataService;
    
    setUp(() {
      dataService = DataService();
    });
    
    test('should fetch data successfully', () async {
      final result = await dataService.fetchData();
      
      expect(result, isNotNull);
      expect(result.isNotEmpty, isTrue);
    });
    
    test('should handle timeout errors', () async {
      dataService.setTimeout(Duration(milliseconds: 100));
      
      expect(
        () => dataService.fetchSlowData(),
        throwsA(isA<TimeoutException>()),
      );
    });
    
    test('should retry failed requests', () async {
      int attemptCount = 0;
      dataService.onRequest = () {
        attemptCount++;
        if (attemptCount < 3) {
          throw Exception('Network error');
        }
      };
      
      final result = await dataService.fetchDataWithRetry();
      
      expect(attemptCount, equals(3));
      expect(result, isNotNull);
    });
  });
}
```

### Testing Streams
```dart
// test/services/stream_service_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/services/message_service.dart';

void main() {
  group('MessageService Stream Tests', () {
    late MessageService messageService;
    
    setUp(() {
      messageService = MessageService();
    });
    
    tearDown(() {
      messageService.dispose();
    });
    
    test('should emit messages from stream', () async {
      final messages = <String>[];
      
      messageService.messageStream.listen((message) {
        messages.add(message);
      });
      
      messageService.sendMessage('Hello');
      messageService.sendMessage('World');
      
      // Wait for stream events
      await Future.delayed(Duration(milliseconds: 100));
      
      expect(messages, equals(['Hello', 'World']));
    });
    
    test('should handle stream errors', () async {
      Exception? caughtError;
      
      messageService.messageStream.listen(
        (message) {},
        onError: (error) {
          caughtError = error;
        },
      );
      
      messageService.simulateError();
      
      await Future.delayed(Duration(milliseconds: 100));
      
      expect(caughtError, isNotNull);
      expect(caughtError, isA<Exception>());
    });
  });
}
```

## React Native vs Flutter Testing

### Testing Comparison

| React Native | Flutter | Notes |
|--------------|---------|-------|
| Jest | flutter_test | Main testing framework |
| React Native Testing Library | Widget testing | Component/widget testing |
| Detox | integration_test | E2E testing |
| Enzyme | flutter_test finders | Element selection |
| Snapshot testing | Golden file testing | Visual regression |

### Migration Examples

#### React Native Component Test
```javascript
// React Native
import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import Counter from '../Counter';

test('increments counter when button pressed', () => {
  const { getByText } = render(<Counter initialValue={0} />);
  
  expect(getByText('Count: 0')).toBeTruthy();
  
  fireEvent.press(getByText('Increment'));
  
  expect(getByText('Count: 1')).toBeTruthy();
});
```

#### Flutter Widget Test
```dart
// Flutter
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/widgets/counter.dart';

void main() {
  testWidgets('increments counter when button pressed', (tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: Counter(initialValue: 0),
      ),
    );
    
    expect(find.text('Count: 0'), findsOneWidget);
    
    await tester.tap(find.text('Increment'));
    await tester.pump();
    
    expect(find.text('Count: 1'), findsOneWidget);
  });
}
```

## Best Practices

### Test Organization
1. **Group related tests** using `group()`
2. **Use descriptive test names** that explain what is being tested
3. **Follow AAA pattern** (Arrange, Act, Assert)
4. **Use setUp and tearDown** for common initialization and cleanup
5. **Keep tests isolated** and independent

### Performance Testing
1. **Test on real devices** when possible
2. **Monitor memory usage** during tests
3. **Use performance overlays** in development
4. **Test with large datasets** to identify bottlenecks
5. **Profile critical user flows**

### Mocking Guidelines
1. **Mock external dependencies** only
2. **Use dependency injection** for easier testing
3. **Prefer fakes over mocks** for complex scenarios
4. **Verify mock interactions** when behavior matters
5. **Reset mocks** between tests

### Coverage and Quality
1. **Aim for high test coverage** but focus on critical paths
2. **Test error conditions** and edge cases
3. **Write tests before fixing bugs** (regression tests)
4. **Use golden tests** for UI consistency
5. **Automate testing** in CI/CD pipelines
