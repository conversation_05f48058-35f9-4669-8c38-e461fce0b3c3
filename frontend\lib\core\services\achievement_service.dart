import 'package:dio/dio.dart';
import '../models/achievement_model.dart';

class AchievementService {
  final Dio dio;

  AchievementService(this.dio);

  // Error handling method
  Exception handleError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return Exception('Connection timeout. Please check your internet connection.');
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          final message = error.response?.data?['message'] ?? 'Server error occurred';
          return Exception('Server error ($statusCode): $message');
        case DioExceptionType.cancel:
          return Exception('Request was cancelled');
        case DioExceptionType.unknown:
          return Exception('Network error occurred');
        default:
          return Exception('An unexpected error occurred');
      }
    }
    return Exception('An unexpected error occurred: $error');
  }

  // Get all achievements with filtering and pagination
  Future<List<Achievement>> getAchievements({
    AchievementCategory? category,
    AchievementType? type,
    AchievementRarity? rarity,
    bool? isHidden,
    bool? isActive,
    bool? unlockedOnly,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (category != null) queryParams['category'] = category.name;
      if (type != null) queryParams['type'] = type.name;
      if (rarity != null) queryParams['rarity'] = rarity.name;
      if (isHidden != null) queryParams['is_hidden'] = isHidden;
      if (isActive != null) queryParams['is_active'] = isActive;
      if (unlockedOnly != null) queryParams['unlocked_only'] = unlockedOnly;

      final response = await dio.get('/achievements', queryParameters: queryParams);
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => Achievement.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get achievement by ID
  Future<Achievement> getAchievement(String id) async {
    try {
      final response = await dio.get('/achievements/$id');
      return Achievement.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get achievements by category
  Future<List<Achievement>> getAchievementsByCategory(AchievementCategory category, {
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await dio.get('/achievements/category/${category.name}', 
        queryParameters: {'page': page, 'limit': limit});
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => Achievement.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get user's achievements
  Future<List<UserAchievement>> getUserAchievements({
    bool? isUnlocked,
    bool? isClaimed,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (isUnlocked != null) queryParams['is_unlocked'] = isUnlocked;
      if (isClaimed != null) queryParams['is_claimed'] = isClaimed;

      final response = await dio.get('/user/achievements', queryParameters: queryParams);
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => UserAchievement.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get achievement statistics
  Future<Map<String, dynamic>> getAchievementStatistics() async {
    try {
      final response = await dio.get('/achievements/statistics');
      return response.data['data'] ?? response.data;
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get achievement categories
  Future<List<AchievementCategory>> getAchievementCategories() async {
    try {
      final response = await dio.get('/achievements/categories');
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((categoryName) {
        return AchievementCategory.values.firstWhere(
          (category) => category.name == categoryName,
          orElse: () => AchievementCategory.special,
        );
      }).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Claim achievement reward
  Future<Map<String, dynamic>> claimAchievementReward(String achievementId) async {
    try {
      final response = await dio.post('/achievements/$achievementId/claim');
      return response.data;
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get user's achievement progress for a specific achievement
  Future<UserAchievement?> getUserAchievementProgress(String achievementId) async {
    try {
      final response = await dio.get('/user/achievements/$achievementId');
      if (response.data == null) return null;
      return UserAchievement.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      if (e is DioException && e.response?.statusCode == 404) {
        return null;
      }
      throw handleError(e);
    }
  }

  // Get recently unlocked achievements
  Future<List<UserAchievement>> getRecentlyUnlockedAchievements({int limit = 10}) async {
    try {
      final response = await dio.get('/user/achievements/recent', 
        queryParameters: {'limit': limit});
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => UserAchievement.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get achievements by rarity
  Future<List<Achievement>> getAchievementsByRarity(AchievementRarity rarity, {
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await dio.get('/achievements', queryParameters: {
        'rarity': rarity.name,
        'page': page,
        'limit': limit,
      });
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => Achievement.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get user's achievement summary
  Future<Map<String, dynamic>> getUserAchievementSummary() async {
    try {
      final response = await dio.get('/user/achievements/summary');
      return response.data['data'] ?? response.data;
    } catch (e) {
      throw handleError(e);
    }
  }

  // Check achievement progress (for real-time updates)
  Future<List<UserAchievement>> checkAchievementProgress() async {
    try {
      final response = await dio.post('/user/achievements/check');
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => UserAchievement.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Create a new achievement (admin functionality)
  Future<Achievement> createAchievement({
    required String name,
    required String description,
    String? icon,
    String? image,
    required AchievementCategory category,
    required AchievementType type,
    required AchievementRarity rarity,
    required int points,
    required int experience,
    required Map<String, dynamic> unlockCriteria,
    bool isHidden = false,
    bool isActive = true,
    List<AchievementReward>? rewards,
  }) async {
    try {
      final data = <String, dynamic>{
        'name': name,
        'description': description,
        'category': category.name,
        'type': type.name,
        'rarity': rarity.name,
        'points': points,
        'experience': experience,
        'unlock_criteria': unlockCriteria,
        'is_hidden': isHidden,
        'is_active': isActive,
      };

      if (icon != null) data['icon'] = icon;
      if (image != null) data['image'] = image;
      if (rewards != null) data['rewards'] = rewards.map((reward) => reward.toJson()).toList();

      final response = await dio.post('/achievements', data: data);
      return Achievement.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Update an achievement (admin functionality)
  Future<Achievement> updateAchievement(String achievementId, {
    String? name,
    String? description,
    String? icon,
    String? image,
    AchievementCategory? category,
    AchievementType? type,
    AchievementRarity? rarity,
    int? points,
    int? experience,
    Map<String, dynamic>? unlockCriteria,
    bool? isHidden,
    bool? isActive,
  }) async {
    try {
      final data = <String, dynamic>{};

      if (name != null) data['name'] = name;
      if (description != null) data['description'] = description;
      if (icon != null) data['icon'] = icon;
      if (image != null) data['image'] = image;
      if (category != null) data['category'] = category.name;
      if (type != null) data['type'] = type.name;
      if (rarity != null) data['rarity'] = rarity.name;
      if (points != null) data['points'] = points;
      if (experience != null) data['experience'] = experience;
      if (unlockCriteria != null) data['unlock_criteria'] = unlockCriteria;
      if (isHidden != null) data['is_hidden'] = isHidden;
      if (isActive != null) data['is_active'] = isActive;

      final response = await dio.put('/achievements/$achievementId', data: data);
      return Achievement.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Delete an achievement (admin functionality)
  Future<void> deleteAchievement(String achievementId) async {
    try {
      await dio.delete('/achievements/$achievementId');
    } catch (e) {
      throw handleError(e);
    }
  }
}
