package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Base model with common fields
type BaseModel struct {
	ID        uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
}

// User represents a user in the system
type User struct {
	BaseModel
	Username         string            `json:"username" gorm:"uniqueIndex;not null"`
	Email            string            `json:"email" gorm:"uniqueIndex;not null"`
	PasswordHash     string            `json:"-" gorm:"not null"`
	FirstName        string            `json:"first_name"`
	LastName         string            `json:"last_name"`
	Avatar           string            `json:"avatar"`
	Bio              string            `json:"bio"`
	Level            int               `json:"level" gorm:"default:1"`
	Experience       int               `json:"experience" gorm:"default:0"`
	Points           int               `json:"points" gorm:"default:0"`
	IsActive         bool              `json:"is_active" gorm:"default:true"`
	IsVerified       bool              `json:"is_verified" gorm:"default:false"`
	LastLoginAt      *time.Time        `json:"last_login_at"`
	Roles            []Role            `json:"roles" gorm:"many2many:user_roles;"`
	Quests           []UserQuest       `json:"quests"`
	Achievements     []UserAchievement `json:"achievements"`
	Wallet           Wallet            `json:"wallet"`
	Friends          []User            `json:"friends" gorm:"many2many:user_friends;"`
	Notifications    []Notification    `json:"notifications"`
	MarketplaceItems []MarketplaceItem `json:"marketplace_items" gorm:"foreignKey:SellerID"`
}

// Role represents user roles
type Role struct {
	BaseModel
	Name        string       `json:"name" gorm:"uniqueIndex;not null"`
	Description string       `json:"description"`
	Permissions []Permission `json:"permissions" gorm:"many2many:role_permissions;"`
	Users       []User       `json:"users" gorm:"many2many:user_roles;"`
}

// Permission represents system permissions
type Permission struct {
	BaseModel
	Name        string `json:"name" gorm:"uniqueIndex;not null"`
	Description string `json:"description"`
	Resource    string `json:"resource"`
	Action      string `json:"action"`
	Roles       []Role `json:"roles" gorm:"many2many:role_permissions;"`
}

// Quest represents a quest/task
type Quest struct {
	BaseModel
	Title            string        `json:"title" gorm:"not null"`
	Description      string        `json:"description"`
	ShortDescription string        `json:"short_description"`
	CategoryID       uuid.UUID     `json:"category_id"`
	Category         QuestCategory `json:"category" gorm:"foreignKey:CategoryID"`
	Type             string        `json:"type" gorm:"default:'individual'"` // individual, collaborative
	Difficulty       string        `json:"difficulty" gorm:"default:'easy'"` // easy, medium, hard, expert
	Status           string        `json:"status" gorm:"default:'draft'"`    // draft, published, archived
	Requirements     string        `json:"requirements" gorm:"type:text"`
	Instructions     string        `json:"instructions" gorm:"type:text"`
	Duration         int           `json:"duration"` // in minutes
	ExperienceReward int           `json:"experience_reward" gorm:"default:100"`
	PointsReward     int           `json:"points_reward" gorm:"default:50"`
	CurrencyReward   int           `json:"currency_reward" gorm:"default:10"`
	ItemRewards      []Item        `json:"item_rewards" gorm:"many2many:quest_item_rewards;"`
	MaxParticipants  int           `json:"max_participants" gorm:"default:1"`
	StartDate        *time.Time    `json:"start_date"`
	EndDate          *time.Time    `json:"end_date"`
	IsRecurring      bool          `json:"is_recurring" gorm:"default:false"`
	RecurrenceRule   string        `json:"recurrence_rule"`
	Steps            []QuestStep   `json:"steps" gorm:"foreignKey:QuestID"`
	Users            []UserQuest   `json:"users" gorm:"foreignKey:QuestID"`
	Tags             []Tag         `json:"tags" gorm:"many2many:quest_tags;"`
	CreatedByID      uuid.UUID     `json:"created_by_id"`
	CreatedBy        User          `json:"created_by" gorm:"foreignKey:CreatedByID"`
}

// QuestCategory represents quest categories
type QuestCategory struct {
	BaseModel
	Name        string  `json:"name" gorm:"uniqueIndex;not null"`
	Description string  `json:"description"`
	Icon        string  `json:"icon"`
	Color       string  `json:"color"`
	Quests      []Quest `json:"quests" gorm:"foreignKey:CategoryID"`
}

// QuestStep represents steps within a quest
type QuestStep struct {
	BaseModel
	QuestID     uuid.UUID `json:"quest_id"`
	Quest       Quest     `json:"quest" gorm:"foreignKey:QuestID"`
	Title       string    `json:"title" gorm:"not null"`
	Description string    `json:"description"`
	Order       int       `json:"order" gorm:"not null"`
	IsRequired  bool      `json:"is_required" gorm:"default:true"`
	Type        string    `json:"type" gorm:"default:'action'"` // action, verification, upload, location
	Metadata    string    `json:"metadata" gorm:"type:text"`    // JSON for step-specific data
}

// UserQuest represents user-quest relationship
type UserQuest struct {
	BaseModel
	UserID       uuid.UUID       `json:"user_id"`
	User         User            `json:"user" gorm:"foreignKey:UserID"`
	QuestID      uuid.UUID       `json:"quest_id"`
	Quest        Quest           `json:"quest" gorm:"foreignKey:QuestID"`
	Status       string          `json:"status" gorm:"default:'not_started'"` // not_started, in_progress, completed, abandoned
	Progress     int             `json:"progress" gorm:"default:0"`           // percentage 0-100
	StartedAt    *time.Time      `json:"started_at"`
	CompletedAt  *time.Time      `json:"completed_at"`
	StepProgress []UserQuestStep `json:"step_progress" gorm:"foreignKey:UserQuestID"`
}

// UserQuestStep represents user progress on quest steps
type UserQuestStep struct {
	BaseModel
	UserQuestID uuid.UUID  `json:"user_quest_id"`
	UserQuest   UserQuest  `json:"user_quest"`
	QuestStepID uuid.UUID  `json:"quest_step_id"`
	QuestStep   QuestStep  `json:"quest_step"`
	IsCompleted bool       `json:"is_completed" gorm:"default:false"`
	CompletedAt *time.Time `json:"completed_at"`
	Data        string     `json:"data" gorm:"type:text"` // JSON for step completion data
}

// Achievement represents achievements/badges
type Achievement struct {
	BaseModel
	Name           string              `json:"name" gorm:"uniqueIndex;not null"`
	Description    string              `json:"description"`
	Icon           string              `json:"icon"`
	Badge          string              `json:"badge"`
	Type           string              `json:"type" gorm:"default:'quest'"` // quest, social, milestone, special
	CategoryID     uuid.UUID           `json:"category_id"`
	Category       AchievementCategory `json:"category" gorm:"foreignKey:CategoryID"`
	Criteria       string              `json:"criteria" gorm:"type:text"` // JSON criteria for earning
	PointsReward   int                 `json:"points_reward" gorm:"default:100"`
	CurrencyReward int                 `json:"currency_reward" gorm:"default:25"`
	ItemRewards    []Item              `json:"item_rewards" gorm:"many2many:achievement_item_rewards;"`
	IsSecret       bool                `json:"is_secret" gorm:"default:false"`
	IsActive       bool                `json:"is_active" gorm:"default:true"`
	Users          []UserAchievement   `json:"users"`
}

// AchievementCategory represents achievement categories
type AchievementCategory struct {
	BaseModel
	Name         string        `json:"name" gorm:"uniqueIndex;not null"`
	Description  string        `json:"description"`
	Icon         string        `json:"icon"`
	Color        string        `json:"color"`
	Achievements []Achievement `json:"achievements" gorm:"foreignKey:CategoryID"`
}

// UserAchievement represents user-achievement relationship
type UserAchievement struct {
	BaseModel
	UserID        uuid.UUID   `json:"user_id"`
	User          User        `json:"user"`
	AchievementID uuid.UUID   `json:"achievement_id"`
	Achievement   Achievement `json:"achievement"`
	UnlockedAt    time.Time   `json:"unlocked_at"`
	IsClaimed     bool        `json:"is_claimed" gorm:"default:false"`
	ClaimedAt     *time.Time  `json:"claimed_at"`
}

// Wallet represents user's virtual currency wallet
type Wallet struct {
	BaseModel
	UserID       uuid.UUID     `json:"user_id" gorm:"uniqueIndex"`
	Balance      int           `json:"balance" gorm:"default:0"`
	Transactions []Transaction `json:"transactions"`
}

// Transaction represents wallet transactions
type Transaction struct {
	BaseModel
	WalletID    uuid.UUID  `json:"wallet_id"`
	Wallet      Wallet     `json:"wallet"`
	Type        string     `json:"type" gorm:"not null"`     // credit, debit
	Category    string     `json:"category" gorm:"not null"` // quest_reward, achievement_reward, purchase, trade
	Amount      int        `json:"amount" gorm:"not null"`
	Description string     `json:"description"`
	ReferenceID *uuid.UUID `json:"reference_id"`              // ID of related quest, achievement, item, etc.
	Metadata    string     `json:"metadata" gorm:"type:text"` // JSON for additional data
}

// Item represents virtual items/rewards
type Item struct {
	BaseModel
	Name             string            `json:"name" gorm:"not null"`
	Description      string            `json:"description"`
	Icon             string            `json:"icon"`
	Image            string            `json:"image"`
	Type             string            `json:"type" gorm:"default:'badge'"`    // badge, avatar, background, special
	Rarity           string            `json:"rarity" gorm:"default:'common'"` // common, rare, epic, legendary
	Value            int               `json:"value" gorm:"default:0"`
	IsTradeable      bool              `json:"is_tradeable" gorm:"default:true"`
	IsActive         bool              `json:"is_active" gorm:"default:true"`
	UserItems        []UserItem        `json:"user_items"`
	MarketplaceItems []MarketplaceItem `json:"marketplace_items"`
}

// UserItem represents user-owned items
type UserItem struct {
	BaseModel
	UserID     uuid.UUID `json:"user_id"`
	User       User      `json:"user"`
	ItemID     uuid.UUID `json:"item_id"`
	Item       Item      `json:"item"`
	Quantity   int       `json:"quantity" gorm:"default:1"`
	AcquiredAt time.Time `json:"acquired_at"`
	IsEquipped bool      `json:"is_equipped" gorm:"default:false"`
}

// MarketplaceItem represents items for sale/trade
type MarketplaceItem struct {
	BaseModel
	SellerID    uuid.UUID  `json:"seller_id"`
	Seller      User       `json:"seller"`
	ItemID      uuid.UUID  `json:"item_id"`
	Item        Item       `json:"item"`
	Quantity    int        `json:"quantity" gorm:"default:1"`
	Price       int        `json:"price" gorm:"not null"`
	Type        string     `json:"type" gorm:"default:'sale'"`     // sale, trade
	Status      string     `json:"status" gorm:"default:'active'"` // active, sold, expired, cancelled
	ExpiresAt   *time.Time `json:"expires_at"`
	Description string     `json:"description"`
}

// Notification represents user notifications
type Notification struct {
	BaseModel
	UserID      uuid.UUID  `json:"user_id"`
	User        User       `json:"user"`
	Type        string     `json:"type" gorm:"not null"` // quest, achievement, social, system
	Title       string     `json:"title" gorm:"not null"`
	Message     string     `json:"message" gorm:"not null"`
	Icon        string     `json:"icon"`
	IsRead      bool       `json:"is_read" gorm:"default:false"`
	ReadAt      *time.Time `json:"read_at"`
	ReferenceID *uuid.UUID `json:"reference_id"`              // ID of related entity
	Metadata    string     `json:"metadata" gorm:"type:text"` // JSON for additional data
}

// Tag represents tags for categorization
type Tag struct {
	BaseModel
	Name   string  `json:"name" gorm:"uniqueIndex;not null"`
	Color  string  `json:"color"`
	Quests []Quest `json:"quests" gorm:"many2many:quest_tags;"`
}

// Leaderboard represents leaderboard entries
type Leaderboard struct {
	BaseModel
	UserID   uuid.UUID `json:"user_id"`
	User     User      `json:"user"`
	Type     string    `json:"type" gorm:"not null"` // overall, weekly, monthly, quest_specific
	Period   string    `json:"period"`               // 2024-W1, 2024-01, etc.
	Score    int       `json:"score" gorm:"not null"`
	Rank     int       `json:"rank" gorm:"not null"`
	Metadata string    `json:"metadata" gorm:"type:text"` // JSON for additional data
}

// FriendRequest represents friend requests
type FriendRequest struct {
	BaseModel
	RequesterID uuid.UUID  `json:"requester_id"`
	Requester   User       `json:"requester"`
	RequesteeID uuid.UUID  `json:"requestee_id"`
	Requestee   User       `json:"requestee"`
	Status      string     `json:"status" gorm:"default:'pending'"` // pending, accepted, declined
	Message     string     `json:"message"`
	ResponsedAt *time.Time `json:"responsed_at"`
}

// UserRole represents the many-to-many relationship between users and roles
type UserRole struct {
	UserID     uuid.UUID `json:"user_id" gorm:"primaryKey"`
	RoleID     uuid.UUID `json:"role_id" gorm:"primaryKey"`
	AssignedAt time.Time `json:"assigned_at" gorm:"autoCreateTime"`
}

// RolePermission represents the many-to-many relationship between roles and permissions
type RolePermission struct {
	RoleID       uuid.UUID `json:"role_id" gorm:"primaryKey"`
	PermissionID uuid.UUID `json:"permission_id" gorm:"primaryKey"`
	AssignedAt   time.Time `json:"assigned_at" gorm:"autoCreateTime"`
}

// UserStats represents aggregated user statistics
type UserStats struct {
	BaseModel
	UserID            uuid.UUID  `json:"user_id" gorm:"uniqueIndex"`
	TotalQuests       int        `json:"total_quests" gorm:"default:0"`
	CompletedQuests   int        `json:"completed_quests" gorm:"default:0"`
	TotalAchievements int        `json:"total_achievements" gorm:"default:0"`
	TotalExperience   int        `json:"total_experience" gorm:"default:0"`
	TotalPoints       int        `json:"total_points" gorm:"default:0"`
	CurrentLevel      int        `json:"current_level" gorm:"default:1"`
	QuestSuccessRate  float64    `json:"quest_success_rate" gorm:"default:0"`
	AverageQuestTime  int        `json:"average_quest_time" gorm:"default:0"` // in minutes
	LastActivityAt    *time.Time `json:"last_activity_at"`
}
