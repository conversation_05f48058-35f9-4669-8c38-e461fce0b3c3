# Quester Frontend

A modular Flutter application for the Quester project, built with modern Flutter architecture patterns and best practices.

## 🏗️ Architecture

### State Management
- **Riverpod** for state management and dependency injection
- **Provider pattern** for business logic separation

### Navigation
- **GoRouter** for declarative routing and navigation
- Type-safe route definitions with constants
- Shell routes for bottom navigation

### Local Storage
- **Hive** for local data storage
- **Shared Preferences** for app settings
- **Flutter Secure Storage** for sensitive data

### UI Framework
- **Material Design 3** with custom theming
- **Google Fonts** for typography
- **Custom widgets** for consistent UI components

## 📁 Project Structure

```
lib/
├── core/                           # Core functionality
│   ├── constants/                  # App-wide constants
│   │   └── app_constants.dart     # Route definitions, app info
│   ├── models/                     # Data models with JSON serialization
│   │   ├── user_model.dart
│   │   ├── quest_model.dart
│   │   ├── achievement_model.dart
│   │   ├── marketplace_item_model.dart
│   │   └── wallet_model.dart
│   ├── services/                   # Core services
│   │   ├── api_service.dart       # HTTP client and API endpoints
│   │   ├── auth_service.dart      # Authentication logic
│   │   ├── storage_service.dart   # Local storage wrapper
│   │   └── app_router.dart        # Navigation configuration
│   └── theme/                      # App theming
│       └── app_theme.dart         # Light/dark themes, colors
├── features/                       # Feature modules
│   ├── authentication/            # Login, register, forgot password
│   ├── dashboard/                  # Main dashboard and navigation
│   ├── quests/                     # Quest listing and details
│   ├── achievements/               # Achievement tracking
│   ├── wallet/                     # Wallet and transactions
│   ├── marketplace/                # Item marketplace
│   ├── notifications/              # Notification management
│   ├── leaderboard/               # User rankings
│   ├── profile/                    # User profile management
│   └── settings/                   # App settings
└── shared/                         # Shared components
    └── widgets/                    # Reusable UI widgets
        ├── custom_button.dart
        ├── custom_text_field.dart
        ├── custom_card.dart
        ├── loading_overlay.dart
        ├── achievement_badge.dart
        └── quest_card.dart
```

## 🚀 Features Implemented

### ✅ Core Architecture
- [x] Modular project structure
- [x] Riverpod state management setup
- [x] GoRouter navigation with shell routes
- [x] Hive local storage integration
- [x] Material Design 3 theming
- [x] JSON serialization with code generation

### ✅ Authentication Module
- [x] Login page with form validation
- [x] Registration page with password confirmation
- [x] Forgot password flow
- [x] Splash screen with app branding
- [x] Auth provider for state management

### ✅ Dashboard & Navigation
- [x] Bottom navigation with 5 main tabs
- [x] Dashboard with overview cards
- [x] Navigation state management

### ✅ Feature Pages
- [x] Quest listing and detail pages
- [x] Achievement tracking page
- [x] Wallet management page
- [x] Marketplace with search and filters
- [x] Notifications page
- [x] Leaderboard with rankings
- [x] User profile page
- [x] Settings page

### ✅ Shared Components
- [x] Custom input fields with validation
- [x] Reusable buttons with loading states
- [x] Card components with consistent styling
- [x] Achievement badges with animations
- [x] Quest cards with progress indicators
- [x] Loading overlays

### ✅ Models & Providers
- [x] User model with preferences
- [x] Quest model with categories and progress
- [x] Achievement model with tiers
- [x] Marketplace item model
- [x] Wallet model with transactions
- [x] Riverpod providers for each feature

## 🛠️ Development Status

### Build Status
- ✅ **Flutter Analyze**: Passing (only info-level warnings)
- ✅ **Unit Tests**: Basic smoke test passing
- ✅ **Web Build**: Successfully compiles
- ✅ **Code Generation**: All models have generated serialization

### Current Warnings
- ℹ️ Deprecated `withOpacity` usage (non-breaking, can be updated gradually)
- ℹ️ `prefer_const_constructors` optimizations (performance improvements)

## 🔄 Pending Implementation

### Backend Integration
- [ ] Connect API service to actual backend endpoints
- [ ] Implement real authentication flow
- [ ] Add error handling for network requests
- [ ] Implement data synchronization

### Enhanced Features
- [ ] Push notifications
- [ ] Offline support with data caching
- [ ] Image upload and optimization
- [ ] Real-time features (WebSocket)
- [ ] Internationalization (i18n)

### Testing & Quality
- [ ] Comprehensive unit tests
- [ ] Widget tests for pages
- [ ] Integration tests
- [ ] Performance optimization

## 🧪 Running the Project

### Prerequisites
- Flutter SDK (latest stable)
- Dart SDK (included with Flutter)
- Web browser (for web development)

### Commands

```bash
# Install dependencies
flutter pub get

# Generate code (for models)
flutter packages pub run build_runner build

# Run development server
flutter run -d web-server --web-port=8080

# Run tests
flutter test

# Build for production
flutter build web

# Analyze code
flutter analyze
```

### Docker Development
Use the provided Docker tasks in VS Code:
- `Docker: Build Frontend Development`
- `Docker: Build Frontend Production`

## 📱 Supported Platforms
- ✅ **Web** (primary target)
- ✅ **Android** (configured)
- ✅ **iOS** (configured)
- ✅ **Windows** (configured)
- ✅ **macOS** (configured)
- ✅ **Linux** (configured)

## 🎨 UI/UX Features
- Material Design 3 with custom color scheme
- Dark/light theme support
- Responsive design for web and mobile
- Smooth animations and transitions
- Consistent spacing and typography
- Loading states and error handling
- Form validation with user feedback

## 📚 Dependencies

### Core Dependencies
- `flutter_riverpod`: State management
- `go_router`: Navigation
- `hive_flutter`: Local storage
- `google_fonts`: Typography
- `json_annotation`: Serialization

### UI & Graphics
- `flutter_svg`: SVG support
- `cached_network_image`: Image caching
- `lottie`: Animations
- `fl_chart`: Charts and graphs
- `shimmer`: Loading effects

### Utilities
- `shared_preferences`: Simple storage
- `url_launcher`: External links
- `device_info_plus`: Device information
- `package_info_plus`: App information

## 🤝 Contributing

When adding new features:
1. Follow the modular structure pattern
2. Add models with JSON serialization
3. Create Riverpod providers for state management
4. Include error handling and loading states
5. Add basic tests for new functionality
6. Update this README with new features

## 📄 License

Part of the Quester project. See main project documentation for license details.
