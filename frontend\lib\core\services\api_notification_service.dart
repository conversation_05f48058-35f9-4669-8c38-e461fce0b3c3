import '../models/notification_model.dart';
import '../models/notification_settings_model.dart';
import 'base_api_service.dart';

class ApiNotificationService extends BaseApiService {
  ApiNotificationService(super.dio);

  // Get all notifications with filtering and pagination
  Future<List<Notification>> getNotifications({
    NotificationType? type,
    NotificationPriority? priority,
    bool? isRead,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (type != null) queryParams['type'] = type.name;
      if (priority != null) queryParams['priority'] = priority.name;
      if (isRead != null) queryParams['is_read'] = isRead;

      final response = await dio.get('/notifications', queryParameters: queryParams);
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => Notification.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get notification by ID
  Future<Notification> getNotification(String id) async {
    try {
      final response = await dio.get('/notifications/$id');
      return Notification.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get unread notifications count
  Future<int> getUnreadNotificationsCount() async {
    try {
      final response = await dio.get('/notifications/unread/count');
      return response.data['count'] ?? response.data['data']['count'] ?? 0;
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get unread notifications
  Future<List<Notification>> getUnreadNotifications({int limit = 20}) async {
    try {
      final response = await dio.get('/notifications', queryParameters: {
        'is_read': false,
        'limit': limit,
      });
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => Notification.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Mark notification as read
  Future<Notification> markAsRead(String notificationId) async {
    try {
      final response = await dio.put('/notifications/$notificationId/read');
      return Notification.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      await dio.put('/notifications/read/all');
    } catch (e) {
      throw handleError(e);
    }
  }

  // Delete notification
  Future<void> deleteNotification(String notificationId) async {
    try {
      await dio.delete('/notifications/$notificationId');
    } catch (e) {
      throw handleError(e);
    }
  }

  // Delete all notifications
  Future<void> deleteAllNotifications() async {
    try {
      await dio.delete('/notifications/all');
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get notification settings
  Future<NotificationSettings> getNotificationSettings() async {
    try {
      final response = await dio.get('/user/notification-settings');
      return NotificationSettings.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Update notification settings
  Future<NotificationSettings> updateNotificationSettings({
    bool? pushEnabled,
    bool? emailEnabled,
    bool? questNotifications,
    bool? achievementNotifications,
    bool? socialNotifications,
    bool? marketplaceNotifications,
    bool? reminderNotifications,
    bool? promotionNotifications,
    String? quietHoursStart,
    String? quietHoursEnd,
  }) async {
    try {
      final data = <String, dynamic>{};

      if (pushEnabled != null) data['push_enabled'] = pushEnabled;
      if (emailEnabled != null) data['email_enabled'] = emailEnabled;
      if (questNotifications != null) data['quest_notifications'] = questNotifications;
      if (achievementNotifications != null) data['achievement_notifications'] = achievementNotifications;
      if (socialNotifications != null) data['social_notifications'] = socialNotifications;
      if (marketplaceNotifications != null) data['marketplace_notifications'] = marketplaceNotifications;
      if (reminderNotifications != null) data['reminder_notifications'] = reminderNotifications;
      if (promotionNotifications != null) data['promotion_notifications'] = promotionNotifications;
      if (quietHoursStart != null) data['quiet_hours_start'] = quietHoursStart;
      if (quietHoursEnd != null) data['quiet_hours_end'] = quietHoursEnd;

      final response = await dio.put('/user/notification-settings', data: data);
      return NotificationSettings.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }
}
