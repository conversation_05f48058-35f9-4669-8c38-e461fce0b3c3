#!/bin/bash
set -e

echo "Starting Flutter development server..."
echo "Working directory: $(pwd)"
echo "Flutter version: $(flutter --version)"
echo "Port: $FLUTTER_WEB_PORT"

# Ensure dependencies are up to date
flutter pub get

# Start Flutter web server
echo "Starting Flutter web server on port $FLUTTER_WEB_PORT..."
flutter run -d web-server --web-port=$FLUTTER_WEB_PORT --web-hostname=0.0.0.0 --hot
