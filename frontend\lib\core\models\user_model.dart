import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

@JsonSerializable()
class User {
  final String id;
  final String email;
  final String username;
  @Json<PERSON><PERSON>(name: 'first_name')
  final String firstName;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_name')
  final String lastName;
  final String? avatar;
  final String? bio;
  final UserRole role;
  final UserStatus status;
  final int level;
  final int experience;
  final int points;
  @<PERSON>sonKey(name: 'total_points')
  final int totalPoints;
  @<PERSON>sonKey(name: 'total_quest_completed')
  final int totalQuestCompleted;
  @<PERSON>sonKey(name: 'total_achievements')
  final int totalAchievements;
  final List<String> permissions;
  @<PERSON>son<PERSON><PERSON>(name: 'is_active')
  final bool isActive;
  @Json<PERSON>ey(name: 'is_verified')
  final bool isVerified;
  @<PERSON>son<PERSON>ey(name: 'last_login_at')
  final DateTime? lastLoginAt;
  @<PERSON>son<PERSON>ey(name: 'created_at')
  final DateTime createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final DateTime updatedAt;

  const User({
    required this.id,
    required this.email,
    required this.username,
    required this.firstName,
    required this.lastName,
    this.avatar,
    this.bio,
    required this.role,
    required this.status,
    required this.level,
    required this.experience,
    required this.points,
    required this.totalPoints,
    required this.totalQuestCompleted,
    required this.totalAchievements,
    required this.permissions,
    required this.isActive,
    required this.isVerified,
    this.lastLoginAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  User copyWith({
    String? id,
    String? email,
    String? username,
    String? firstName,
    String? lastName,
    String? avatar,
    String? bio,
    UserRole? role,
    UserStatus? status,
    int? level,
    int? experience,
    int? points,
    int? totalPoints,
    int? totalQuestCompleted,
    int? totalAchievements,
    List<String>? permissions,
    bool? isActive,
    bool? isVerified,
    DateTime? lastLoginAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      username: username ?? this.username,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      avatar: avatar ?? this.avatar,
      bio: bio ?? this.bio,
      role: role ?? this.role,
      status: status ?? this.status,
      level: level ?? this.level,
      experience: experience ?? this.experience,
      points: points ?? this.points,
      totalPoints: totalPoints ?? this.totalPoints,
      totalQuestCompleted: totalQuestCompleted ?? this.totalQuestCompleted,
      totalAchievements: totalAchievements ?? this.totalAchievements,
      permissions: permissions ?? this.permissions,
      isActive: isActive ?? this.isActive,
      isVerified: isVerified ?? this.isVerified,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper getters
  String get fullName => '$firstName $lastName';
  String get displayName => username.isNotEmpty ? username : fullName;
  bool get hasAvatar => avatar != null && avatar!.isNotEmpty;
}

@JsonEnum()
enum UserRole {
  @JsonValue('user')
  user,
  @JsonValue('admin')
  admin,
  @JsonValue('moderator')
  moderator,
  @JsonValue('developer')
  developer,
}

@JsonEnum()
enum UserStatus {
  @JsonValue('active')
  active,
  @JsonValue('inactive')
  inactive,
  @JsonValue('suspended')
  suspended,
  @JsonValue('banned')
  banned,
}

extension UserRoleExtension on UserRole {
  String get displayName {
    switch (this) {
      case UserRole.user:
        return 'User';
      case UserRole.admin:
        return 'Admin';
      case UserRole.moderator:
        return 'Moderator';
      case UserRole.developer:
        return 'Developer';
    }
  }

  bool get isAdmin => this == UserRole.admin || this == UserRole.developer;
  bool get isModerator => this == UserRole.moderator || isAdmin;
}

extension UserStatusExtension on UserStatus {
  String get displayName {
    switch (this) {
      case UserStatus.active:
        return 'Active';
      case UserStatus.inactive:
        return 'Inactive';
      case UserStatus.suspended:
        return 'Suspended';
      case UserStatus.banned:
        return 'Banned';
    }
  }

  bool get isActive => this == UserStatus.active;
}
