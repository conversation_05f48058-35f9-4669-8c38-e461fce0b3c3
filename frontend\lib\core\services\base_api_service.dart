import 'package:dio/dio.dart';

/// Base API service class that provides common functionality for all API services
abstract class BaseApiService {
  final Dio dio;
  
  BaseApiService(this.dio);

  /// Handle errors from API calls and convert them to user-friendly exceptions
  Exception handleError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return Exception('Connection timeout. Please check your internet connection.');
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          final message = error.response?.data?['message'] ?? 'Server error occurred';
          return Exception('Server error ($statusCode): $message');
        case DioExceptionType.cancel:
          return Exception('Request was cancelled');
        case DioExceptionType.unknown:
          return Exception('Network error occurred');
        default:
          return Exception('An unexpected error occurred');
      }
    }
    return Exception('An unexpected error occurred: $error');
  }

  /// Get data from response, handling both wrapped and unwrapped responses
  T getDataFromResponse<T>(Response response, T Function(dynamic) fromJson) {
    final data = response.data['data'] ?? response.data;
    return fromJson(data);
  }

  /// Get list data from response, handling both wrapped and unwrapped responses
  List<T> getListFromResponse<T>(Response response, T Function(Map<String, dynamic>) fromJson) {
    final data = response.data['data'] ?? response.data;
    if (data is List) {
      return data.map((item) => fromJson(item as Map<String, dynamic>)).toList();
    }
    return [];
  }

  /// Build query parameters for pagination
  Map<String, dynamic> buildPaginationParams({
    int page = 1,
    int limit = 20,
    Map<String, dynamic>? additionalParams,
  }) {
    final params = <String, dynamic>{
      'page': page,
      'limit': limit,
    };
    
    if (additionalParams != null) {
      params.addAll(additionalParams);
    }
    
    return params;
  }
}
