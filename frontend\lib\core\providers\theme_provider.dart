import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';

import '../theme/app_theme.dart';

/// Theme mode enum for better type safety
enum AppThemeMode {
  light,
  dark,
  system,
}

extension AppThemeModeExtension on AppThemeMode {
  ThemeMode get themeMode {
    switch (this) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
      case AppThemeMode.system:
        return ThemeMode.system;
    }
  }

  String get displayName {
    switch (this) {
      case AppThemeMode.light:
        return 'Light';
      case AppThemeMode.dark:
        return 'Dark';
      case AppThemeMode.system:
        return 'System';
    }
  }

  IconData get icon {
    switch (this) {
      case AppThemeMode.light:
        return Icons.light_mode;
      case AppThemeMode.dark:
        return Icons.dark_mode;
      case AppThemeMode.system:
        return Icons.brightness_auto;
    }
  }
}

/// Theme state class
class ThemeState {
  final AppThemeMode themeMode;
  final bool isDarkMode;
  final bool isSystemMode;

  const ThemeState({
    required this.themeMode,
    required this.isDarkMode,
    required this.isSystemMode,
  });

  ThemeState copyWith({
    AppThemeMode? themeMode,
    bool? isDarkMode,
    bool? isSystemMode,
  }) {
    return ThemeState(
      themeMode: themeMode ?? this.themeMode,
      isDarkMode: isDarkMode ?? this.isDarkMode,
      isSystemMode: isSystemMode ?? this.isSystemMode,
    );
  }
}

/// Theme notifier for managing theme state
class ThemeNotifier extends StateNotifier<ThemeState> {
  static const String _themeKey = 'app_theme_mode';
  late Box _box;

  ThemeNotifier() : super(const ThemeState(
    themeMode: AppThemeMode.system,
    isDarkMode: false,
    isSystemMode: true,
  )) {
    _initializeTheme();
  }

  /// Initialize theme from storage
  Future<void> _initializeTheme() async {
    try {
      _box = await Hive.openBox('theme_settings');
      final savedTheme = _box.get(_themeKey, defaultValue: 'system');
      
      final themeMode = _parseThemeMode(savedTheme);
      final isDarkMode = _calculateIsDarkMode(themeMode);
      
      state = ThemeState(
        themeMode: themeMode,
        isDarkMode: isDarkMode,
        isSystemMode: themeMode == AppThemeMode.system,
      );
    } catch (e) {
      debugPrint('Failed to initialize theme: $e');
    }
  }

  /// Parse theme mode from string
  AppThemeMode _parseThemeMode(String value) {
    switch (value) {
      case 'light':
        return AppThemeMode.light;
      case 'dark':
        return AppThemeMode.dark;
      case 'system':
      default:
        return AppThemeMode.system;
    }
  }

  /// Calculate if dark mode should be active
  bool _calculateIsDarkMode(AppThemeMode themeMode) {
    switch (themeMode) {
      case AppThemeMode.light:
        return false;
      case AppThemeMode.dark:
        return true;
      case AppThemeMode.system:
        // This will be updated by the system brightness listener
        return WidgetsBinding.instance.platformDispatcher.platformBrightness == Brightness.dark;
    }
  }

  /// Set theme mode
  Future<void> setThemeMode(AppThemeMode themeMode) async {
    try {
      await _box.put(_themeKey, themeMode.name);
      
      state = ThemeState(
        themeMode: themeMode,
        isDarkMode: _calculateIsDarkMode(themeMode),
        isSystemMode: themeMode == AppThemeMode.system,
      );
    } catch (e) {
      debugPrint('Failed to save theme: $e');
    }
  }

  /// Toggle between light and dark mode
  Future<void> toggleTheme() async {
    final newMode = state.themeMode == AppThemeMode.light 
        ? AppThemeMode.dark 
        : AppThemeMode.light;
    await setThemeMode(newMode);
  }

  /// Update system brightness (called when system theme changes)
  void updateSystemBrightness(Brightness brightness) {
    if (state.isSystemMode) {
      state = state.copyWith(
        isDarkMode: brightness == Brightness.dark,
      );
    }
  }

  /// Get current theme data
  ThemeData get currentTheme {
    return state.isDarkMode ? AppTheme.darkTheme : AppTheme.lightTheme;
  }

  /// Get current theme mode for MaterialApp
  ThemeMode get materialThemeMode => state.themeMode.themeMode;
}

/// Theme provider
final themeProvider = StateNotifierProvider<ThemeNotifier, ThemeState>((ref) {
  return ThemeNotifier();
});

/// Current theme data provider
final currentThemeProvider = Provider<ThemeData>((ref) {
  final themeNotifier = ref.watch(themeProvider.notifier);
  return themeNotifier.currentTheme;
});

/// Is dark mode provider
final isDarkModeProvider = Provider<bool>((ref) {
  return ref.watch(themeProvider).isDarkMode;
});

/// Theme mode provider
final themeModeProvider = Provider<ThemeMode>((ref) {
  return ref.watch(themeProvider).themeMode.themeMode;
});
