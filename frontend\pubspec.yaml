name: quester_frontend
description: "Quester - Gamified Quest Management Platform Frontend"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.0

dependencies:
  flutter:
    sdk: flutter

  # Core dependencies
  cupertino_icons: ^1.0.8
  
  # HTTP client and networking
  dio: ^5.4.0
  web_socket_channel: ^2.4.0
  
  # State management
  provider: ^6.1.1
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9
  
  # Navigation
  go_router: ^13.2.0
  
  # UI components and styling
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  lottie: ^3.0.0
  
  # Local storage and preferences
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
    # Authentication and security
  flutter_secure_storage: ^9.0.0
  crypto: ^3.0.3
  
  # JSON serialization
  json_annotation: ^4.9.0
  
  # Utils and helpers
  uuid: ^4.3.3
  url_launcher: ^6.2.4
  
  # Animations and transitions
  flutter_staggered_animations: ^1.1.1
  shimmer: ^3.0.0
  # Forms and validation
  email_validator: ^2.1.17
  
  # Image handling
  image_picker: ^1.0.7
  flutter_image_compress: ^2.1.0
  
  # Charts and data visualization
  fl_chart: ^0.66.0
  
  # Platform specific
  device_info_plus: ^9.1.2
  package_info_plus: ^5.0.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  hive_generator: ^2.0.1
  build_runner: ^2.4.7
  json_serializable: ^6.7.1

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/
  
  # fonts:
  #   - family: Quester
  #     fonts:
  #       - asset: assets/fonts/Quester-Regular.ttf
  #       - asset: assets/fonts/Quester-Bold.ttf
  #         weight: 700
