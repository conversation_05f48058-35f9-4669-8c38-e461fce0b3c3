import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/providers/websocket_provider.dart';

/// Enhanced WebSocket connection status indicator with animations
class WebSocketStatusIndicator extends ConsumerStatefulWidget {
  final bool showLabel;
  final bool showIcon;
  final EdgeInsetsGeometry? padding;
  final bool showPulse;

  const WebSocketStatusIndicator({
    super.key,
    this.showLabel = true,
    this.showIcon = true,
    this.padding,
    this.showPulse = true,
  });

  @override
  ConsumerState<WebSocketStatusIndicator> createState() => _WebSocketStatusIndicatorState();
}

class _WebSocketStatusIndicatorState extends ConsumerState<WebSocketStatusIndicator>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _fadeController.forward();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _updateAnimations(WebSocketConnectionState state) {
    switch (state) {
      case WebSocketConnectionState.connecting:
      case WebSocketConnectionState.reconnecting:
        if (widget.showPulse) {
          _pulseController.repeat(reverse: true);
        }
        break;
      case WebSocketConnectionState.connected:
      case WebSocketConnectionState.disconnected:
      case WebSocketConnectionState.error:
        _pulseController.stop();
        _pulseController.reset();
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    final connectionState = ref.watch(webSocketManagerProvider);
    final statusText = ref.watch(webSocketStatusProvider);
    final statusColor = ref.watch(webSocketStatusColorProvider);

    // Update animations based on connection state
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateAnimations(connectionState);
    });

    return FadeTransition(
      opacity: _fadeAnimation,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        padding: widget.padding ?? const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: statusColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: statusColor.withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: connectionState == WebSocketConnectionState.connected
              ? [
                  BoxShadow(
                    color: statusColor.withValues(alpha: 0.2),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (widget.showIcon) ...[
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: (connectionState == WebSocketConnectionState.connecting ||
                            connectionState == WebSocketConnectionState.reconnecting)
                        ? _pulseAnimation.value
                        : 1.0,
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: statusColor,
                        boxShadow: connectionState == WebSocketConnectionState.connected
                            ? [
                                BoxShadow(
                                  color: statusColor.withValues(alpha: 0.5),
                                  blurRadius: 4,
                                  spreadRadius: 1,
                                ),
                              ]
                            : null,
                      ),
                    ),
                  );
                },
              ),
              if (widget.showLabel) const SizedBox(width: 6),
            ],
            if (widget.showLabel)
              AnimatedDefaultTextStyle(
                duration: const Duration(milliseconds: 300),
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: statusColor,
                      fontWeight: FontWeight.w500,
                    ) ?? TextStyle(color: statusColor),
                child: Text(statusText),
              ),
          ],
        ),
      ),
    );
  }
}

/// Detailed WebSocket status card
class WebSocketStatusCard extends ConsumerWidget {
  const WebSocketStatusCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectionState = ref.watch(webSocketManagerProvider);
    final statusColor = ref.watch(webSocketStatusColorProvider);
    final manager = ref.read(webSocketManagerProvider.notifier);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getStatusIcon(connectionState),
                  color: statusColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Real-time Connection',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Spacer(),
                WebSocketStatusIndicator(
                  showIcon: true,
                  showLabel: true,
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _getStatusDescription(connectionState),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            if (connectionState == WebSocketConnectionState.disconnected ||
                connectionState == WebSocketConnectionState.error) ...[
              const SizedBox(height: 12),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () => manager.connect(),
                  icon: const Icon(Icons.refresh, size: 18),
                  label: const Text('Reconnect'),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  IconData _getStatusIcon(WebSocketConnectionState state) {
    switch (state) {
      case WebSocketConnectionState.disconnected:
        return Icons.cloud_off;
      case WebSocketConnectionState.connecting:
      case WebSocketConnectionState.reconnecting:
        return Icons.cloud_sync;
      case WebSocketConnectionState.connected:
        return Icons.cloud_done;
      case WebSocketConnectionState.error:
        return Icons.error_outline;
    }
  }

  String _getStatusDescription(WebSocketConnectionState state) {
    switch (state) {
      case WebSocketConnectionState.disconnected:
        return 'Real-time updates are currently unavailable. You may not receive live notifications or updates.';
      case WebSocketConnectionState.connecting:
        return 'Establishing connection for real-time updates...';
      case WebSocketConnectionState.connected:
        return 'Connected! You\'ll receive real-time notifications, quest updates, and achievements.';
      case WebSocketConnectionState.reconnecting:
        return 'Reconnecting to restore real-time functionality...';
      case WebSocketConnectionState.error:
        return 'Connection failed. Some features may not work properly. Please check your internet connection.';
    }
  }
}

/// Floating WebSocket status indicator
class FloatingWebSocketStatus extends ConsumerWidget {
  const FloatingWebSocketStatus({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectionState = ref.watch(webSocketManagerProvider);
    
    // Only show when disconnected or error
    if (connectionState == WebSocketConnectionState.connected) {
      return const SizedBox.shrink();
    }

    final statusColor = ref.watch(webSocketStatusColorProvider);
    final manager = ref.read(webSocketManagerProvider.notifier);

    return Positioned(
      top: MediaQuery.of(context).padding.top + 8,
      left: 16,
      right: 16,
      child: Material(
        elevation: 4,
        borderRadius: BorderRadius.circular(8),
        color: statusColor,
        child: InkWell(
          onTap: connectionState == WebSocketConnectionState.disconnected ||
                 connectionState == WebSocketConnectionState.error
              ? () => manager.connect()
              : null,
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Icon(
                  connectionState == WebSocketConnectionState.error
                      ? Icons.error_outline
                      : Icons.cloud_off,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    connectionState == WebSocketConnectionState.error
                        ? 'Connection error - Tap to retry'
                        : 'Offline - Tap to reconnect',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (connectionState == WebSocketConnectionState.connecting ||
                    connectionState == WebSocketConnectionState.reconnecting)
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// App bar WebSocket status
class AppBarWebSocketStatus extends ConsumerWidget {
  const AppBarWebSocketStatus({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectionState = ref.watch(webSocketManagerProvider);
    
    // Only show when not connected
    if (connectionState == WebSocketConnectionState.connected) {
      return const SizedBox.shrink();
    }

    return WebSocketStatusIndicator(
      showLabel: false,
      showIcon: true,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    );
  }
}
