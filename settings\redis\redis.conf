# Redis configuration file

# General
daemonize no
bind 0.0.0.0
port 6379
timeout 0
loglevel notice

# Memory management
maxmemory 512mb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# Security
protected-mode yes
# requirepass yourpassword  # Uncomment and change for production

# Persistence
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data/cache

# Advanced config
tcp-keepalive 300
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec