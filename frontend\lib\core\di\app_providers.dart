import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dio/dio.dart';

import '../config/app_config.dart';
import '../models/user_model.dart';
import '../services/api_service.dart';
import '../services/auth_service.dart';
import '../services/storage_service.dart';

import '../logging/app_logger.dart';

/// Dependency injection providers for the application
/// This centralizes all service providers for better maintainability

// Core Services
final storageServiceProvider = Provider<StorageService>((ref) {
  return StorageService();
});

final dioProvider = Provider<Dio>((ref) {
  final dio = Dio(BaseOptions(
    baseUrl: AppConfig.baseUrl,
    connectTimeout: AppConfig.apiTimeout,
    receiveTimeout: AppConfig.apiTimeout,
    sendTimeout: AppConfig.apiTimeout,
  ));

  // Add interceptors
  dio.interceptors.add(LogInterceptor(
    requestBody: true,
    responseBody: true,
    logPrint: (object) => AppLogger.debug(object.toString(), tag: 'DIO'),
  ));

  return dio;
});

final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService();
});

final apiServiceProvider = Provider<ApiService>((ref) {
  final authService = ref.watch(authServiceProvider);
  return ApiService(authService);
});

// WebSocket and Notification services are provided by their respective modules

// Repository Providers (for future use)
// These would be used when implementing repository pattern

// final userRepositoryProvider = Provider<UserRepository>((ref) {
//   final apiService = ref.watch(apiServiceProvider);
//   return UserRepository(apiService);
// });

// final questRepositoryProvider = Provider<QuestRepository>((ref) {
//   final apiService = ref.watch(apiServiceProvider);
//   return QuestRepository(apiService);
// });

// final achievementRepositoryProvider = Provider<AchievementRepository>((ref) {
//   final apiService = ref.watch(apiServiceProvider);
//   return AchievementRepository(apiService);
// });

// final marketplaceRepositoryProvider = Provider<MarketplaceRepository>((ref) {
//   final apiService = ref.watch(apiServiceProvider);
//   return MarketplaceRepository(apiService);
// });

// final notificationRepositoryProvider = Provider<NotificationRepository>((ref) {
//   final apiService = ref.watch(apiServiceProvider);
//   return NotificationRepository(apiService);
// });

// final walletRepositoryProvider = Provider<WalletRepository>((ref) {
//   final apiService = ref.watch(apiServiceProvider);
//   return WalletRepository(apiService);
// });

/// Provider for app initialization status
final appInitializationProvider = FutureProvider<bool>((ref) async {
  try {
    AppLogger.info('Initializing application...');
    
    // Initialize storage
    await StorageService.init();
    
    // Initialize other services as needed
    AppLogger.info('Application initialized successfully');
    return true;
  } catch (error, stackTrace) {
    AppLogger.error('Failed to initialize application', error: error, stackTrace: stackTrace);
    return false;
  }
});

/// Provider for checking if user is authenticated
final isAuthenticatedProvider = FutureProvider<bool>((ref) async {
  final authService = ref.watch(authServiceProvider);
  return await authService.isAuthenticated();
});

/// Provider for current user
final currentUserProvider = FutureProvider<User?>((ref) async {
  final authService = ref.watch(authServiceProvider);
  return await authService.getStoredUser();
});
