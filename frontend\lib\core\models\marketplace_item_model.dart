import 'package:json_annotation/json_annotation.dart';

part 'marketplace_item_model.g.dart';

@JsonSerializable()
class MarketplaceItem {
  final String id;
  final String name;
  final String description;
  final String? imageUrl;
  final String? videoUrl;
  final MarketplaceItemCategory category;
  final MarketplaceItemRarity rarity;
  final int price;
  final String currency; // 'points', 'coins', 'premium'
  final String sellerId;
  final String sellerName;
  final DateTime createdAt;
  final DateTime updatedAt;
  final MarketplaceItemStatus status;
  final int quantity;
  final int maxQuantity;
  final List<String> tags;
  final Map<String, dynamic>? metadata;
  final bool isLimited;
  final DateTime? limitedUntil;
  final double? discountPercentage;
  final int? originalPrice;

  const MarketplaceItem({
    required this.id,
    required this.name,
    required this.description,
    this.imageUrl,
    this.videoUrl,
    required this.category,
    required this.rarity,
    required this.price,
    required this.currency,
    required this.sellerId,
    required this.sellerName,
    required this.createdAt,
    required this.updatedAt,
    required this.status,
    required this.quantity,
    required this.maxQuantity,
    required this.tags,
    this.metadata,
    required this.isLimited,
    this.limitedUntil,
    this.discountPercentage,
    this.originalPrice,  });
  
  factory MarketplaceItem.fromJson(Map<String, dynamic> json) => _$MarketplaceItemFromJson(json);  Map<String, dynamic> toJson() => _$MarketplaceItemToJson(this);

  bool get isOnSale => discountPercentage != null && discountPercentage! > 0;
  bool get isAvailable => status == MarketplaceItemStatus.available && quantity > 0;
  bool get isExpired => limitedUntil != null && limitedUntil!.isBefore(DateTime.now());
}

@JsonSerializable()
class MarketplacePurchase {
  final String id;
  final String userId;
  final String itemId;
  final MarketplaceItem item;
  final int quantity;
  final int totalPrice;
  final String currency;
  final DateTime purchasedAt;
  final MarketplacePurchaseStatus status;
  final String? transactionId;

  const MarketplacePurchase({
    required this.id,
    required this.userId,
    required this.itemId,
    required this.item,
    required this.quantity,
    required this.totalPrice,
    required this.currency,
    required this.purchasedAt,
    required this.status,    this.transactionId,
  });
  
  factory MarketplacePurchase.fromJson(Map<String, dynamic> json) => _$MarketplacePurchaseFromJson(json);
  Map<String, dynamic> toJson() => _$MarketplacePurchaseToJson(this);
}

@JsonEnum()
enum MarketplaceItemCategory {
  @JsonValue('avatar')
  avatar,
  @JsonValue('badge')
  badge,
  @JsonValue('consumable')
  consumable,
  @JsonValue('decoration')
  decoration,
  @JsonValue('boost')
  boost,
  @JsonValue('theme')
  theme,
  @JsonValue('special')
  special,
}

@JsonEnum()
enum MarketplaceItemRarity {
  @JsonValue('common')
  common,
  @JsonValue('uncommon')
  uncommon,
  @JsonValue('rare')
  rare,
  @JsonValue('epic')
  epic,
  @JsonValue('legendary')
  legendary,
}

@JsonEnum()
enum MarketplaceItemStatus {
  @JsonValue('available')
  available,
  @JsonValue('sold_out')
  soldOut,
  @JsonValue('coming_soon')
  comingSoon,
  @JsonValue('discontinued')
  discontinued,
}

@JsonEnum()
enum MarketplacePurchaseStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('completed')
  completed,
  @JsonValue('failed')
  failed,
  @JsonValue('refunded')
  refunded,
}

extension MarketplaceItemCategoryExtension on MarketplaceItemCategory {
  String get displayName {
    switch (this) {
      case MarketplaceItemCategory.avatar:
        return 'Avatar';
      case MarketplaceItemCategory.badge:
        return 'Badge';
      case MarketplaceItemCategory.consumable:
        return 'Consumable';
      case MarketplaceItemCategory.decoration:
        return 'Decoration';
      case MarketplaceItemCategory.boost:
        return 'Boost';
      case MarketplaceItemCategory.theme:
        return 'Theme';
      case MarketplaceItemCategory.special:
        return 'Special';
    }
  }

  String get icon {
    switch (this) {
      case MarketplaceItemCategory.avatar:
        return '👤';
      case MarketplaceItemCategory.badge:
        return '🏆';
      case MarketplaceItemCategory.consumable:
        return '🧪';
      case MarketplaceItemCategory.decoration:
        return '🎨';
      case MarketplaceItemCategory.boost:
        return '⚡';
      case MarketplaceItemCategory.theme:
        return '🎭';
      case MarketplaceItemCategory.special:
        return '✨';
    }
  }
}

extension MarketplaceItemRarityExtension on MarketplaceItemRarity {
  String get displayName {
    switch (this) {
      case MarketplaceItemRarity.common:
        return 'Common';
      case MarketplaceItemRarity.uncommon:
        return 'Uncommon';
      case MarketplaceItemRarity.rare:
        return 'Rare';
      case MarketplaceItemRarity.epic:
        return 'Epic';
      case MarketplaceItemRarity.legendary:
        return 'Legendary';
    }
  }
}

extension MarketplaceItemStatusExtension on MarketplaceItemStatus {
  String get displayName {
    switch (this) {
      case MarketplaceItemStatus.available:
        return 'Available';
      case MarketplaceItemStatus.soldOut:
        return 'Sold Out';
      case MarketplaceItemStatus.comingSoon:
        return 'Coming Soon';
      case MarketplaceItemStatus.discontinued:
        return 'Discontinued';
    }
  }
}
