# Flutter Platform Integration Guide

## Table of Contents
1. [Platform Channels Overview](#platform-channels-overview)
2. [Method Channels](#method-channels)
3. [Event Channels](#event-channels)
4. [Basic Message Channels](#basic-message-channels)
5. [Platform-Specific Code](#platform-specific-code)
6. [Plugin Development](#plugin-development)
7. [Native Code Integration](#native-code-integration)
8. [Platform Adaptations](#platform-adaptations)
9. [React Native Migration Considerations](#react-native-migration-considerations)
10. [Best Practices](#best-practices)

## Platform Channels Overview

Platform channels in Flutter provide a way to communicate between Dart code and platform-specific code (Android/iOS). This is essential for accessing platform APIs that aren't available in Flutter or when you need to integrate existing native code.

### Architecture

```
┌─────────────────┐    MethodChannel    ┌──────────────────┐
│   Flutter/Dart  │ ←─────────────────→ │ Platform Native  │
│                 │                     │ (Android/iOS)    │
└─────────────────┘                     └──────────────────┘
```

### Data Types Support

Flutter supports automatic serialization of simple data types across platforms:

| Dart | Android | iOS | Description |
|------|---------|-----|-------------|
| null | null | nil | Null value |
| bool | Boolean | NSNumber | Boolean value |
| int | Integer/Long | NSNumber | 32/64-bit integer |
| double | Double | NSNumber | 64-bit float |
| String | String | NSString | UTF-8 string |
| Uint8List | byte[] | NSData | Byte array |
| List | ArrayList | NSArray | Ordered collection |
| Map | HashMap | NSDictionary | Key-value pairs |

## Method Channels

Method channels are the most common way to invoke platform-specific methods.

### Flutter Side Implementation

```dart
import 'package:flutter/services.dart';

class PlatformService {
  static const MethodChannel _channel = MethodChannel('com.example.platform');
  
  // Simple method call
  static Future<String> getPlatformVersion() async {
    final String version = await _channel.invokeMethod('getPlatformVersion');
    return version;
  }
  
  // Method call with parameters
  static Future<bool> saveFile(String path, String content) async {
    final bool result = await _channel.invokeMethod('saveFile', {
      'path': path,
      'content': content,
    });
    return result;
  }
  
  // Method call with error handling
  static Future<String> getBatteryLevel() async {
    try {
      final int result = await _channel.invokeMethod('getBatteryLevel');
      return 'Battery level: $result%';
    } on PlatformException catch (e) {
      return 'Failed to get battery level: ${e.message}';
    }
  }
}
```

### Android Implementation (Kotlin)

```kotlin
// MainActivity.kt
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.example.platform"
    
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
            .setMethodCallHandler { call, result ->
                when (call.method) {
                    "getPlatformVersion" -> {
                        result.success("Android ${android.os.Build.VERSION.RELEASE}")
                    }
                    "getBatteryLevel" -> {
                        val batteryLevel = getBatteryLevel()
                        if (batteryLevel != -1) {
                            result.success(batteryLevel)
                        } else {
                            result.error("UNAVAILABLE", "Battery level not available", null)
                        }
                    }
                    "saveFile" -> {
                        val path = call.argument<String>("path")
                        val content = call.argument<String>("content")
                        val success = saveFileToStorage(path!!, content!!)
                        result.success(success)
                    }
                    else -> {
                        result.notImplemented()
                    }
                }
            }
    }
    
    private fun getBatteryLevel(): Int {
        val batteryManager = getSystemService(Context.BATTERY_SERVICE) as BatteryManager
        return batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY)
    }
    
    private fun saveFileToStorage(path: String, content: String): Boolean {
        return try {
            val file = File(path)
            file.writeText(content)
            true
        } catch (e: Exception) {
            false
        }
    }
}
```

### iOS Implementation (Swift)

```swift
// AppDelegate.swift
import UIKit
import Flutter

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        
        let controller = window?.rootViewController as! FlutterViewController
        let batteryChannel = FlutterMethodChannel(
            name: "com.example.platform",
            binaryMessenger: controller.binaryMessenger
        )
        
        batteryChannel.setMethodCallHandler({ [weak self] (call, result) in
            switch call.method {
            case "getPlatformVersion":
                result("iOS " + UIDevice.current.systemVersion)
            case "getBatteryLevel":
                self?.receiveBatteryLevel(result: result)
            case "saveFile":
                if let args = call.arguments as? Dictionary<String, Any>,
                   let path = args["path"] as? String,
                   let content = args["content"] as? String {
                    let success = self?.saveFile(path: path, content: content) ?? false
                    result(success)
                } else {
                    result(FlutterError(code: "INVALID_ARGUMENTS", message: nil, details: nil))
                }
            default:
                result(FlutterMethodNotImplemented)
            }
        })
        
        GeneratedPluginRegistrant.register(with: self)
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
    
    private func receiveBatteryLevel(result: FlutterResult) {
        let device = UIDevice.current
        device.isBatteryMonitoringEnabled = true
        
        if device.batteryState == UIDevice.BatteryState.unknown {
            result(FlutterError(code: "UNAVAILABLE", message: "Battery level not available", details: nil))
        } else {
            result(Int(device.batteryLevel * 100))
        }
    }
    
    private func saveFile(path: String, content: String) -> Bool {
        do {
            try content.write(toFile: path, atomically: true, encoding: .utf8)
            return true
        } catch {
            return false
        }
    }
}
```

## Event Channels

Event channels allow streaming of data from the platform to Flutter.

### Flutter Implementation

```dart
import 'package:flutter/services.dart';

class SensorService {
  static const EventChannel _eventChannel = EventChannel('com.example.sensors');
  
  static Stream<double>? _accelerometerStream;
  
  static Stream<double> get accelerometerEvents {
    _accelerometerStream ??= _eventChannel
        .receiveBroadcastStream()
        .map<double>((dynamic event) => event as double);
    return _accelerometerStream!;
  }
}

// Usage in widget
class SensorWidget extends StatefulWidget {
  @override
  _SensorWidgetState createState() => _SensorWidgetState();
}

class _SensorWidgetState extends State<SensorWidget> {
  late StreamSubscription<double> _accelerometerSubscription;
  double _accelerometerValue = 0.0;
  
  @override
  void initState() {
    super.initState();
    _accelerometerSubscription = SensorService.accelerometerEvents.listen(
      (double value) {
        setState(() {
          _accelerometerValue = value;
        });
      },
    );
  }
  
  @override
  void dispose() {
    _accelerometerSubscription.cancel();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Text('Accelerometer: $_accelerometerValue');
  }
}
```

### Android Event Channel Implementation

```kotlin
// Event Channel in MainActivity.kt
import io.flutter.plugin.common.EventChannel

class MainActivity: FlutterActivity() {
    private val EVENT_CHANNEL = "com.example.sensors"
    private lateinit var sensorManager: SensorManager
    private var accelerometer: Sensor? = null
    
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        sensorManager = getSystemService(Context.SENSOR_SERVICE) as SensorManager
        accelerometer = sensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER)
        
        EventChannel(flutterEngine.dartExecutor.binaryMessenger, EVENT_CHANNEL)
            .setStreamHandler(object : EventChannel.StreamHandler {
                override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
                    startAccelerometerListening(events)
                }
                
                override fun onCancel(arguments: Any?) {
                    stopAccelerometerListening()
                }
            })
    }
    
    private val sensorEventListener = object : SensorEventListener {
        override fun onSensorChanged(event: SensorEvent?) {
            val x = event?.values?.get(0) ?: 0.0f
            eventSink?.success(x.toDouble())
        }
        
        override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {}
    }
    
    private var eventSink: EventChannel.EventSink? = null
    
    private fun startAccelerometerListening(events: EventChannel.EventSink?) {
        eventSink = events
        accelerometer?.let {
            sensorManager.registerListener(sensorEventListener, it, SensorManager.SENSOR_DELAY_NORMAL)
        }
    }
    
    private fun stopAccelerometerListening() {
        sensorManager.unregisterListener(sensorEventListener)
        eventSink = null
    }
}
```

## Platform-Specific Code

### Platform Detection

```dart
import 'dart:io' show Platform;
import 'package:flutter/foundation.dart' show kIsWeb, defaultTargetPlatform;

class PlatformUtils {
  static bool get isAndroid => !kIsWeb && Platform.isAndroid;
  static bool get isIOS => !kIsWeb && Platform.isIOS;
  static bool get isWeb => kIsWeb;
  static bool get isMobile => isAndroid || isIOS;
  static bool get isDesktop => !kIsWeb && (Platform.isWindows || Platform.isMacOS || Platform.isLinux);
  
  static String get platformName {
    if (kIsWeb) return 'Web';
    if (Platform.isAndroid) return 'Android';
    if (Platform.isIOS) return 'iOS';
    if (Platform.isWindows) return 'Windows';
    if (Platform.isMacOS) return 'macOS';
    if (Platform.isLinux) return 'Linux';
    return 'Unknown';
  }
}

// Platform-specific widgets
class PlatformText extends StatelessWidget {
  final String text;
  
  const PlatformText(this.text);
  
  @override
  Widget build(BuildContext context) {
    if (PlatformUtils.isIOS) {
      return CupertinoText(text);
    } else {
      return Text(text, style: Theme.of(context).textTheme.bodyText1);
    }
  }
}
```

### Conditional Imports

```dart
// platform_file_picker.dart
export 'platform_file_picker_stub.dart'
    if (dart.library.io) 'platform_file_picker_mobile.dart'
    if (dart.library.html) 'platform_file_picker_web.dart';

// platform_file_picker_stub.dart
class PlatformFilePicker {
  static Future<String?> pickFile() {
    throw UnsupportedError('Platform not supported');
  }
}

// platform_file_picker_mobile.dart
import 'package:file_picker/file_picker.dart';

class PlatformFilePicker {
  static Future<String?> pickFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles();
    return result?.files.single.path;
  }
}

// platform_file_picker_web.dart
import 'dart:html' as html;

class PlatformFilePicker {
  static Future<String?> pickFile() async {
    final html.FileUploadInputElement input = html.FileUploadInputElement();
    input.click();
    
    await input.onChange.first;
    return input.files?.first.name;
  }
}
```

## Plugin Development

### Creating a Plugin

```bash
flutter create --template=plugin --platforms=android,ios my_plugin
```

### Plugin Structure

```
my_plugin/
├── lib/
│   └── my_plugin.dart           # Public API
├── android/
│   └── src/main/kotlin/
│       └── MyPlugin.kt          # Android implementation
├── ios/
│   └── Classes/
│       └── MyPlugin.swift       # iOS implementation
├── example/                     # Example app
└── pubspec.yaml
```

### Plugin Implementation

```dart
// lib/my_plugin.dart
import 'dart:async';
import 'package:flutter/services.dart';

class MyPlugin {
  static const MethodChannel _channel = MethodChannel('my_plugin');
  
  static Future<String?> get platformVersion async {
    final String? version = await _channel.invokeMethod('getPlatformVersion');
    return version;
  }
  
  static Future<bool> vibrate({int duration = 100}) async {
    final bool result = await _channel.invokeMethod('vibrate', {'duration': duration});
    return result;
  }
}
```

### Android Plugin Implementation

```kotlin
// android/src/main/kotlin/MyPlugin.kt
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel

class MyPlugin: FlutterPlugin, MethodChannel.MethodCallHandler {
    private lateinit var channel: MethodChannel
    private lateinit var context: Context
    
    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, "my_plugin")
        channel.setMethodCallHandler(this)
        context = flutterPluginBinding.applicationContext
    }
    
    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        when (call.method) {
            "getPlatformVersion" -> {
                result.success("Android ${android.os.Build.VERSION.RELEASE}")
            }
            "vibrate" -> {
                val duration = call.argument<Int>("duration") ?: 100
                val vibrator = context.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
                vibrator.vibrate(duration.toLong())
                result.success(true)
            }
            else -> {
                result.notImplemented()
            }
        }
    }
    
    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
    }
}
```

## Native Code Integration

### Accessing Android APIs

```kotlin
// Accessing device information
private fun getDeviceInfo(): Map<String, Any> {
    return mapOf(
        "model" to Build.MODEL,
        "brand" to Build.BRAND,
        "device" to Build.DEVICE,
        "androidVersion" to Build.VERSION.RELEASE,
        "sdkVersion" to Build.VERSION.SDK_INT
    )
}

// Accessing location services
private fun getCurrentLocation(result: MethodChannel.Result) {
    val locationManager = getSystemService(Context.LOCATION_SERVICE) as LocationManager
    
    if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) 
        != PackageManager.PERMISSION_GRANTED) {
        result.error("PERMISSION_DENIED", "Location permission not granted", null)
        return
    }
    
    val locationListener = object : LocationListener {
        override fun onLocationChanged(location: Location) {
            val locationMap = mapOf(
                "latitude" to location.latitude,
                "longitude" to location.longitude,
                "accuracy" to location.accuracy
            )
            result.success(locationMap)
            locationManager.removeUpdates(this)
        }
        
        override fun onStatusChanged(provider: String?, status: Int, extras: Bundle?) {}
        override fun onProviderEnabled(provider: String) {}
        override fun onProviderDisabled(provider: String) {}
    }
    
    locationManager.requestLocationUpdates(
        LocationManager.GPS_PROVIDER,
        0L, 0f, locationListener
    )
}
```

### Accessing iOS APIs

```swift
// Accessing device information
private func getDeviceInfo() -> [String: Any] {
    let device = UIDevice.current
    return [
        "name": device.name,
        "model": device.model,
        "systemName": device.systemName,
        "systemVersion": device.systemVersion,
        "identifierForVendor": device.identifierForVendor?.uuidString ?? ""
    ]
}

// Accessing camera
import AVFoundation

private func openCamera(result: @escaping FlutterResult) {
    let authStatus = AVCaptureDevice.authorizationStatus(for: .video)
    
    switch authStatus {
    case .authorized:
        presentCamera(result: result)
    case .notDetermined:
        AVCaptureDevice.requestAccess(for: .video) { granted in
            DispatchQueue.main.async {
                if granted {
                    self.presentCamera(result: result)
                } else {
                    result(FlutterError(code: "PERMISSION_DENIED", message: "Camera permission denied", details: nil))
                }
            }
        }
    case .denied, .restricted:
        result(FlutterError(code: "PERMISSION_DENIED", message: "Camera permission denied", details: nil))
    @unknown default:
        result(FlutterError(code: "UNKNOWN", message: "Unknown camera authorization status", details: nil))
    }
}

private func presentCamera(result: @escaping FlutterResult) {
    let imagePicker = UIImagePickerController()
    imagePicker.sourceType = .camera
    imagePicker.delegate = self
    
    if let viewController = UIApplication.shared.keyWindow?.rootViewController {
        viewController.present(imagePicker, animated: true)
        result(true)
    } else {
        result(FlutterError(code: "NO_CONTROLLER", message: "No view controller available", details: nil))
    }
}
```

## React Native Migration Considerations

### Bridging Differences

| Aspect | React Native | Flutter |
|--------|--------------|---------|
| **Bridge Type** | JavaScript Bridge | Platform Channels |
| **Communication** | Asynchronous only | Synchronous/Asynchronous |
| **Type Safety** | Runtime type checking | Compile-time type safety |
| **Performance** | Bridge overhead | Direct compilation |
| **Threading** | Single-threaded JS | Multi-threaded Dart |

### Migration Examples

**React Native Native Modules:**
```javascript
// React Native
import { NativeModules } from 'react-native';
const { CustomModule } = NativeModules;

const result = await CustomModule.doSomething(params);
```

**Flutter Platform Channels:**
```dart
// Flutter
const MethodChannel _channel = MethodChannel('custom_module');

final result = await _channel.invokeMethod('doSomething', params);
```

### Converting React Native Modules

1. **Identify Dependencies**: List all native modules used
2. **Find Flutter Equivalents**: Check pub.dev for existing packages
3. **Create Custom Channels**: For unique functionality
4. **Migrate Gradually**: Replace modules one by one

```dart
// Example: Converting a React Native storage module
class SecureStorage {
  static const MethodChannel _channel = MethodChannel('secure_storage');
  
  static Future<void> setItem(String key, String value) async {
    await _channel.invokeMethod('setItem', {'key': key, 'value': value});
  }
  
  static Future<String?> getItem(String key) async {
    return await _channel.invokeMethod('getItem', {'key': key});
  }
  
  static Future<void> removeItem(String key) async {
    await _channel.invokeMethod('removeItem', {'key': key});
  }
}
```

## Platform Adaptations

Flutter automatically adapts to platform conventions:

### Material vs Cupertino

```dart
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

class AdaptiveButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  
  const AdaptiveButton({
    required this.text,
    required this.onPressed,
  });
  
  @override
  Widget build(BuildContext context) {
    return Platform.isIOS
        ? CupertinoButton(
            child: Text(text),
            onPressed: onPressed,
          )
        : ElevatedButton(
            child: Text(text),
            onPressed: onPressed,
          );
  }
}
```

### Platform-Aware Widgets

```dart
class PlatformAlertDialog extends StatelessWidget {
  final String title;
  final String content;
  final List<Widget> actions;
  
  const PlatformAlertDialog({
    required this.title,
    required this.content,
    required this.actions,
  });
  
  @override
  Widget build(BuildContext context) {
    return Platform.isIOS
        ? CupertinoAlertDialog(
            title: Text(title),
            content: Text(content),
            actions: actions,
          )
        : AlertDialog(
            title: Text(title),
            content: Text(content),
            actions: actions,
          );
  }
}
```

## Best Practices

### 1. Error Handling

```dart
class RobustPlatformService {
  static const MethodChannel _channel = MethodChannel('robust_service');
  
  static Future<T?> safeInvokeMethod<T>(String method, [dynamic arguments]) async {
    try {
      final result = await _channel.invokeMethod<T>(method, arguments);
      return result;
    } on PlatformException catch (e) {
      print('Platform error: ${e.code} - ${e.message}');
      return null;
    } catch (e) {
      print('Unexpected error: $e');
      return null;
    }
  }
}
```

### 2. Channel Naming

```dart
// Use reverse domain notation
static const String CHANNEL_NAME = 'com.yourcompany.yourapp.feature';

// Group related functionality
static const String STORAGE_CHANNEL = 'com.yourcompany.yourapp.storage';
static const String NETWORK_CHANNEL = 'com.yourcompany.yourapp.network';
static const String SENSORS_CHANNEL = 'com.yourcompany.yourapp.sensors';
```

### 3. Background Thread Execution

```kotlin
// Android - Execute on background thread
override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
    when (call.method) {
        "heavyComputation" -> {
            Thread {
                val computationResult = performHeavyComputation()
                Handler(Looper.getMainLooper()).post {
                    result.success(computationResult)
                }
            }.start()
        }
    }
}
```

### 4. Resource Management

```dart
class ManagedPlatformService {
  static StreamSubscription? _subscription;
  
  static void startListening() {
    _subscription = SensorService.accelerometerEvents.listen((data) {
      // Handle data
    });
  }
  
  static void stopListening() {
    _subscription?.cancel();
    _subscription = null;
  }
}
```

### 5. Testing Platform Channels

```dart
// test/platform_test.dart
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('Platform Service Tests', () {
    const MethodChannel channel = MethodChannel('test_channel');
    
    setUp(() {
      TestDefaultBinaryMessengerBinding.instance!.defaultBinaryMessenger
          .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
        switch (methodCall.method) {
          case 'getPlatformVersion':
            return '42';
          default:
            return null;
        }
      });
    });
    
    tearDown(() {
      TestDefaultBinaryMessengerBinding.instance!.defaultBinaryMessenger
          .setMockMethodCallHandler(channel, null);
    });
    
    test('should return platform version', () async {
      final result = await channel.invokeMethod('getPlatformVersion');
      expect(result, '42');
    });
  });
}
```

## Common Platform Integration Patterns

### 1. Battery Information

```dart
class BatteryService {
  static const MethodChannel _channel = MethodChannel('battery');
  
  static Future<int> getBatteryLevel() async {
    final int level = await _channel.invokeMethod('getBatteryLevel');
    return level;
  }
  
  static Stream<int> get batteryLevelStream {
    return EventChannel('battery_stream')
        .receiveBroadcastStream()
        .map((level) => level as int);
  }
}
```

### 2. File System Access

```dart
class FileSystemService {
  static const MethodChannel _channel = MethodChannel('filesystem');
  
  static Future<String> getDocumentsPath() async {
    return await _channel.invokeMethod('getDocumentsPath');
  }
  
  static Future<bool> writeFile(String path, String content) async {
    return await _channel.invokeMethod('writeFile', {
      'path': path,
      'content': content,
    });
  }
  
  static Future<String?> readFile(String path) async {
    return await _channel.invokeMethod('readFile', {'path': path});
  }
}
```

### 3. Network Information

```dart
class NetworkService {
  static const MethodChannel _channel = MethodChannel('network');
  
  static Future<bool> isConnected() async {
    return await _channel.invokeMethod('isConnected');
  }
  
  static Future<String> getConnectionType() async {
    return await _channel.invokeMethod('getConnectionType');
  }
  
  static Stream<bool> get connectivityStream {
    return EventChannel('connectivity_stream')
        .receiveBroadcastStream()
        .map((connected) => connected as bool);
  }
}
```

This platform integration guide provides comprehensive coverage of Flutter's platform channel system, from basic method calls to complex native integrations. The examples show how to migrate from React Native's bridge system to Flutter's more type-safe and performant platform channels.
