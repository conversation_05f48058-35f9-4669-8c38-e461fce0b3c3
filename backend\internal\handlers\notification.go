package handlers

import "github.com/gofiber/fiber/v2"

// ListNotifications retrieves all notifications for a user
func ListNotifications(c *fiber.Ctx) error {
	userID := c.Locals("user_id")
	// TODO: Implement list notifications logic
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "List notifications endpoint - not implemented yet",
		"user_id": userID,
	})
}

// MarkNotificationRead marks a notification as read
func MarkNotificationRead(c *fiber.Ctx) error {
	userID := c.Locals("user_id")
	// TODO: Implement mark notification read logic
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "Mark notification read endpoint - not implemented yet",
		"user_id": userID,
	})
}
