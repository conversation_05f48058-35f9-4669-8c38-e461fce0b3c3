# Application settings
APP_ENV=development
APP_NAME=quester

# Frontend settings
FRONTEND_PORT=9000
FRONTEND_HOST=0.0.0.0
HTTP_PORT=80
HTTPS_PORT=443

# Backend settings
BACKEND_PORT=8080
BACKEND_HOST=0.0.0.0
API_VERSION=1
SERVER_READ_TIMEOUT=10s
SERVER_WRITE_TIMEOUT=10s

# Database settings
DB_HOST=postgres
DB_PORT=5432
DB_NAME=quester
DB_USER=keshabalive
DB_PASSWORD=9871
DB_SSL_MODE=disable

# Redis settings
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT settings
JWT_SECRET=your-secret-key-here
JWT_ACCESS_TOKEN_TTL=15m
JWT_REFRESH_TOKEN_TTL=168h
JWT_SIGNING_ALGORITHM=HS256

# CORS settings
CORS_ALLOW_ORIGINS=http://localhost:9000
CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOW_HEADERS=Origin,Content-Type,Accept,Authorization
CORS_ALLOW_CREDENTIALS=true
CORS_EXPOSE_HEADERS=
CORS_MAX_AGE=86400

# AI settings
AI_MODEL_ENDPOINT=http://localhost:5000/predict
AI_API_KEY=

# Blockchain settings
BLOCKCHAIN_ENABLED=false
BLOCKCHAIN_NETWORK=testnet
BLOCKCHAIN_CHAIN_ID=1
BLOCKCHAIN_RPC=https://rpc.example.com
BLOCKCHAIN_QUEST_TOKEN_ADDRESS=0x1234567890abcdef
BLOCKCHAIN_MINTER_PRIVATE_KEY=
BLOCKCHAIN_GAS_PRICE_MODE=standard
BLOCKCHAIN_SIMULATION_MODE=false
BLOCKCHAIN_BATCH_PROCESSING=false
BLOCKCHAIN_EXPLORER_URL=https://explorer.example.com
BLOCKCHAIN_QUEST_TOKEN_SYMBOL=QUEST
BLOCKCHAIN_QUEST_TOKEN_NAME=Quest Token
BLOCKCHAIN_QUEST_TOKEN_DECIMALS=18

# Admin tools settings
PGADMIN_PORT=5050
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin
REDIS_ADMIN_PORT=8081
