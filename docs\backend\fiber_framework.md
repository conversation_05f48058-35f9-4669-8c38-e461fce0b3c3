# Fiber Framework Documentation

## Overview

Fiber is an Express-inspired web framework built on top of Fasthttp, the fastest HTTP engine for Go. It's designed to ease things up for fast development with zero memory allocation and performance in mind.

## Key Features

- **Zero Allocation**: Fiber is built with zero memory allocation in mind
- **Fast**: Up to 10x faster than other Go frameworks
- **Express-like**: Familiar API for developers coming from Node.js/Express
- **Middleware Support**: Extensive middleware ecosystem
- **WebSocket Support**: Built-in WebSocket support
- **Template Engines**: Support for multiple template engines
- **Static Files**: Efficient static file serving

## Installation and Setup

```bash
go mod init your-app
go get github.com/gofiber/fiber/v2
```

### Basic Application

```go
package main

import (
    "log"
    "github.com/gofiber/fiber/v2"
)

func main() {
    // Create new Fiber instance
    app := fiber.New()

    // Define route
    app.Get("/", func(c *fiber.Ctx) error {
        return c.SendString("Hello, World!")
    })

    // Start server on port 3000
    log.Fatal(app.Listen(":3000"))
}
```

## Configuration

### App Configuration

```go
app := fiber.New(fiber.Config{
    // Prefork enables use of the SO_REUSEPORT socket option
    Prefork: false,
    
    // Enable strict routing
    StrictRouting: false,
    
    // Enable case sensitive routing
    CaseSensitive: false,
    
    // Immutable context values
    Immutable: false,
    
    // Compress response
    CompressedFileSuffix: ".fiber.gz",
    
    // Server header
    ServerHeader: "Fiber",
    
    // App name for debugging
    AppName: "Quester API v1.0.0",
    
    // Body limit
    BodyLimit: 4 * 1024 * 1024, // 4MB
    
    // Read timeout
    ReadTimeout: time.Second * 10,
    
    // Write timeout
    WriteTimeout: time.Second * 10,
    
    // Idle timeout
    IdleTimeout: time.Second * 120,
    
    // Error handler
    ErrorHandler: func(c *fiber.Ctx, err error) error {
        code := fiber.StatusInternalServerError
        if e, ok := err.(*fiber.Error); ok {
            code = e.Code
        }
        return c.Status(code).JSON(fiber.Map{
            "error": err.Error(),
        })
    },
})
```

## Routing

### Basic Routes

```go
// HTTP Methods
app.Get("/users", getUsers)
app.Post("/users", createUser)
app.Put("/users/:id", updateUser)
app.Patch("/users/:id", patchUser)
app.Delete("/users/:id", deleteUser)
app.Head("/users", headUsers)
app.Options("/users", optionsUsers)

// All methods
app.All("/api/*", func(c *fiber.Ctx) error {
    return c.SendString("API endpoint")
})

// Multiple methods
app.Add(["GET", "POST"], "/api/users", handleUsers)
```

### Route Parameters

```go
// Named parameters
app.Get("/users/:id", func(c *fiber.Ctx) error {
    userID := c.Params("id")
    return c.SendString("User ID: " + userID)
})

// Optional parameters
app.Get("/users/:id?", func(c *fiber.Ctx) error {
    userID := c.Params("id", "default")
    return c.SendString("User ID: " + userID)
})

// Wildcard parameters
app.Get("/files/*", func(c *fiber.Ctx) error {
    path := c.Params("*")
    return c.SendString("File path: " + path)
})

// Multiple parameters
app.Get("/users/:userID/posts/:postID", func(c *fiber.Ctx) error {
    userID := c.Params("userID")
    postID := c.Params("postID")
    return c.JSON(fiber.Map{
        "userID": userID,
        "postID": postID,
    })
})
```

### Route Constraints

```go
// Regex constraints
app.Get("/users/:id<int>", func(c *fiber.Ctx) error {
    userID := c.Params("id")
    return c.SendString("User ID (int): " + userID)
})

// Custom regex
app.Get("/users/:id<^\\d+$>", func(c *fiber.Ctx) error {
    userID := c.Params("id")
    return c.SendString("User ID (digits only): " + userID)
})
```

### Route Groups

```go
// API versioning
api := app.Group("/api")
v1 := api.Group("/v1")
v2 := api.Group("/v2")

// V1 routes
v1.Get("/users", getUsersV1)
v1.Post("/users", createUserV1)

// V2 routes
v2.Get("/users", getUsersV2)
v2.Post("/users", createUserV2)

// Middleware for groups
admin := app.Group("/admin", authMiddleware)
admin.Get("/users", getAdminUsers)
admin.Delete("/users/:id", deleteUser)
```

## Context (Ctx)

### Request Data

```go
func handler(c *fiber.Ctx) error {
    // Request method
    method := c.Method() // GET, POST, etc.
    
    // Request URL
    url := c.OriginalURL()
    path := c.Path()
    
    // Headers
    contentType := c.Get("Content-Type")
    userAgent := c.Get("User-Agent")
    
    // Query parameters
    name := c.Query("name")
    age := c.QueryInt("age", 0) // with default
    tags := c.QueryParser(&struct{
        Tags []string `query:"tags"`
    }{})
    
    // Form data
    username := c.FormValue("username")
    file, err := c.FormFile("upload")
    
    // JSON body
    var user User
    if err := c.BodyParser(&user); err != nil {
        return c.Status(400).JSON(fiber.Map{
            "error": "Invalid JSON",
        })
    }
    
    // Raw body
    body := c.Body()
    
    return c.SendString("OK")
}
```

### Response Methods

```go
func responseExamples(c *fiber.Ctx) error {
    // String response
    return c.SendString("Hello, World!")
    
    // JSON response
    return c.JSON(fiber.Map{
        "message": "Success",
        "data":    []string{"item1", "item2"},
    })
    
    // Status with JSON
    return c.Status(201).JSON(fiber.Map{
        "message": "Created",
    })
    
    // File response
    return c.SendFile("./public/index.html")
    
    // Download file
    return c.Download("./files/document.pdf", "report.pdf")
    
    // Redirect
    return c.Redirect("/new-path", 301)
    
    // Set headers
    c.Set("X-Custom-Header", "value")
    c.Set("Content-Type", "application/json")
    
    // Cookies
    c.Cookie(&fiber.Cookie{
        Name:     "session",
        Value:    "abc123",
        Expires:  time.Now().Add(24 * time.Hour),
        HTTPOnly: true,
        Secure:   true,
        SameSite: "Strict",
    })
    
    return c.SendString("Response sent")
}
```

## Middleware

### Built-in Middleware

```go
import (
    "github.com/gofiber/fiber/v2"
    "github.com/gofiber/fiber/v2/middleware/cors"
    "github.com/gofiber/fiber/v2/middleware/logger"
    "github.com/gofiber/fiber/v2/middleware/recover"
    "github.com/gofiber/fiber/v2/middleware/compress"
    "github.com/gofiber/fiber/v2/middleware/helmet"
    "github.com/gofiber/fiber/v2/middleware/limiter"
)

// CORS middleware
app.Use(cors.New(cors.Config{
    AllowOrigins: "https://example.com, https://app.example.com",
    AllowHeaders: "Origin, Content-Type, Accept, Authorization",
    AllowMethods: "GET, POST, PUT, DELETE, OPTIONS",
}))

// Logger middleware
app.Use(logger.New(logger.Config{
    Format: "${time} ${method} ${path} ${status} ${latency}\n",
    TimeFormat: "2006-01-02 15:04:05",
    TimeZone: "Local",
}))

// Recover middleware
app.Use(recover.New())

// Compression middleware
app.Use(compress.New(compress.Config{
    Level: compress.LevelBestSpeed,
}))

// Security headers
app.Use(helmet.New())

// Rate limiting
app.Use(limiter.New(limiter.Config{
    Max:        100,
    Expiration: 1 * time.Minute,
    KeyGenerator: func(c *fiber.Ctx) string {
        return c.IP()
    },
    LimitReached: func(c *fiber.Ctx) error {
        return c.Status(429).JSON(fiber.Map{
            "error": "Rate limit exceeded",
        })
    },
}))
```

### Custom Middleware

```go
// Authentication middleware
func authMiddleware(c *fiber.Ctx) error {
    token := c.Get("Authorization")
    if token == "" {
        return c.Status(401).JSON(fiber.Map{
            "error": "Missing authorization token",
        })
    }
    
    // Validate token
    userID, err := validateToken(token)
    if err != nil {
        return c.Status(401).JSON(fiber.Map{
            "error": "Invalid token",
        })
    }
    
    // Store user ID in context
    c.Locals("userID", userID)
    
    return c.Next()
}

// Request ID middleware
func requestIDMiddleware(c *fiber.Ctx) error {
    requestID := uuid.New().String()
    c.Locals("requestID", requestID)
    c.Set("X-Request-ID", requestID)
    return c.Next()
}

// Usage
app.Use(requestIDMiddleware)
app.Use("/api", authMiddleware)
```

### Middleware Order

```go
// Order matters!
app.Use(recover.New())     // 1. Recover from panics
app.Use(logger.New())      // 2. Log requests
app.Use(cors.New())        // 3. Handle CORS
app.Use(helmet.New())      // 4. Security headers
app.Use(compress.New())    // 5. Compress responses
app.Use(limiter.New())     // 6. Rate limiting
app.Use(authMiddleware)    // 7. Authentication

// Routes
app.Get("/api/users", getUsers)
```

## Error Handling

### Custom Error Handler

```go
app := fiber.New(fiber.Config{
    ErrorHandler: func(c *fiber.Ctx, err error) error {
        // Default 500 status code
        code := fiber.StatusInternalServerError
        message := "Internal Server Error"
        
        // Check if it's a Fiber error
        if e, ok := err.(*fiber.Error); ok {
            code = e.Code
            message = e.Message
        }
        
        // Log error
        log.Printf("Error: %v", err)
        
        // Return error response
        return c.Status(code).JSON(fiber.Map{
            "error": message,
            "code":  code,
        })
    },
})
```

### Custom Errors

```go
// Create custom errors
func validateUser(user *User) error {
    if user.Email == "" {
        return fiber.NewError(400, "Email is required")
    }
    if user.Age < 18 {
        return fiber.NewError(400, "User must be at least 18 years old")
    }
    return nil
}

// Error types
type ValidationError struct {
    Field   string `json:"field"`
    Message string `json:"message"`
}

func (e ValidationError) Error() string {
    return fmt.Sprintf("validation error: %s - %s", e.Field, e.Message)
}

// Handler with error handling
func createUser(c *fiber.Ctx) error {
    var user User
    if err := c.BodyParser(&user); err != nil {
        return fiber.NewError(400, "Invalid request body")
    }
    
    if err := validateUser(&user); err != nil {
        return err // Will be handled by error handler
    }
    
    // Create user logic
    createdUser, err := userService.Create(&user)
    if err != nil {
        return fiber.NewError(500, "Failed to create user")
    }
    
    return c.Status(201).JSON(createdUser)
}
```

## Static Files

```go
// Serve static files
app.Static("/", "./public")
app.Static("/assets", "./assets")

// Static with config
app.Static("/", "./public", fiber.Static{
    Compress:      true,
    ByteRange:     true,
    Browse:        true,
    Index:         "index.html",
    CacheDuration: 10 * time.Second,
    MaxAge:        3600,
})

// File server
app.Get("/download/*", func(c *fiber.Ctx) error {
    return c.SendFile("./files/" + c.Params("*"))
})
```

## WebSocket Support

```go
import "github.com/gofiber/contrib/websocket"

// WebSocket upgrade middleware
app.Use("/ws", func(c *fiber.Ctx) error {
    if websocket.IsWebSocketUpgrade(c) {
        c.Locals("allowed", true)
        return c.Next()
    }
    return fiber.ErrUpgradeRequired
})

// WebSocket handler
app.Get("/ws/:id", websocket.New(func(c *websocket.Conn) {
    // Get connection ID from URL
    id := c.Params("id")
    
    // Connection handling
    defer func() {
        c.Close()
        log.Printf("WebSocket connection %s closed", id)
    }()
    
    log.Printf("WebSocket connection %s established", id)
    
    for {
        // Read message
        messageType, message, err := c.ReadMessage()
        if err != nil {
            log.Printf("Read error: %v", err)
            break
        }
        
        log.Printf("Received: %s", message)
        
        // Echo message back
        if err := c.WriteMessage(messageType, message); err != nil {
            log.Printf("Write error: %v", err)
            break
        }
    }
}))
```

### WebSocket with Channels

```go
type Hub struct {
    clients    map[*websocket.Conn]bool
    broadcast  chan []byte
    register   chan *websocket.Conn
    unregister chan *websocket.Conn
}

func newHub() *Hub {
    return &Hub{
        clients:    make(map[*websocket.Conn]bool),
        broadcast:  make(chan []byte),
        register:   make(chan *websocket.Conn),
        unregister: make(chan *websocket.Conn),
    }
}

func (h *Hub) run() {
    for {
        select {
        case client := <-h.register:
            h.clients[client] = true
            log.Println("Client connected")
            
        case client := <-h.unregister:
            if _, ok := h.clients[client]; ok {
                delete(h.clients, client)
                client.Close()
                log.Println("Client disconnected")
            }
            
        case message := <-h.broadcast:
            for client := range h.clients {
                if err := client.WriteMessage(websocket.TextMessage, message); err != nil {
                    delete(h.clients, client)
                    client.Close()
                }
            }
        }
    }
}

// Usage
hub := newHub()
go hub.run()

app.Get("/ws", websocket.New(func(c *websocket.Conn) {
    defer func() {
        hub.unregister <- c
    }()
    
    hub.register <- c
    
    for {
        _, message, err := c.ReadMessage()
        if err != nil {
            break
        }
        hub.broadcast <- message
    }
}))
```

## Testing

### Unit Testing

```go
package main

import (
    "bytes"
    "encoding/json"
    "io"
    "net/http/httptest"
    "testing"
    
    "github.com/gofiber/fiber/v2"
    "github.com/stretchr/testify/assert"
)

func setupTestApp() *fiber.App {
    app := fiber.New()
    
    app.Get("/users/:id", func(c *fiber.Ctx) error {
        id := c.Params("id")
        return c.JSON(fiber.Map{
            "id":   id,
            "name": "John Doe",
        })
    })
    
    app.Post("/users", func(c *fiber.Ctx) error {
        var user map[string]interface{}
        if err := c.BodyParser(&user); err != nil {
            return c.Status(400).JSON(fiber.Map{
                "error": "Invalid JSON",
            })
        }
        
        user["id"] = "123"
        return c.Status(201).JSON(user)
    })
    
    return app
}

func TestGetUser(t *testing.T) {
    app := setupTestApp()
    
    req := httptest.NewRequest("GET", "/users/123", nil)
    resp, err := app.Test(req)
    
    assert.NoError(t, err)
    assert.Equal(t, 200, resp.StatusCode)
    
    body, err := io.ReadAll(resp.Body)
    assert.NoError(t, err)
    
    var result map[string]interface{}
    err = json.Unmarshal(body, &result)
    assert.NoError(t, err)
    assert.Equal(t, "123", result["id"])
    assert.Equal(t, "John Doe", result["name"])
}

func TestCreateUser(t *testing.T) {
    app := setupTestApp()
    
    user := map[string]interface{}{
        "name":  "Jane Doe",
        "email": "<EMAIL>",
    }
    
    body, _ := json.Marshal(user)
    req := httptest.NewRequest("POST", "/users", bytes.NewReader(body))
    req.Header.Set("Content-Type", "application/json")
    
    resp, err := app.Test(req)
    
    assert.NoError(t, err)
    assert.Equal(t, 201, resp.StatusCode)
}
```

## Performance Optimization

### Connection Pooling

```go
// Database connection pool
db, err := sql.Open("postgres", dsn)
if err != nil {
    log.Fatal(err)
}

db.SetMaxOpenConns(25)
db.SetMaxIdleConns(25)
db.SetConnMaxLifetime(5 * time.Minute)

// HTTP client pool
var httpClient = &http.Client{
    Timeout: 30 * time.Second,
    Transport: &http.Transport{
        MaxIdleConns:        100,
        MaxIdleConnsPerHost: 100,
        IdleConnTimeout:     90 * time.Second,
    },
}
```

### Memory Optimization

```go
// Object pooling
var bufferPool = sync.Pool{
    New: func() interface{} {
        return make([]byte, 1024)
    },
}

func handler(c *fiber.Ctx) error {
    buf := bufferPool.Get().([]byte)
    defer bufferPool.Put(buf)
    
    // Use buffer
    return c.SendString("OK")
}

// Immutable context (recommended)
app := fiber.New(fiber.Config{
    Immutable: true, // Makes context values immutable
})
```

### Caching

```go
import "github.com/gofiber/fiber/v2/middleware/cache"

// Cache middleware
app.Use(cache.New(cache.Config{
    Expiration:   30 * time.Minute,
    CacheControl: true,
    KeyGenerator: func(c *fiber.Ctx) string {
        return c.Path() + "?" + c.Request().URI().QueryString()
    },
}))

// Manual caching
var memoryCache = make(map[string]interface{})
var cacheMutex = sync.RWMutex{}

func getCachedData(key string) (interface{}, bool) {
    cacheMutex.RLock()
    defer cacheMutex.RUnlock()
    data, exists := memoryCache[key]
    return data, exists
}

func setCachedData(key string, data interface{}) {
    cacheMutex.Lock()
    defer cacheMutex.Unlock()
    memoryCache[key] = data
}
```

## Best Practices

### Project Structure

```
project/
├── cmd/
│   └── server/
│       └── main.go
├── internal/
│   ├── handlers/
│   │   ├── user.go
│   │   └── auth.go
│   ├── middleware/
│   │   ├── auth.go
│   │   └── cors.go
│   ├── models/
│   │   └── user.go
│   ├── services/
│   │   └── user.go
│   └── database/
│       └── connection.go
├── pkg/
│   └── utils/
│       └── validation.go
└── go.mod
```

### Handler Organization

```go
// handlers/user.go
package handlers

type UserHandler struct {
    userService *services.UserService
}

func NewUserHandler(userService *services.UserService) *UserHandler {
    return &UserHandler{
        userService: userService,
    }
}

func (h *UserHandler) GetUser(c *fiber.Ctx) error {
    id := c.Params("id")
    
    user, err := h.userService.GetByID(id)
    if err != nil {
        return fiber.NewError(404, "User not found")
    }
    
    return c.JSON(user)
}

func (h *UserHandler) CreateUser(c *fiber.Ctx) error {
    var req CreateUserRequest
    if err := c.BodyParser(&req); err != nil {
        return fiber.NewError(400, "Invalid request body")
    }
    
    if err := req.Validate(); err != nil {
        return fiber.NewError(400, err.Error())
    }
    
    user, err := h.userService.Create(&req)
    if err != nil {
        return fiber.NewError(500, "Failed to create user")
    }
    
    return c.Status(201).JSON(user)
}
```

### Route Registration

```go
// routes/routes.go
package routes

func SetupRoutes(app *fiber.App, handlers *handlers.Handlers) {
    // API group
    api := app.Group("/api/v1")
    
    // Health check
    api.Get("/health", func(c *fiber.Ctx) error {
        return c.JSON(fiber.Map{
            "status": "ok",
            "time":   time.Now(),
        })
    })
    
    // User routes
    users := api.Group("/users")
    users.Get("/", handlers.User.GetUsers)
    users.Get("/:id", handlers.User.GetUser)
    users.Post("/", handlers.User.CreateUser)
    users.Put("/:id", handlers.User.UpdateUser)
    users.Delete("/:id", handlers.User.DeleteUser)
    
    // Protected routes
    protected := api.Group("/", middleware.AuthRequired())
    protected.Get("/profile", handlers.User.GetProfile)
    protected.Put("/profile", handlers.User.UpdateProfile)
}
```

This comprehensive Fiber framework documentation provides all the essential knowledge needed for building robust web APIs in the Quester project.