import 'package:json_annotation/json_annotation.dart';

part 'notification_settings_model.g.dart';

@JsonSerializable()
class NotificationSettings {
  final String id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id')
  final String userId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'push_enabled')
  final bool pushEnabled;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'email_enabled')
  final bool emailEnabled;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'quest_notifications')
  final bool questNotifications;
  @<PERSON>son<PERSON><PERSON>(name: 'achievement_notifications')
  final bool achievementNotifications;
  @<PERSON>son<PERSON>ey(name: 'social_notifications')
  final bool socialNotifications;
  @<PERSON>sonKey(name: 'marketing_notifications')
  final bool marketingNotifications;
  @<PERSON>son<PERSON>ey(name: 'quiet_hours_enabled')
  final bool quietHoursEnabled;
  @<PERSON>son<PERSON><PERSON>(name: 'quiet_hours_start')
  final String? quietHoursStart; // Format: "HH:mm"
  @<PERSON><PERSON><PERSON><PERSON>(name: 'quiet_hours_end')
  final String? quietHoursEnd; // Format: "HH:mm"
  @<PERSON><PERSON><PERSON><PERSON>(name: 'notification_frequency')
  final NotificationFrequency frequency;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final DateTime updatedAt;

  const NotificationSettings({
    required this.id,
    required this.userId,
    required this.pushEnabled,
    required this.emailEnabled,
    required this.questNotifications,
    required this.achievementNotifications,
    required this.socialNotifications,
    required this.marketingNotifications,
    required this.quietHoursEnabled,
    this.quietHoursStart,
    this.quietHoursEnd,
    required this.frequency,
    required this.createdAt,
    required this.updatedAt,
  });

  factory NotificationSettings.fromJson(Map<String, dynamic> json) => _$NotificationSettingsFromJson(json);
  Map<String, dynamic> toJson() => _$NotificationSettingsToJson(this);

  /// Create default notification settings
  factory NotificationSettings.defaultSettings({
    required String id,
    required String userId,
  }) {
    final now = DateTime.now();
    return NotificationSettings(
      id: id,
      userId: userId,
      pushEnabled: true,
      emailEnabled: true,
      questNotifications: true,
      achievementNotifications: true,
      socialNotifications: true,
      marketingNotifications: false,
      quietHoursEnabled: false,
      quietHoursStart: '22:00',
      quietHoursEnd: '08:00',
      frequency: NotificationFrequency.normal,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Copy with method for updating settings
  NotificationSettings copyWith({
    String? id,
    String? userId,
    bool? pushEnabled,
    bool? emailEnabled,
    bool? questNotifications,
    bool? achievementNotifications,
    bool? socialNotifications,
    bool? marketingNotifications,
    bool? quietHoursEnabled,
    String? quietHoursStart,
    String? quietHoursEnd,
    NotificationFrequency? frequency,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return NotificationSettings(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      pushEnabled: pushEnabled ?? this.pushEnabled,
      emailEnabled: emailEnabled ?? this.emailEnabled,
      questNotifications: questNotifications ?? this.questNotifications,
      achievementNotifications: achievementNotifications ?? this.achievementNotifications,
      socialNotifications: socialNotifications ?? this.socialNotifications,
      marketingNotifications: marketingNotifications ?? this.marketingNotifications,
      quietHoursEnabled: quietHoursEnabled ?? this.quietHoursEnabled,
      quietHoursStart: quietHoursStart ?? this.quietHoursStart,
      quietHoursEnd: quietHoursEnd ?? this.quietHoursEnd,
      frequency: frequency ?? this.frequency,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Check if notifications are allowed at current time
  bool get areNotificationsAllowed {
    if (!quietHoursEnabled || quietHoursStart == null || quietHoursEnd == null) {
      return true;
    }

    final now = DateTime.now();
    final currentTime = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
    
    // Simple time comparison (doesn't handle cross-midnight ranges perfectly)
    return !(currentTime.compareTo(quietHoursStart!) >= 0 && currentTime.compareTo(quietHoursEnd!) <= 0);
  }
}

@JsonEnum()
enum NotificationFrequency {
  @JsonValue('minimal')
  minimal,
  @JsonValue('normal')
  normal,
  @JsonValue('frequent')
  frequent,
}

extension NotificationFrequencyExtension on NotificationFrequency {
  String get displayName {
    switch (this) {
      case NotificationFrequency.minimal:
        return 'Minimal';
      case NotificationFrequency.normal:
        return 'Normal';
      case NotificationFrequency.frequent:
        return 'Frequent';
    }
  }

  String get description {
    switch (this) {
      case NotificationFrequency.minimal:
        return 'Only critical notifications';
      case NotificationFrequency.normal:
        return 'Important updates and achievements';
      case NotificationFrequency.frequent:
        return 'All notifications including tips and reminders';
    }
  }
}
