// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quest_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Quest _$QuestFromJson(Map<String, dynamic> json) => Quest(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      image: json['image'] as String?,
      type: $enumDecode(_$QuestTypeEnumMap, json['type']),
      category: $enumDecode(_$QuestCategoryEnumMap, json['category']),
      difficulty: $enumDecode(_$QuestDifficultyEnumMap, json['difficulty']),
      status: $enumDecode(_$QuestStatusEnumMap, json['status']),
      points: (json['points'] as num).toInt(),
      experience: (json['experience'] as num).toInt(),
      estimatedDuration: (json['estimated_duration'] as num).toInt(),
      maxParticipants: (json['max_participants'] as num?)?.toInt(),
      isActive: json['is_active'] as bool,
      isFeatured: json['is_featured'] as bool,
      startDate: json['start_date'] == null
          ? null
          : DateTime.parse(json['start_date'] as String),
      endDate: json['end_date'] == null
          ? null
          : DateTime.parse(json['end_date'] as String),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      steps: (json['steps'] as List<dynamic>?)
          ?.map((e) => QuestStep.fromJson(e as Map<String, dynamic>))
          .toList(),
      rewards: (json['rewards'] as List<dynamic>?)
          ?.map((e) => QuestReward.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$QuestToJson(Quest instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'image': instance.image,
      'type': _$QuestTypeEnumMap[instance.type]!,
      'category': _$QuestCategoryEnumMap[instance.category]!,
      'difficulty': _$QuestDifficultyEnumMap[instance.difficulty]!,
      'status': _$QuestStatusEnumMap[instance.status]!,
      'points': instance.points,
      'experience': instance.experience,
      'estimated_duration': instance.estimatedDuration,
      'max_participants': instance.maxParticipants,
      'is_active': instance.isActive,
      'is_featured': instance.isFeatured,
      'start_date': instance.startDate?.toIso8601String(),
      'end_date': instance.endDate?.toIso8601String(),
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'steps': instance.steps,
      'rewards': instance.rewards,
    };

const _$QuestTypeEnumMap = {
  QuestType.daily: 'daily',
  QuestType.weekly: 'weekly',
  QuestType.monthly: 'monthly',
  QuestType.oneTime: 'one_time',
  QuestType.chain: 'chain',
  QuestType.collaborative: 'collaborative',
};

const _$QuestCategoryEnumMap = {
  QuestCategory.fitness: 'fitness',
  QuestCategory.learning: 'learning',
  QuestCategory.social: 'social',
  QuestCategory.creative: 'creative',
  QuestCategory.productivity: 'productivity',
  QuestCategory.health: 'health',
  QuestCategory.adventure: 'adventure',
  QuestCategory.skill: 'skill',
  QuestCategory.daily: 'daily',
  QuestCategory.weekly: 'weekly',
};

const _$QuestDifficultyEnumMap = {
  QuestDifficulty.easy: 'easy',
  QuestDifficulty.medium: 'medium',
  QuestDifficulty.hard: 'hard',
  QuestDifficulty.expert: 'expert',
};

const _$QuestStatusEnumMap = {
  QuestStatus.notStarted: 'not_started',
  QuestStatus.active: 'active',
  QuestStatus.inProgress: 'in_progress',
  QuestStatus.completed: 'completed',
  QuestStatus.failed: 'failed',
  QuestStatus.abandoned: 'abandoned',
};

QuestStep _$QuestStepFromJson(Map<String, dynamic> json) => QuestStep(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      order: (json['order'] as num).toInt(),
      type: $enumDecode(_$QuestStepTypeEnumMap, json['type']),
      requirements: json['requirements'] as Map<String, dynamic>?,
      isCompleted: json['isCompleted'] as bool,
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
    );

Map<String, dynamic> _$QuestStepToJson(QuestStep instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'order': instance.order,
      'type': _$QuestStepTypeEnumMap[instance.type]!,
      'requirements': instance.requirements,
      'isCompleted': instance.isCompleted,
      'completedAt': instance.completedAt?.toIso8601String(),
    };

const _$QuestStepTypeEnumMap = {
  QuestStepType.action: 'action',
  QuestStepType.submission: 'submission',
  QuestStepType.verification: 'verification',
  QuestStepType.timer: 'timer',
  QuestStepType.location: 'location',
  QuestStepType.social: 'social',
};

QuestReward _$QuestRewardFromJson(Map<String, dynamic> json) => QuestReward(
      id: json['id'] as String,
      type: $enumDecode(_$QuestRewardTypeEnumMap, json['type']),
      name: json['name'] as String,
      description: json['description'] as String,
      quantity: (json['quantity'] as num).toInt(),
      imageUrl: json['imageUrl'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$QuestRewardToJson(QuestReward instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$QuestRewardTypeEnumMap[instance.type]!,
      'name': instance.name,
      'description': instance.description,
      'quantity': instance.quantity,
      'imageUrl': instance.imageUrl,
      'metadata': instance.metadata,
    };

const _$QuestRewardTypeEnumMap = {
  QuestRewardType.points: 'points',
  QuestRewardType.badge: 'badge',
  QuestRewardType.item: 'item',
  QuestRewardType.currency: 'currency',
  QuestRewardType.experience: 'experience',
  QuestRewardType.nft: 'nft',
};

QuestProgress _$QuestProgressFromJson(Map<String, dynamic> json) =>
    QuestProgress(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      questId: json['quest_id'] as String,
      quest: Quest.fromJson(json['quest'] as Map<String, dynamic>),
      status: $enumDecode(_$QuestStatusEnumMap, json['status']),
      currentStep: (json['current_step'] as num).toInt(),
      totalSteps: (json['total_steps'] as num).toInt(),
      completedSteps: (json['completed_steps'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      progress: (json['progress'] as num).toInt(),
      startedAt: DateTime.parse(json['started_at'] as String),
      completedAt: json['completed_at'] == null
          ? null
          : DateTime.parse(json['completed_at'] as String),
      lastActivityAt: DateTime.parse(json['last_activity_at'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$QuestProgressToJson(QuestProgress instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'quest_id': instance.questId,
      'quest': instance.quest,
      'status': _$QuestStatusEnumMap[instance.status]!,
      'current_step': instance.currentStep,
      'total_steps': instance.totalSteps,
      'completed_steps': instance.completedSteps,
      'progress': instance.progress,
      'started_at': instance.startedAt.toIso8601String(),
      'completed_at': instance.completedAt?.toIso8601String(),
      'last_activity_at': instance.lastActivityAt.toIso8601String(),
      'metadata': instance.metadata,
    };
