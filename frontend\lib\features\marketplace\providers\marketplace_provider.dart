import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/marketplace_item_model.dart';

class MarketplaceState {
  final bool isLoading;
  final List<MarketplaceItem> items;
  final List<MarketplaceItem> featuredItems;
  final List<MarketplacePurchase> purchases;
  final MarketplaceItem? selectedItem;
  final String? error;
  final String? searchQuery;
  final MarketplaceItemCategory? selectedCategory;
  final bool hasMore;
  final int currentPage;

  const MarketplaceState({
    this.isLoading = false,
    this.items = const [],
    this.featuredItems = const [],
    this.purchases = const [],
    this.selectedItem,
    this.error,
    this.searchQuery,
    this.selectedCategory,
    this.hasMore = true,
    this.currentPage = 1,
  });

  MarketplaceState copyWith({
    bool? isLoading,
    List<MarketplaceItem>? items,
    List<MarketplaceItem>? featuredItems,
    List<MarketplacePurchase>? purchases,
    MarketplaceItem? selectedItem,
    String? error,
    String? searchQuery,
    MarketplaceItemCategory? selectedCategory,
    bool? hasMore,
    int? currentPage,
  }) {
    return MarketplaceState(
      isLoading: isLoading ?? this.isLoading,
      items: items ?? this.items,
      featuredItems: featuredItems ?? this.featuredItems,
      purchases: purchases ?? this.purchases,
      selectedItem: selectedItem ?? this.selectedItem,
      error: error,
      searchQuery: searchQuery ?? this.searchQuery,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      hasMore: hasMore ?? this.hasMore,
      currentPage: currentPage ?? this.currentPage,
    );
  }

  List<MarketplaceItem> get filteredItems {
    var filtered = items;

    // Filter by category
    if (selectedCategory != null) {
      filtered = filtered.where((item) => item.category == selectedCategory).toList();
    }

    // Filter by search query
    if (searchQuery != null && searchQuery!.isNotEmpty) {
      final query = searchQuery!.toLowerCase();
      filtered = filtered.where((item) =>
          item.name.toLowerCase().contains(query) ||
          item.description.toLowerCase().contains(query) ||
          item.tags.any((tag) => tag.toLowerCase().contains(query))
      ).toList();
    }

    return filtered;
  }
}

class MarketplaceNotifier extends StateNotifier<MarketplaceState> {
  MarketplaceNotifier() : super(const MarketplaceState());

  Future<void> loadItems({bool refresh = false}) async {
    if (refresh) {
      state = state.copyWith(
        isLoading: true,
        currentPage: 1,
        items: [],
        hasMore: true,
        error: null,
      );
    } else if (state.isLoading || !state.hasMore) {
      return;
    } else {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      // For now, create mock data until we have the real API methods
      final newItems = await _getMockMarketplaceItems();
      final featuredItems = await _getMockFeaturedItems();

      final allItems = refresh 
          ? newItems 
          : [...state.items, ...newItems];

      state = state.copyWith(
        isLoading: false,
        items: allItems,
        featuredItems: featuredItems,
        hasMore: newItems.length >= 20,
        currentPage: refresh ? 2 : state.currentPage + 1,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> loadItemDetails(String itemId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final item = state.items.firstWhere(
        (item) => item.id == itemId,
        orElse: () => throw Exception('Item not found'),
      );
      
      state = state.copyWith(
        isLoading: false,
        selectedItem: item,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<bool> purchaseItem(String itemId, int quantity) async {
    try {
      final item = state.items.firstWhere((item) => item.id == itemId);
      
      // Simulate purchase
      final purchase = MarketplacePurchase(
        id: 'purchase_${DateTime.now().millisecondsSinceEpoch}',
        userId: 'current_user',
        itemId: itemId,
        item: item,
        quantity: quantity,
        totalPrice: item.price * quantity,
        currency: item.currency,
        purchasedAt: DateTime.now(),
        status: MarketplacePurchaseStatus.completed,
        transactionId: 'tx_${DateTime.now().millisecondsSinceEpoch}',
      );

      final updatedPurchases = [...state.purchases, purchase];
      state = state.copyWith(purchases: updatedPurchases);
      
      return true;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  void setSearchQuery(String? query) {
    state = state.copyWith(searchQuery: query);
  }

  void setSelectedCategory(MarketplaceItemCategory? category) {
    state = state.copyWith(selectedCategory: category);
  }

  void clearSelectedItem() {
    state = state.copyWith(selectedItem: null);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  // Mock data methods - replace with real API calls
  Future<List<MarketplaceItem>> _getMockMarketplaceItems() async {
    await Future.delayed(const Duration(milliseconds: 500));
    return [
      MarketplaceItem(
        id: 'item_1',
        name: 'Golden Crown Avatar',
        description: 'A majestic golden crown for your avatar',
        imageUrl: 'https://example.com/crown.png',
        category: MarketplaceItemCategory.avatar,
        rarity: MarketplaceItemRarity.legendary,
        price: 500,
        currency: 'points',
        sellerId: 'admin',
        sellerName: 'Quester Official',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        status: MarketplaceItemStatus.available,
        quantity: 1,
        maxQuantity: 1,
        tags: ['crown', 'gold', 'avatar', 'royal'],
        isLimited: true,
        limitedUntil: DateTime.now().add(const Duration(days: 7)),
      ),
      MarketplaceItem(
        id: 'item_2',
        name: 'XP Boost Potion',
        description: 'Double your XP for 1 hour',
        category: MarketplaceItemCategory.consumable,
        rarity: MarketplaceItemRarity.common,
        price: 50,
        currency: 'points',
        sellerId: 'admin',
        sellerName: 'Quester Official',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        status: MarketplaceItemStatus.available,
        quantity: 99,
        maxQuantity: 100,
        tags: ['xp', 'boost', 'potion'],
        isLimited: false,
      ),
      MarketplaceItem(
        id: 'item_3',
        name: 'Rainbow Badge',
        description: 'Show off your colorful achievement',
        category: MarketplaceItemCategory.badge,
        rarity: MarketplaceItemRarity.rare,
        price: 200,
        currency: 'points',
        sellerId: 'admin',
        sellerName: 'Quester Official',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        status: MarketplaceItemStatus.available,
        quantity: 50,
        maxQuantity: 50,
        tags: ['badge', 'rainbow', 'colorful'],
        isLimited: false,
        discountPercentage: 20,
        originalPrice: 250,
      ),
    ];
  }

  Future<List<MarketplaceItem>> _getMockFeaturedItems() async {
    final items = await _getMockMarketplaceItems();
    return items.take(2).toList();
  }
}

final marketplaceNotifierProvider = StateNotifierProvider<MarketplaceNotifier, MarketplaceState>((ref) {
  return MarketplaceNotifier();
});

// Filtered item providers
final filteredItemsProvider = Provider<List<MarketplaceItem>>((ref) {
  return ref.watch(marketplaceNotifierProvider).filteredItems;
});

final featuredItemsProvider = Provider<List<MarketplaceItem>>((ref) {
  return ref.watch(marketplaceNotifierProvider).featuredItems;
});

final onSaleItemsProvider = Provider<List<MarketplaceItem>>((ref) {
  final items = ref.watch(marketplaceNotifierProvider).items;
  return items.where((item) => item.isOnSale).toList();
});

final limitedItemsProvider = Provider<List<MarketplaceItem>>((ref) {
  final items = ref.watch(marketplaceNotifierProvider).items;
  return items.where((item) => item.isLimited && !item.isExpired).toList();
});

// Category providers
final avatarItemsProvider = Provider<List<MarketplaceItem>>((ref) {
  final items = ref.watch(marketplaceNotifierProvider).items;
  return items.where((item) => item.category == MarketplaceItemCategory.avatar).toList();
});

final badgeItemsProvider = Provider<List<MarketplaceItem>>((ref) {
  final items = ref.watch(marketplaceNotifierProvider).items;
  return items.where((item) => item.category == MarketplaceItemCategory.badge).toList();
});

final consumableItemsProvider = Provider<List<MarketplaceItem>>((ref) {
  final items = ref.watch(marketplaceNotifierProvider).items;
  return items.where((item) => item.category == MarketplaceItemCategory.consumable).toList();
});

// Purchase providers
final userPurchasesProvider = Provider<List<MarketplacePurchase>>((ref) {
  return ref.watch(marketplaceNotifierProvider).purchases;
});

// Loading state providers
final marketplaceLoadingProvider = Provider<bool>((ref) {
  return ref.watch(marketplaceNotifierProvider).isLoading;
});

final marketplaceErrorProvider = Provider<String?>((ref) {
  return ref.watch(marketplaceNotifierProvider).error;
});

final selectedItemProvider = Provider<MarketplaceItem?>((ref) {
  return ref.watch(marketplaceNotifierProvider).selectedItem;
});
