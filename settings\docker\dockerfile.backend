# Development stage
FROM golang:latest AS development

# Set environment variables
ENV GOOS=linux \
    GOARCH=amd64 \
    CGO_ENABLED=0 \
    GO_ENV=development

# Set working directory
WORKDIR /app

# Install git and air for hot reload
RUN apt-get update && apt-get install -y git && \
    go install github.com/air-verse/air@latest

# Copy go.mod and go.sum first
COPY backend/go.mod backend/go.sum ./


# Download dependencies (bypass Go proxy if needed)
ENV GOPROXY=direct
RUN go mod download

# Copy the entire backend directory
COPY backend/ ./

# Install dependencies
RUN go mod tidy

# Expose the application port (runtime env will set PORT)
EXPOSE 8080

# Command to run the development server with hot reload
CMD ["air", "-c", ".air.toml"]

# Production build stage
FROM golang:latest AS builder

# Set working directory
WORKDIR /app

# Set environment variables for build
ENV GOOS=linux \
    GOARCH=amd64 \
    CGO_ENABLED=0

# Copy go.mod and go.sum files
COPY backend/go.* .

# Copy the rest of the application code into working directory root
COPY backend/. ./

# Copy .env file if it exists
COPY backend/.env* ./


# Download dependencies (bypass Go proxy if needed)
ENV GOPROXY=direct
RUN go mod download

# Build the Go application
RUN go build -o main ./cmd/api

# Production runtime stage
FROM alpine:latest AS production

# Set environment variables
ENV GO_ENV=production

# Set working directory
WORKDIR /app

# Install CA certificates
RUN apk --no-cache add ca-certificates

# Copy the compiled binary from the builder stage
COPY --from=builder /app/main .

# Make the binary executable
RUN chmod +x /app/main

# Expose the application port (runtime env will set PORT)
EXPOSE 8080

# Command to run the application
CMD ["./main"]






