// Core barrel export file
// This file exports all core functionality for easy importing

// Configuration
export 'config/app_config.dart';

// Constants
export 'constants/app_constants.dart';

// Dependency Injection
export 'di/app_providers.dart';

// Error Handling
export 'error/app_error.dart';

// Logging
export 'logging/app_logger.dart';

// Models
export 'models/achievement_model.dart';
export 'models/marketplace_item_model.dart';
export 'models/marketplace_model.dart' hide MarketplaceItem;
export 'models/notification_model.dart';
export 'models/notification_settings_model.dart';
export 'models/quest_model.dart';
export 'models/user_model.dart';
export 'models/wallet_model.dart';

// Providers
export 'providers/theme_provider.dart';
export 'providers/websocket_provider.dart' hide webSocketServiceProvider;

// Services
export 'services/api_service.dart' hide apiServiceProvider;
export 'services/app_router.dart';
export 'services/asset_preloader_service.dart';
export 'services/auth_service.dart' hide authServiceProvider;
export 'services/notification_service.dart' hide NotificationType, notificationServiceProvider;
export 'services/storage_service.dart';
export 'services/websocket_service.dart';

// Theme
export 'theme/app_theme.dart';

// Assets
export 'assets/app_assets.dart';

// Testing (only in debug mode)
export 'testing/test_helpers.dart';

// Utils
export 'utils/responsive_helper.dart';
export 'utils/performance_monitor.dart';
