import '../models/marketplace_model.dart';
import 'base_api_service.dart';

class MarketplaceService extends BaseApiService {
  MarketplaceService(super.dio);

  // Get all marketplace items with filtering and pagination
  Future<List<MarketplaceItem>> getMarketplaceItems({
    ItemCategory? category,
    ItemType? type,
    ItemRarity? rarity,
    bool? isActive,
    bool? isFeatured,
    bool? isLimited,
    int? minPrice,
    int? maxPrice,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (category != null) queryParams['category'] = category.name;
      if (type != null) queryParams['type'] = type.name;
      if (rarity != null) queryParams['rarity'] = rarity.name;
      if (isActive != null) queryParams['is_active'] = isActive;
      if (isFeatured != null) queryParams['is_featured'] = isFeatured;
      if (isLimited != null) queryParams['is_limited'] = isLimited;
      if (minPrice != null) queryParams['min_price'] = minPrice;
      if (maxPrice != null) queryParams['max_price'] = maxPrice;

      final response = await dio.get('/marketplace/items', queryParameters: queryParams);
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => MarketplaceItem.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get marketplace item by ID
  Future<MarketplaceItem> getMarketplaceItem(String id) async {
    try {
      final response = await dio.get('/marketplace/items/$id');
      return MarketplaceItem.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get items by category
  Future<List<MarketplaceItem>> getItemsByCategory(ItemCategory category, {
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await dio.get('/marketplace/categories/${category.name}/items', 
        queryParameters: {'page': page, 'limit': limit});
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => MarketplaceItem.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get featured items
  Future<List<MarketplaceItem>> getFeaturedItems({int limit = 10}) async {
    try {
      final response = await dio.get('/marketplace/items', 
        queryParameters: {'is_featured': true, 'limit': limit});
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => MarketplaceItem.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get marketplace categories
  Future<List<ItemCategory>> getMarketplaceCategories() async {
    try {
      final response = await dio.get('/marketplace/categories');
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((categoryName) {
        return ItemCategory.values.firstWhere(
          (category) => category.name == categoryName,
          orElse: () => ItemCategory.cosmetic,
        );
      }).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Cart operations
  Future<Cart> getCart() async {
    try {
      final response = await dio.get('/marketplace/cart');
      return Cart.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Add item to cart
  Future<CartItem> addToCart(String itemId, {int quantity = 1}) async {
    try {
      final response = await dio.post('/marketplace/cart', data: {
        'item_id': itemId,
        'quantity': quantity,
      });
      return CartItem.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Update cart item quantity
  Future<CartItem> updateCartItem(String cartItemId, int quantity) async {
    try {
      final response = await dio.put('/marketplace/cart/$cartItemId', data: {
        'quantity': quantity,
      });
      return CartItem.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Remove item from cart
  Future<void> removeFromCart(String cartItemId) async {
    try {
      await dio.delete('/marketplace/cart/$cartItemId');
    } catch (e) {
      throw handleError(e);
    }
  }

  // Clear cart
  Future<void> clearCart() async {
    try {
      await dio.delete('/marketplace/cart');
    } catch (e) {
      throw handleError(e);
    }
  }

  // Purchase operations
  Future<Purchase> purchaseItem(String itemId, {int quantity = 1}) async {
    try {
      final response = await dio.post('/marketplace/purchase', data: {
        'item_id': itemId,
        'quantity': quantity,
      });
      return Purchase.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Purchase all items in cart
  Future<List<Purchase>> purchaseCart() async {
    try {
      final response = await dio.post('/marketplace/purchase/cart');
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => Purchase.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get user's purchase history
  Future<List<Purchase>> getPurchaseHistory({
    PurchaseStatus? status,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (status != null) queryParams['status'] = status.name;

      final response = await dio.get('/user/purchases', queryParameters: queryParams);
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => Purchase.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get purchase by ID
  Future<Purchase> getPurchase(String purchaseId) async {
    try {
      final response = await dio.get('/user/purchases/$purchaseId');
      return Purchase.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Search marketplace items
  Future<List<MarketplaceItem>> searchItems(String query, {
    ItemCategory? category,
    ItemType? type,
    ItemRarity? rarity,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'q': query,
        'page': page,
        'limit': limit,
      };

      if (category != null) queryParams['category'] = category.name;
      if (type != null) queryParams['type'] = type.name;
      if (rarity != null) queryParams['rarity'] = rarity.name;

      final response = await dio.get('/marketplace/search', queryParameters: queryParams);
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => MarketplaceItem.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get user's owned items
  Future<List<MarketplaceItem>> getOwnedItems({
    ItemCategory? category,
    ItemType? type,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (category != null) queryParams['category'] = category.name;
      if (type != null) queryParams['type'] = type.name;

      final response = await dio.get('/user/items', queryParameters: queryParams);
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => MarketplaceItem.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Admin operations
  // Create a new marketplace item (admin functionality)
  Future<MarketplaceItem> createMarketplaceItem({
    required String name,
    required String description,
    String? image,
    required ItemCategory category,
    required ItemType type,
    required ItemRarity rarity,
    required int price,
    required String currency,
    int? stockQuantity,
    bool isLimited = false,
    bool isActive = true,
    bool isFeatured = false,
    int? purchaseLimit,
    int? levelRequirement,
    DateTime? availableFrom,
    DateTime? availableUntil,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final data = <String, dynamic>{
        'name': name,
        'description': description,
        'category': category.name,
        'type': type.name,
        'rarity': rarity.name,
        'price': price,
        'currency': currency,
        'is_limited': isLimited,
        'is_active': isActive,
        'is_featured': isFeatured,
      };

      if (image != null) data['image'] = image;
      if (stockQuantity != null) data['stock_quantity'] = stockQuantity;
      if (purchaseLimit != null) data['purchase_limit'] = purchaseLimit;
      if (levelRequirement != null) data['level_requirement'] = levelRequirement;
      if (availableFrom != null) data['available_from'] = availableFrom.toIso8601String();
      if (availableUntil != null) data['available_until'] = availableUntil.toIso8601String();
      if (metadata != null) data['metadata'] = metadata;

      final response = await dio.post('/marketplace/items', data: data);
      return MarketplaceItem.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Update marketplace item (admin functionality)
  Future<MarketplaceItem> updateMarketplaceItem(String itemId, {
    String? name,
    String? description,
    String? image,
    ItemCategory? category,
    ItemType? type,
    ItemRarity? rarity,
    int? price,
    String? currency,
    int? stockQuantity,
    bool? isLimited,
    bool? isActive,
    bool? isFeatured,
    int? purchaseLimit,
    int? levelRequirement,
    DateTime? availableFrom,
    DateTime? availableUntil,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final data = <String, dynamic>{};

      if (name != null) data['name'] = name;
      if (description != null) data['description'] = description;
      if (image != null) data['image'] = image;
      if (category != null) data['category'] = category.name;
      if (type != null) data['type'] = type.name;
      if (rarity != null) data['rarity'] = rarity.name;
      if (price != null) data['price'] = price;
      if (currency != null) data['currency'] = currency;
      if (stockQuantity != null) data['stock_quantity'] = stockQuantity;
      if (isLimited != null) data['is_limited'] = isLimited;
      if (isActive != null) data['is_active'] = isActive;
      if (isFeatured != null) data['is_featured'] = isFeatured;
      if (purchaseLimit != null) data['purchase_limit'] = purchaseLimit;
      if (levelRequirement != null) data['level_requirement'] = levelRequirement;
      if (availableFrom != null) data['available_from'] = availableFrom.toIso8601String();
      if (availableUntil != null) data['available_until'] = availableUntil.toIso8601String();
      if (metadata != null) data['metadata'] = metadata;

      final response = await dio.put('/marketplace/items/$itemId', data: data);
      return MarketplaceItem.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Delete marketplace item (admin functionality)
  Future<void> deleteMarketplaceItem(String itemId) async {
    try {
      await dio.delete('/marketplace/items/$itemId');
    } catch (e) {
      throw handleError(e);
    }
  }
}
