import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../services/websocket_service.dart';
import '../models/notification_model.dart' as notification_model;
import '../models/quest_model.dart';
import '../models/achievement_model.dart';

/// WebSocket connection state
enum WebSocketConnectionState {
  disconnected,
  connecting,
  connected,
  reconnecting,
  error,
}

/// WebSocket connection state provider
final webSocketConnectionProvider = StreamProvider<WebSocketConnectionState>((ref) {
  final webSocketService = WebSocketService.instance;
  
  return webSocketService.connectionStream.map((isConnected) {
    return isConnected 
        ? WebSocketConnectionState.connected 
        : WebSocketConnectionState.disconnected;
  });
});

/// WebSocket service provider
final webSocketServiceProvider = Provider<WebSocketService>((ref) {
  return WebSocketService.instance;
});

/// Real-time notifications provider
final realTimeNotificationsProvider = StreamProvider<notification_model.Notification>((ref) {
  final webSocketService = ref.watch(webSocketServiceProvider);

  return webSocketService.notificationStream.map((data) {
    try {
      return notification_model.Notification.fromJson(data['data'] ?? data);
    } catch (e) {
      debugPrint('Failed to parse notification: $e');
      rethrow;
    }
  });
});

/// Real-time quest updates provider
final realTimeQuestUpdatesProvider = StreamProvider<QuestProgress>((ref) {
  final webSocketService = ref.watch(webSocketServiceProvider);
  
  return webSocketService.questUpdateStream.map((data) {
    try {
      return QuestProgress.fromJson(data['data'] ?? data);
    } catch (e) {
      debugPrint('Failed to parse quest update: $e');
      rethrow;
    }
  });
});

/// Real-time achievement unlocks provider
final realTimeAchievementProvider = StreamProvider<Achievement>((ref) {
  final webSocketService = ref.watch(webSocketServiceProvider);
  
  return webSocketService.achievementStream.map((data) {
    try {
      return Achievement.fromJson(data['data'] ?? data);
    } catch (e) {
      debugPrint('Failed to parse achievement: $e');
      rethrow;
    }
  });
});

/// Real-time leaderboard updates provider
final realTimeLeaderboardProvider = StreamProvider<Map<String, dynamic>>((ref) {
  final webSocketService = ref.watch(webSocketServiceProvider);
  
  return webSocketService.leaderboardStream;
});

/// WebSocket manager notifier
class WebSocketManagerNotifier extends StateNotifier<WebSocketConnectionState> {
  final WebSocketService _webSocketService;
  StreamSubscription? _connectionSubscription;

  WebSocketManagerNotifier(this._webSocketService) 
      : super(WebSocketConnectionState.disconnected) {
    _initializeConnection();
  }

  void _initializeConnection() {
    _connectionSubscription = _webSocketService.connectionStream.listen(
      (isConnected) {
        state = isConnected 
            ? WebSocketConnectionState.connected 
            : WebSocketConnectionState.disconnected;
      },
      onError: (error) {
        state = WebSocketConnectionState.error;
        debugPrint('WebSocket connection error: $error');
      },
    );
  }

  /// Connect to WebSocket
  Future<void> connect() async {
    if (state == WebSocketConnectionState.connecting) return;
    
    state = WebSocketConnectionState.connecting;
    try {
      await _webSocketService.connect();
      // Subscribe to all event types
      _webSocketService.subscribe('notifications');
      _webSocketService.subscribe('quest_updates');
      _webSocketService.subscribe('achievements');
      _webSocketService.subscribe('leaderboard');
      _webSocketService.subscribe('wallet_updates');
    } catch (e) {
      state = WebSocketConnectionState.error;
      debugPrint('Failed to connect to WebSocket: $e');
    }
  }

  /// Disconnect from WebSocket
  Future<void> disconnect() async {
    await _webSocketService.disconnect();
    state = WebSocketConnectionState.disconnected;
  }

  /// Reconnect to WebSocket
  Future<void> reconnect() async {
    state = WebSocketConnectionState.reconnecting;
    await disconnect();
    await Future.delayed(const Duration(seconds: 1));
    await connect();
  }

  /// Send message through WebSocket
  void sendMessage(Map<String, dynamic> message) {
    _webSocketService.sendMessage(message);
  }

  /// Subscribe to specific event type
  void subscribe(String eventType) {
    _webSocketService.subscribe(eventType);
  }

  /// Unsubscribe from specific event type
  void unsubscribe(String eventType) {
    _webSocketService.unsubscribe(eventType);
  }

  @override
  void dispose() {
    _connectionSubscription?.cancel();
    super.dispose();
  }
}

/// WebSocket manager provider
final webSocketManagerProvider = StateNotifierProvider<WebSocketManagerNotifier, WebSocketConnectionState>((ref) {
  final webSocketService = ref.watch(webSocketServiceProvider);
  return WebSocketManagerNotifier(webSocketService);
});

/// Auto-connect WebSocket provider (automatically connects when app starts)
final autoConnectWebSocketProvider = Provider<void>((ref) {
  final manager = ref.watch(webSocketManagerProvider.notifier);
  
  // Auto-connect when provider is first accessed
  Future.microtask(() => manager.connect());
  
  // Listen for app lifecycle changes to reconnect when app becomes active
  ref.onDispose(() {
    manager.disconnect();
  });
});

/// WebSocket connection status widget helper
final webSocketStatusProvider = Provider<String>((ref) {
  final connectionState = ref.watch(webSocketManagerProvider);
  
  switch (connectionState) {
    case WebSocketConnectionState.disconnected:
      return 'Disconnected';
    case WebSocketConnectionState.connecting:
      return 'Connecting...';
    case WebSocketConnectionState.connected:
      return 'Connected';
    case WebSocketConnectionState.reconnecting:
      return 'Reconnecting...';
    case WebSocketConnectionState.error:
      return 'Connection Error';
  }
});

/// WebSocket connection color provider
final webSocketStatusColorProvider = Provider<Color>((ref) {
  final connectionState = ref.watch(webSocketManagerProvider);
  
  switch (connectionState) {
    case WebSocketConnectionState.disconnected:
      return const Color(0xFFEF4444); // Red
    case WebSocketConnectionState.connecting:
    case WebSocketConnectionState.reconnecting:
      return const Color(0xFFF97316); // Orange
    case WebSocketConnectionState.connected:
      return const Color(0xFF10B981); // Green
    case WebSocketConnectionState.error:
      return const Color(0xFFDC2626); // Dark red
  }
});

/// Real-time data sync provider
final realTimeDataSyncProvider = Provider<void>((ref) {
  // Watch all real-time providers to ensure they're active
  ref.watch(realTimeNotificationsProvider);
  ref.watch(realTimeQuestUpdatesProvider);
  ref.watch(realTimeAchievementProvider);
  ref.watch(realTimeLeaderboardProvider);
  
  // Ensure WebSocket is connected
  ref.watch(autoConnectWebSocketProvider);
});
