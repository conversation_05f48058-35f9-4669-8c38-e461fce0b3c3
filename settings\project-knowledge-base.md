# Quester Project Knowledge Base

## Technology Stack Overview

### Frontend Technologies
- Vue 3.5+ with TypeScript 5.4+
- Quasar Framework 3.0+ for UI
- Pinia 3.0+ for state management
- Vue Router 5.0+ for navigation
- WebSocket integration for real-time features
- Testing with Vitest and Cypress

### Backend Technologies
- Go 1.22+ with Fiber v2.0+
- GORM with PostgreSQL driver
- Redis for caching and pub/sub
- JWT & OAuth2 authentication
- WebSocket support with connection pooling

### Infrastructure
- Docker and Kubernetes
- Nginx with HTTP/3
- Redis cluster
- PostgreSQL with replication
- Monitoring with Prometheus & Grafana

## Architecture Components

### Frontend Architecture
- Component-based UI structure
- Centralized state management
- Composables for reusable logic
- Lazy loading and code splitting
- PWA support with service workers

### Backend Architecture
- Layered design (Handlers → Services → Repositories → Models)
- Domain-driven design principles
- Clean architecture patterns
- Event-driven with message broker
- Idempotent operations

### Database Schema
- User management
- Quest and achievement systems
- Marketplace functionality
- Notification system
- Friend relationships
- Virtual wallet system

## Integration Points

### Frontend-Backend Communication
- REST API endpoints
- WebSocket connections for real-time updates
- Server-sent events for notifications
- File uploads and media handling
- Authentication flow with JWT

### State Management
- Pinia stores for frontend state
- Redis for backend caching
- PostgreSQL for persistent storage
- WebSocket for real-time sync
- Event-driven updates

### Infrastructure Integration
- Docker container orchestration
- Kubernetes service mesh
- Redis pub/sub system
- Database replication
- Load balancing with Nginx

## Development Guidelines

### Frontend Development
- Use Composition API with script setup
- Follow Vue 3 best practices
- Implement responsive design
- Use TypeScript for type safety
- Write comprehensive tests

### Backend Development
- Follow Go idiomatic patterns
- Implement proper error handling
- Use middleware for cross-cutting concerns
- Implement proper logging
- Follow REST API best practices

### Database Guidelines
- Use GORM migrations
- Implement proper indexing
- Follow normalization rules
- Implement caching strategy
- Handle transactions properly

## Testing Strategy

### Frontend Testing
- Unit tests with Vitest
- Component testing
- E2E tests with Cypress
- Performance testing
- Accessibility testing

### Backend Testing
- Unit tests for business logic
- Integration tests for APIs
- Performance benchmarks
- Security testing
- Contract testing

## Security Measures

### Authentication
- JWT token management
- OAuth2 integration
- Role-based access control
- Session management
- Rate limiting

### Data Protection
- Input validation
- SQL injection prevention
- XSS protection
- CSRF protection
- Data encryption

## Performance Optimizations

### Frontend Performance
- Code splitting
- Lazy loading
- Asset optimization
- Caching strategy
- Progressive loading

### Backend Performance
- Connection pooling
- Query optimization
- Caching layers
- Load balancing
- Resource management

## Documentation Links
### Knowledge Graph
- [Technology Relationships Diagram](docs/tech/knowledge-graph.md#technology-relationships)
- [Structured Data Format](docs/tech/knowledge-graph.md#structured-data-representation)
- Uses Mermaid.js for visualization
- Implements JSON-LD for machine-readable metadata

### Frontend Resources
- Vue 3: https://vuejs.org/guide/introduction.html
- Quasar Framework: https://quasar.dev/start/quick-start
- Quasar Components: https://quasar.dev/components
- Pinia State Management: https://pinia.vuejs.org/core-concepts/
- TypeScript: https://www.typescriptlang.org/docs/
- Vue Router: https://router.vuejs.org/guide/
- Vitest: https://vitest.dev/guide/

### Backend Resources
- Go Language: https://go.dev/doc/
- Fiber Framework: https://docs.gofiber.io/
- Fiber API Reference: https://pkg.go.dev/github.com/gofiber/fiber/v2
- Fiber WebSocket: https://pkg.go.dev/github.com/gofiber/contrib/websocket
- GORM ORM: https://gorm.io/docs/
- GORM API Reference: https://pkg.go.dev/gorm.io/gorm
- PostgreSQL: https://www.postgresql.org/docs/
- Redis: https://redis.io/documentation
