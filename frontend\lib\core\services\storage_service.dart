import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class StorageService {
  static const String _userBox = 'user_box';
  static const String _settingsBox = 'settings_box';
  static const String _cacheBox = 'cache_box';
  static const String _questsBox = 'quests_box';
    static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  static late Box _userStorage;
  static late Box _settingsStorage;
  static late Box _cacheStorage;
  static late Box _questsStorage;

  /// Initialize all storage boxes
  static Future<void> init() async {
    _userStorage = await Hive.openBox(_userBox);
    _settingsStorage = await Hive.openBox(_settingsBox);
    _cacheStorage = await Hive.openBox(_cacheBox);
    _questsStorage = await Hive.openBox(_questsBox);
  }

  // ==== User Storage Methods ====
  
  /// Save user data
  static Future<void> saveUser(Map<String, dynamic> userData) async {
    await _userStorage.put('user', userData);
  }

  /// Get user data
  static Map<String, dynamic>? getUser() {
    final userData = _userStorage.get('user');
    return userData != null ? Map<String, dynamic>.from(userData) : null;
  }

  /// Clear user data
  static Future<void> clearUser() async {
    await _userStorage.delete('user');
  }

  /// Check if user is logged in
  static bool isLoggedIn() {
    return _userStorage.containsKey('user');
  }

  // ==== Token Management (Secure Storage) ====
  
  /// Save access token securely
  static Future<void> saveAccessToken(String token) async {
    await _secureStorage.write(key: 'access_token', value: token);
  }

  /// Get access token
  static Future<String?> getAccessToken() async {
    return await _secureStorage.read(key: 'access_token');
  }

  /// Save refresh token securely
  static Future<void> saveRefreshToken(String token) async {
    await _secureStorage.write(key: 'refresh_token', value: token);
  }

  /// Get refresh token
  static Future<String?> getRefreshToken() async {
    return await _secureStorage.read(key: 'refresh_token');
  }

  /// Clear all tokens
  static Future<void> clearTokens() async {
    await _secureStorage.delete(key: 'access_token');
    await _secureStorage.delete(key: 'refresh_token');
  }

  // ==== Settings Storage ====
  
  /// Save app theme mode
  static Future<void> saveThemeMode(String themeMode) async {
    await _settingsStorage.put('theme_mode', themeMode);
  }

  /// Get app theme mode
  static String getThemeMode() {
    return _settingsStorage.get('theme_mode', defaultValue: 'system');
  }

  /// Save notification settings
  static Future<void> saveNotificationSettings(Map<String, bool> settings) async {
    await _settingsStorage.put('notifications', settings);
  }

  /// Get notification settings
  static Map<String, bool> getNotificationSettings() {
    final settings = _settingsStorage.get('notifications');
    return settings != null ? Map<String, bool>.from(settings) : {
      'push_enabled': true,
      'quest_updates': true,
      'achievement_alerts': true,
      'marketplace_deals': false,
      'leaderboard_updates': false,
    };
  }

  /// Save language preference
  static Future<void> saveLanguage(String languageCode) async {
    await _settingsStorage.put('language', languageCode);
  }

  /// Get language preference
  static String getLanguage() {
    return _settingsStorage.get('language', defaultValue: 'en');
  }

  // ==== Onboarding & First Time User ====
  
  /// Mark onboarding as complete
  static Future<void> setOnboardingComplete() async {
    await _settingsStorage.put('onboarding_complete', true);
  }

  /// Check if onboarding is complete
  static bool isOnboardingComplete() {
    return _settingsStorage.get('onboarding_complete', defaultValue: false);
  }

  /// Mark first time setup as complete
  static Future<void> setFirstTimeSetupComplete() async {
    await _settingsStorage.put('first_time_setup', true);
  }

  /// Check if first time setup is complete
  static bool isFirstTimeSetupComplete() {
    return _settingsStorage.get('first_time_setup', defaultValue: false);
  }

  // ==== Cache Storage ====
  
  /// Save cached data with expiration
  static Future<void> saveCache(String key, dynamic data, {Duration? expiration}) async {
    final cacheData = {
      'data': data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'expiration': expiration?.inMilliseconds,
    };
    await _cacheStorage.put(key, cacheData);
  }

  /// Get cached data (returns null if expired)
  static T? getCache<T>(String key) {
    final cacheData = _cacheStorage.get(key);
    if (cacheData == null) return null;

    final Map<String, dynamic> cache = Map<String, dynamic>.from(cacheData);
    final timestamp = cache['timestamp'] as int;
    final expiration = cache['expiration'] as int?;

    if (expiration != null) {
      final now = DateTime.now().millisecondsSinceEpoch;
      if (now - timestamp > expiration) {
        // Cache expired, remove it
        _cacheStorage.delete(key);
        return null;
      }
    }

    return cache['data'] as T?;
  }

  /// Clear specific cache
  static Future<void> clearCache(String key) async {
    await _cacheStorage.delete(key);
  }

  /// Clear all cache
  static Future<void> clearAllCache() async {
    await _cacheStorage.clear();
  }

  // ==== Quests Storage ====
  
  /// Save quest progress
  static Future<void> saveQuestProgress(String questId, Map<String, dynamic> progress) async {
    await _questsStorage.put('progress_$questId', progress);
  }

  /// Get quest progress
  static Map<String, dynamic>? getQuestProgress(String questId) {
    final progress = _questsStorage.get('progress_$questId');
    return progress != null ? Map<String, dynamic>.from(progress) : null;
  }

  /// Save completed quests list
  static Future<void> saveCompletedQuests(List<String> questIds) async {
    await _questsStorage.put('completed_quests', questIds);
  }

  /// Get completed quests list
  static List<String> getCompletedQuests() {
    final completed = _questsStorage.get('completed_quests');
    return completed != null ? List<String>.from(completed) : [];
  }

  /// Save favorite quests
  static Future<void> saveFavoriteQuests(List<String> questIds) async {
    await _questsStorage.put('favorite_quests', questIds);
  }

  /// Get favorite quests
  static List<String> getFavoriteQuests() {
    final favorites = _questsStorage.get('favorite_quests');
    return favorites != null ? List<String>.from(favorites) : [];
  }

  // ==== General Utility Methods ====
  
  /// Save any key-value pair
  static Future<void> save(String key, dynamic value, {StorageBox box = StorageBox.cache}) async {
    switch (box) {
      case StorageBox.user:
        await _userStorage.put(key, value);
        break;
      case StorageBox.settings:
        await _settingsStorage.put(key, value);
        break;
      case StorageBox.cache:
        await _cacheStorage.put(key, value);
        break;
      case StorageBox.quests:
        await _questsStorage.put(key, value);
        break;
    }
  }

  /// Get any value by key
  static T? get<T>(String key, {StorageBox box = StorageBox.cache, T? defaultValue}) {
    dynamic value;
    switch (box) {
      case StorageBox.user:
        value = _userStorage.get(key, defaultValue: defaultValue);
        break;
      case StorageBox.settings:
        value = _settingsStorage.get(key, defaultValue: defaultValue);
        break;
      case StorageBox.cache:
        value = _cacheStorage.get(key, defaultValue: defaultValue);
        break;
      case StorageBox.quests:
        value = _questsStorage.get(key, defaultValue: defaultValue);
        break;
    }
    return value as T?;
  }

  /// Delete any key
  static Future<void> delete(String key, {StorageBox box = StorageBox.cache}) async {
    switch (box) {
      case StorageBox.user:
        await _userStorage.delete(key);
        break;
      case StorageBox.settings:
        await _settingsStorage.delete(key);
        break;
      case StorageBox.cache:
        await _cacheStorage.delete(key);
        break;
      case StorageBox.quests:
        await _questsStorage.delete(key);
        break;
    }
  }

  /// Clear all data (for logout)
  static Future<void> clearAllData() async {
    await Future.wait([
      _userStorage.clear(),
      _cacheStorage.clear(),
      _questsStorage.clear(),
      clearTokens(),
    ]);
  }

  /// Get storage statistics
  static Map<String, int> getStorageStats() {
    return {
      'user_items': _userStorage.length,
      'settings_items': _settingsStorage.length,
      'cache_items': _cacheStorage.length,
      'quest_items': _questsStorage.length,
    };
  }

  // ==== General App Data Methods ====
  
  /// Save general app data
  static Future<void> saveAppData(String key, dynamic data) async {
    await _settingsStorage.put(key, data);
  }

  /// Get general app data
  static T? getAppData<T>(String key, {T? defaultValue}) {
    return _settingsStorage.get(key, defaultValue: defaultValue) as T?;
  }
  /// Delete general app data
  static Future<void> deleteAppData(String key) async {
    await _settingsStorage.delete(key);
  }
}

/// Enum for different storage boxes
enum StorageBox {
  user,
  settings,
  cache,
  quests,
}
