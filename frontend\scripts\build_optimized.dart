#!/usr/bin/env dart

import 'dart:io';

/// Optimized build script for the Quester Flutter app
/// This script performs various optimizations before building
void main(List<String> arguments) async {
  print('🚀 Starting optimized build process...');
  
  final stopwatch = Stopwatch()..start();
  
  try {
    // Parse arguments
    final target = arguments.isNotEmpty ? arguments[0] : 'web';
    final mode = arguments.length > 1 ? arguments[1] : 'release';
    
    print('📱 Target: $target');
    print('🔧 Mode: $mode');
    
    // Pre-build optimizations
    await runPreBuildOptimizations();
    
    // Run the build
    await runBuild(target, mode);
    
    // Post-build optimizations
    await runPostBuildOptimizations(target);
    
    stopwatch.stop();
    print('✅ Build completed successfully in ${stopwatch.elapsed.inSeconds}s');
    
  } catch (error) {
    print('❌ Build failed: $error');
    exit(1);
  }
}

/// Run pre-build optimizations
Future<void> runPreBuildOptimizations() async {
  print('\n🔧 Running pre-build optimizations...');
  
  // Clean previous builds
  await runCommand('flutter', ['clean']);
  
  // Get dependencies
  await runCommand('flutter', ['pub', 'get']);
  
  // Generate code (models, etc.)
  await runCommand('flutter', ['packages', 'pub', 'run', 'build_runner', 'build', '--delete-conflicting-outputs']);
  
  // Analyze code
  print('📊 Analyzing code...');
  final analyzeResult = await runCommand('flutter', ['analyze'], throwOnError: false);
  if (analyzeResult.exitCode != 0) {
    print('⚠️  Code analysis found issues, but continuing build...');
  }
  
  print('✅ Pre-build optimizations completed');
}

/// Run the actual build
Future<void> runBuild(String target, String mode) async {
  print('\n🏗️  Building for $target in $mode mode...');
  
  final buildArgs = ['build', target];
  
  if (mode == 'release') {
    buildArgs.addAll(['--release']);
  } else if (mode == 'profile') {
    buildArgs.addAll(['--profile']);
  }
  
  // Add target-specific optimizations
  switch (target) {
    case 'web':
      buildArgs.addAll([
        '--web-renderer', 'html', // Use HTML renderer for better compatibility
        '--dart-define=FLUTTER_WEB_USE_SKIA=false',
        '--dart-define=FLUTTER_WEB_AUTO_DETECT=false',
      ]);
      break;
    case 'apk':
      buildArgs.addAll([
        '--split-per-abi', // Create separate APKs for different architectures
        '--obfuscate',
        '--split-debug-info=build/debug-info',
      ]);
      break;
    case 'ios':
      buildArgs.addAll([
        '--obfuscate',
        '--split-debug-info=build/debug-info',
      ]);
      break;
  }
  
  await runCommand('flutter', buildArgs);
  print('✅ Build completed');
}

/// Run post-build optimizations
Future<void> runPostBuildOptimizations(String target) async {
  print('\n⚡ Running post-build optimizations...');
  
  switch (target) {
    case 'web':
      await optimizeWebBuild();
      break;
    case 'apk':
      await optimizeApkBuild();
      break;
    case 'ios':
      await optimizeIosBuild();
      break;
  }
  
  print('✅ Post-build optimizations completed');
}

/// Optimize web build
Future<void> optimizeWebBuild() async {
  print('🌐 Optimizing web build...');
  
  final buildDir = Directory('build/web');
  if (!buildDir.existsSync()) {
    print('❌ Web build directory not found');
    return;
  }
  
  // Compress assets (if gzip is available)
  if (await isCommandAvailable('gzip')) {
    print('📦 Compressing assets...');
    await compressWebAssets();
  }
  
  // Generate service worker for PWA
  await generateServiceWorker();
  
  // Optimize images (if imagemagick is available)
  if (await isCommandAvailable('convert')) {
    print('🖼️  Optimizing images...');
    await optimizeImages('build/web');
  }
  
  print('✅ Web build optimized');
}

/// Optimize APK build
Future<void> optimizeApkBuild() async {
  print('📱 Optimizing APK build...');
  
  // APK optimization is mostly handled by Flutter build process
  // Additional optimizations could be added here
  
  print('✅ APK build optimized');
}

/// Optimize iOS build
Future<void> optimizeIosBuild() async {
  print('🍎 Optimizing iOS build...');
  
  // iOS optimization is mostly handled by Flutter build process
  // Additional optimizations could be added here
  
  print('✅ iOS build optimized');
}

/// Compress web assets using gzip
Future<void> compressWebAssets() async {
  final webDir = Directory('build/web');
  final files = webDir.listSync(recursive: true).whereType<File>();
  
  for (final file in files) {
    final extension = file.path.split('.').last.toLowerCase();
    if (['js', 'css', 'html', 'json', 'svg'].contains(extension)) {
      await runCommand('gzip', ['-k', '-f', file.path], throwOnError: false);
    }
  }
}

/// Generate service worker for PWA
Future<void> generateServiceWorker() async {
  final swFile = File('build/web/sw.js');
  const swContent = '''
const CACHE_NAME = 'quester-v1';
const urlsToCache = [
  '/',
  '/main.dart.js',
  '/flutter.js',
  '/manifest.json',
  '/icons/Icon-192.png',
  '/icons/Icon-512.png',
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  );
});

self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        if (response) {
          return response;
        }
        return fetch(event.request);
      }
    )
  );
});
''';
  
  await swFile.writeAsString(swContent);
  print('📄 Service worker generated');
}

/// Optimize images in a directory
Future<void> optimizeImages(String dirPath) async {
  final dir = Directory(dirPath);
  final files = dir.listSync(recursive: true).whereType<File>();
  
  for (final file in files) {
    final extension = file.path.split('.').last.toLowerCase();
    if (['png', 'jpg', 'jpeg'].contains(extension)) {
      // Optimize with imagemagick
      await runCommand('convert', [
        file.path,
        '-strip',
        '-quality', '85',
        file.path,
      ], throwOnError: false);
    }
  }
}

/// Check if a command is available
Future<bool> isCommandAvailable(String command) async {
  try {
    final result = await Process.run('which', [command]);
    return result.exitCode == 0;
  } catch (e) {
    return false;
  }
}

/// Run a command and return the result
Future<ProcessResult> runCommand(
  String command,
  List<String> arguments, {
  bool throwOnError = true,
}) async {
  print('🔧 Running: $command ${arguments.join(' ')}');
  
  final result = await Process.run(command, arguments);
  
  if (result.exitCode != 0 && throwOnError) {
    print('❌ Command failed with exit code ${result.exitCode}');
    print('stdout: ${result.stdout}');
    print('stderr: ${result.stderr}');
    throw Exception('Command failed: $command ${arguments.join(' ')}');
  }
  
  return result;
}
