import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class NotificationsPage extends ConsumerWidget {
  const NotificationsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildNotificationSettings(context),
                  const SizedBox(height: 24),
                  _buildSectionHeader(context, 'Recent Notifications'),
                ],
              ),
            ),
          ),
          _buildNotificationsList(context),
        ],
      ),
    );
  }

  Widget _buildNotificationSettings(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.notifications,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Notification Settings',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Manage your notification preferences',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _showNotificationSettings(context),
            icon: const Icon(Icons.settings),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        TextButton(
          onPressed: () => _markAllAsRead(context),
          child: const Text('Mark all as read'),
        ),
      ],
    );
  }

  Widget _buildNotificationsList(BuildContext context) {
    final notifications = _getMockNotifications();
    
    if (notifications.isEmpty) {
      return SliverToBoxAdapter(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            children: [
              Icon(
                Icons.notifications_none,
                size: 64,
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
              ),
              const SizedBox(height: 16),
              Text(
                'No notifications yet',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'You\'ll see quest updates, achievements, and more here.',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            final notification = notifications[index];
            return _buildNotificationItem(context, notification, index);
          },
          childCount: notifications.length,
        ),
      ),
    );
  }

  Widget _buildNotificationItem(BuildContext context, Map<String, dynamic> notification, int index) {
    final isUnread = notification['unread'] as bool;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isUnread 
          ? Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.1)
          : Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isUnread
            ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)
            : Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _getNotificationIconColor(notification['type'] as String).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getNotificationIcon(notification['type'] as String),
              color: _getNotificationIconColor(notification['type'] as String),
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        notification['title'] as String,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: isUnread ? FontWeight.bold : FontWeight.w600,
                        ),
                      ),
                    ),
                    if (isUnread)
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary,
                          shape: BoxShape.circle,
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  notification['message'] as String,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _formatTime(notification['timestamp'] as DateTime),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                ),
              ],
            ),
          ),
          PopupMenuButton(
            onSelected: (value) => _handleNotificationAction(context, value, index),
            itemBuilder: (context) => [
              if (isUnread)
                const PopupMenuItem(
                  value: 'mark_read',
                  child: Text('Mark as read'),
                )
              else
                const PopupMenuItem(
                  value: 'mark_unread',
                  child: Text('Mark as unread'),
                ),
              const PopupMenuItem(
                value: 'delete',
                child: Text('Delete'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  IconData _getNotificationIcon(String type) {
    switch (type) {
      case 'quest':
        return Icons.flag;
      case 'achievement':
        return Icons.emoji_events;
      case 'social':
        return Icons.people;
      case 'system':
        return Icons.info;
      case 'reward':
        return Icons.card_giftcard;
      default:
        return Icons.notifications;
    }
  }

  Color _getNotificationIconColor(String type) {
    switch (type) {
      case 'quest':
        return Colors.blue;
      case 'achievement':
        return Colors.amber;
      case 'social':
        return Colors.green;
      case 'system':
        return Colors.grey;
      case 'reward':
        return Colors.purple;
      default:
        return Colors.blue;
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} minutes ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours} hours ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  void _showNotificationSettings(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notification Settings',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildSettingSwitch(context, 'Quest Updates', true),
            _buildSettingSwitch(context, 'Achievement Unlocked', true),
            _buildSettingSwitch(context, 'Social Interactions', false),
            _buildSettingSwitch(context, 'System Updates', true),
            _buildSettingSwitch(context, 'Marketing', false),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingSwitch(BuildContext context, String title, bool value) {
    return SwitchListTile(
      title: Text(title),
      value: value,
      onChanged: (newValue) {
        // Handle switch change
      },
      contentPadding: EdgeInsets.zero,
    );
  }

  void _markAllAsRead(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('All notifications marked as read')),
    );
  }

  void _handleNotificationAction(BuildContext context, String action, int index) {
    switch (action) {
      case 'mark_read':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Marked as read')),
        );
        break;
      case 'mark_unread':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Marked as unread')),
        );
        break;
      case 'delete':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Notification deleted')),
        );
        break;
    }
  }

  List<Map<String, dynamic>> _getMockNotifications() {
    return [
      {
        'title': 'Quest Completed!',
        'message': 'You\'ve completed "Morning Meditation" and earned 25 tokens!',
        'type': 'quest',
        'timestamp': DateTime.now().subtract(const Duration(minutes: 30)),
        'unread': true,
      },
      {
        'title': 'New Achievement Unlocked',
        'message': 'Congratulations! You\'ve unlocked "Early Bird" achievement.',
        'type': 'achievement',
        'timestamp': DateTime.now().subtract(const Duration(hours: 2)),
        'unread': true,
      },
      {
        'title': 'Friend Request',
        'message': 'Alex wants to connect with you on Quester.',
        'type': 'social',
        'timestamp': DateTime.now().subtract(const Duration(hours: 4)),
        'unread': false,
      },
      {
        'title': 'Weekly Rewards Available',
        'message': 'Check out this week\'s special rewards in the marketplace!',
        'type': 'reward',
        'timestamp': DateTime.now().subtract(const Duration(days: 1)),
        'unread': false,
      },
      {
        'title': 'System Update',
        'message': 'Quester has been updated with new features and improvements.',
        'type': 'system',
        'timestamp': DateTime.now().subtract(const Duration(days: 2)),
        'unread': false,
      },
    ];
  }
}
