# Go Language Fundamentals

## Overview

Go is a statically typed, compiled programming language designed for simplicity, efficiency, and excellent concurrency support. It's particularly well-suited for backend services, microservices, and system programming.

## Core Language Features

### Basic Syntax

#### Package Declaration
```go
package main

import (
    "fmt"
    "net/http"
    "encoding/json"
)

func main() {
    fmt.Println("Hello, World!")
}
```

#### Variable Declarations
```go
// Explicit type declaration
var name string = "<PERSON>"
var age int = 30
var isActive bool = true

// Type inference
var name = "<PERSON>"
var age = 30

// Short variable declaration (inside functions only)
name := "<PERSON>"
age := 30

// Multiple variable declaration
var (
    name string = "<PERSON>"
    age  int    = 30
)
```

#### Constants
```go
const (
    StatusActive   = "active"
    StatusInactive = "inactive"
    MaxRetries     = 3
    Pi             = 3.14159
)

// Iota for enumerated constants
const (
    Sunday = iota // 0
    Monday        // 1
    Tuesday       // 2
    Wednesday     // 3
    Thursday      // 4
    Friday        // 5
    Saturday      // 6
)
```

### Data Types

#### Basic Types
```go
// Numeric types
var i8 int8 = 127
var i16 int16 = 32767
var i32 int32 = 2147483647
var i64 int64 = 9223372036854775807
var ui8 uint8 = 255
var ui16 uint16 = 65535
var ui32 uint32 = 4294967295
var ui64 uint64 = 18446744073709551615

// Platform-dependent types
var i int = 42        // int32 or int64
var ui uint = 42      // uint32 or uint64
var ptr uintptr = 0   // Pointer-sized integer

// Floating point
var f32 float32 = 3.14
var f64 float64 = 3.14159265359

// Complex numbers
var c64 complex64 = 1 + 2i
var c128 complex128 = 1 + 2i

// String and byte
var s string = "Hello, World!"
var b byte = 'A'      // alias for uint8
var r rune = 'A'      // alias for int32, represents Unicode code point

// Boolean
var flag bool = true
```

#### Composite Types

##### Arrays
```go
// Fixed-size arrays
var arr [5]int = [5]int{1, 2, 3, 4, 5}
var arr2 = [3]string{"a", "b", "c"}
var arr3 = [...]int{1, 2, 3} // Compiler determines size

// Array operations
fmt.Println(len(arr))  // Length: 5
fmt.Println(arr[0])    // First element: 1
arr[0] = 10           // Modify element
```

##### Slices
```go
// Dynamic arrays
var slice []int = []int{1, 2, 3, 4, 5}
var slice2 = make([]int, 5)      // Length 5, capacity 5
var slice3 = make([]int, 5, 10)  // Length 5, capacity 10

// Slice operations
slice = append(slice, 6, 7, 8)   // Add elements
subSlice := slice[1:4]           // Subslice [2, 3, 4]
fmt.Println(len(slice))          // Length
fmt.Println(cap(slice))          // Capacity

// Slice iteration
for i, v := range slice {
    fmt.Printf("Index: %d, Value: %d\n", i, v)
}
```

##### Maps
```go
// Key-value pairs
var m map[string]int = make(map[string]int)
m["apple"] = 5
m["banana"] = 3

// Map literal
fruits := map[string]int{
    "apple":  5,
    "banana": 3,
    "orange": 8,
}

// Map operations
value, exists := fruits["apple"]  // Check existence
if exists {
    fmt.Println("Apple count:", value)
}

delete(fruits, "banana")  // Delete key

// Map iteration
for key, value := range fruits {
    fmt.Printf("%s: %d\n", key, value)
}
```

##### Structs
```go
// Struct definition
type Person struct {
    Name    string
    Age     int
    Email   string
    Address Address
}

type Address struct {
    Street  string
    City    string
    Country string
}

// Struct initialization
p1 := Person{
    Name:  "John Doe",
    Age:   30,
    Email: "<EMAIL>",
    Address: Address{
        Street:  "123 Main St",
        City:    "New York",
        Country: "USA",
    },
}

// Anonymous struct
config := struct {
    Host string
    Port int
}{
    Host: "localhost",
    Port: 8080,
}
```

### Functions

#### Function Declaration
```go
// Basic function
func add(a, b int) int {
    return a + b
}

// Multiple parameters of same type
func multiply(a, b, c int) int {
    return a * b * c
}

// Multiple return values
func divide(a, b float64) (float64, error) {
    if b == 0 {
        return 0, fmt.Errorf("division by zero")
    }
    return a / b, nil
}

// Named return values
func calculate(a, b int) (sum, product int) {
    sum = a + b
    product = a * b
    return // Naked return
}

// Variadic functions
func sum(numbers ...int) int {
    total := 0
    for _, num := range numbers {
        total += num
    }
    return total
}

// Usage
result := sum(1, 2, 3, 4, 5)
nums := []int{1, 2, 3, 4, 5}
result2 := sum(nums...) // Spread slice
```

#### Function Types and Closures
```go
// Function as type
type Operation func(int, int) int

func applyOperation(a, b int, op Operation) int {
    return op(a, b)
}

// Anonymous functions
add := func(a, b int) int {
    return a + b
}

// Closures
func counter() func() int {
    count := 0
    return func() int {
        count++
        return count
    }
}

// Usage
c := counter()
fmt.Println(c()) // 1
fmt.Println(c()) // 2
```

### Methods and Interfaces

#### Methods
```go
type Rectangle struct {
    Width, Height float64
}

// Method with value receiver
func (r Rectangle) Area() float64 {
    return r.Width * r.Height
}

// Method with pointer receiver
func (r *Rectangle) Scale(factor float64) {
    r.Width *= factor
    r.Height *= factor
}

// Usage
rect := Rectangle{Width: 10, Height: 5}
area := rect.Area()  // 50
rect.Scale(2)        // Width: 20, Height: 10
```

#### Interfaces
```go
// Interface definition
type Shape interface {
    Area() float64
    Perimeter() float64
}

// Implementing interface
type Circle struct {
    Radius float64
}

func (c Circle) Area() float64 {
    return 3.14159 * c.Radius * c.Radius
}

func (c Circle) Perimeter() float64 {
    return 2 * 3.14159 * c.Radius
}

// Interface usage
func printShapeInfo(s Shape) {
    fmt.Printf("Area: %.2f, Perimeter: %.2f\n", s.Area(), s.Perimeter())
}

// Empty interface
func printAnything(v interface{}) {
    fmt.Println(v)
}
```

### Error Handling

#### Basic Error Handling
```go
// Error interface
type error interface {
    Error() string
}

// Creating errors
import "errors"

func divide(a, b float64) (float64, error) {
    if b == 0 {
        return 0, errors.New("division by zero")
    }
    return a / b, nil
}

// Custom error types
type ValidationError struct {
    Field   string
    Message string
}

func (e ValidationError) Error() string {
    return fmt.Sprintf("validation error in field '%s': %s", e.Field, e.Message)
}

// Error handling patterns
result, err := divide(10, 0)
if err != nil {
    log.Printf("Error: %v", err)
    return
}
fmt.Println("Result:", result)
```

#### Error Wrapping (Go 1.13+)
```go
import "fmt"

func processFile(filename string) error {
    err := readFile(filename)
    if err != nil {
        return fmt.Errorf("failed to process file %s: %w", filename, err)
    }
    return nil
}

// Error unwrapping
if err := processFile("data.txt"); err != nil {
    var validationErr *ValidationError
    if errors.As(err, &validationErr) {
        // Handle validation error specifically
    }
    
    if errors.Is(err, os.ErrNotExist) {
        // Handle file not found
    }
}
```

### Concurrency

#### Goroutines
```go
// Starting goroutines
func worker(id int, jobs <-chan int, results chan<- int) {
    for job := range jobs {
        fmt.Printf("Worker %d processing job %d\n", id, job)
        time.Sleep(time.Second)
        results <- job * 2
    }
}

func main() {
    jobs := make(chan int, 100)
    results := make(chan int, 100)
    
    // Start workers
    for w := 1; w <= 3; w++ {
        go worker(w, jobs, results)
    }
    
    // Send jobs
    for j := 1; j <= 5; j++ {
        jobs <- j
    }
    close(jobs)
    
    // Collect results
    for r := 1; r <= 5; r++ {
        <-results
    }
}
```

#### Channels
```go
// Channel types
ch := make(chan int)           // Unbuffered channel
bufferedCh := make(chan int, 5) // Buffered channel
readOnly := make(<-chan int)    // Read-only channel
writeOnly := make(chan<- int)   // Write-only channel

// Channel operations
go func() {
    ch <- 42 // Send
}()
value := <-ch // Receive

// Channel with select
select {
case msg1 := <-ch1:
    fmt.Println("Received from ch1:", msg1)
case msg2 := <-ch2:
    fmt.Println("Received from ch2:", msg2)
case <-time.After(1 * time.Second):
    fmt.Println("Timeout")
default:
    fmt.Println("No channels ready")
}
```

#### Sync Package
```go
import "sync"

// Mutex for mutual exclusion
type Counter struct {
    mu    sync.Mutex
    value int
}

func (c *Counter) Increment() {
    c.mu.Lock()
    defer c.mu.Unlock()
    c.value++
}

func (c *Counter) Value() int {
    c.mu.Lock()
    defer c.mu.Unlock()
    return c.value
}

// WaitGroup for waiting for goroutines
var wg sync.WaitGroup

for i := 0; i < 5; i++ {
    wg.Add(1)
    go func(id int) {
        defer wg.Done()
        fmt.Printf("Goroutine %d\n", id)
    }(i)
}

wg.Wait() // Wait for all goroutines to complete

// Once for one-time initialization
var once sync.Once
var config *Config

func getConfig() *Config {
    once.Do(func() {
        config = loadConfig()
    })
    return config
}
```

### Package Management

#### Go Modules
```bash
# Initialize module
go mod init github.com/username/project

# Add dependencies
go get github.com/gorilla/mux
go get github.com/lib/pq@v1.10.0

# Update dependencies
go get -u ./...

# Remove unused dependencies
go mod tidy

# Vendor dependencies
go mod vendor
```

#### Import Paths
```go
// Standard library
import "fmt"
import "net/http"
import "encoding/json"

// Third-party packages
import "github.com/gorilla/mux"
import "github.com/lib/pq"

// Local packages
import "./internal/auth"
import "github.com/myorg/myproject/internal/database"

// Import aliases
import (
    "database/sql"
    _ "github.com/lib/pq" // Import for side effects
    pg "github.com/lib/pq"
    . "fmt" // Import into current namespace (not recommended)
)
```

### Testing

#### Unit Tests
```go
// math_test.go
package math

import "testing"

func TestAdd(t *testing.T) {
    result := Add(2, 3)
    expected := 5
    if result != expected {
        t.Errorf("Add(2, 3) = %d; want %d", result, expected)
    }
}

func TestAddTableDriven(t *testing.T) {
    tests := []struct {
        name     string
        a, b     int
        expected int
    }{
        {"positive numbers", 2, 3, 5},
        {"negative numbers", -2, -3, -5},
        {"mixed numbers", -2, 3, 1},
        {"zero", 0, 5, 5},
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result := Add(tt.a, tt.b)
            if result != tt.expected {
                t.Errorf("Add(%d, %d) = %d; want %d", tt.a, tt.b, result, tt.expected)
            }
        })
    }
}
```

#### Benchmarks
```go
func BenchmarkAdd(b *testing.B) {
    for i := 0; i < b.N; i++ {
        Add(2, 3)
    }
}

func BenchmarkAddParallel(b *testing.B) {
    b.RunParallel(func(pb *testing.PB) {
        for pb.Next() {
            Add(2, 3)
        }
    })
}
```

### Common Patterns and Best Practices

#### Error Handling Patterns
```go
// Early return pattern
func processData(data []byte) error {
    if len(data) == 0 {
        return errors.New("empty data")
    }
    
    parsed, err := parseData(data)
    if err != nil {
        return fmt.Errorf("failed to parse data: %w", err)
    }
    
    if err := validateData(parsed); err != nil {
        return fmt.Errorf("validation failed: %w", err)
    }
    
    return nil
}

// Error aggregation
type MultiError []error

func (m MultiError) Error() string {
    var msgs []string
    for _, err := range m {
        msgs = append(msgs, err.Error())
    }
    return strings.Join(msgs, "; ")
}
```

#### Context Usage
```go
import "context"

func processWithTimeout(ctx context.Context, data []byte) error {
    ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
    defer cancel()
    
    select {
    case result := <-processAsync(data):
        return result
    case <-ctx.Done():
        return ctx.Err()
    }
}

func httpHandler(w http.ResponseWriter, r *http.Request) {
    ctx := r.Context()
    
    // Pass context to downstream functions
    data, err := fetchDataWithContext(ctx, "key")
    if err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }
    
    json.NewEncoder(w).Encode(data)
}
```

#### Resource Management
```go
// Defer for cleanup
func processFile(filename string) error {
    file, err := os.Open(filename)
    if err != nil {
        return err
    }
    defer file.Close() // Always executed
    
    // Process file
    return nil
}

// Multiple defers (LIFO order)
func complexOperation() error {
    resource1, err := acquireResource1()
    if err != nil {
        return err
    }
    defer resource1.Release()
    
    resource2, err := acquireResource2()
    if err != nil {
        return err
    }
    defer resource2.Release()
    
    // Use resources
    return nil
}
```

### Performance Considerations

#### Memory Management
```go
// Avoid memory leaks with slices
func processLargeSlice(data []int) []int {
    // Bad: keeps reference to original slice
    // return data[100:200]
    
    // Good: copy to new slice
    result := make([]int, 100)
    copy(result, data[100:200])
    return result
}

// Pool for object reuse
var bufferPool = sync.Pool{
    New: func() interface{} {
        return make([]byte, 1024)
    },
}

func processData(data []byte) {
    buf := bufferPool.Get().([]byte)
    defer bufferPool.Put(buf)
    
    // Use buffer
}
```

#### Profiling
```go
import _ "net/http/pprof"

func main() {
    go func() {
        log.Println(http.ListenAndServe("localhost:6060", nil))
    }()
    
    // Your application code
}

// CPU profiling
// go tool pprof http://localhost:6060/debug/pprof/profile

// Memory profiling
// go tool pprof http://localhost:6060/debug/pprof/heap
```

This comprehensive guide covers the fundamental aspects of Go programming language, providing a solid foundation for backend development in the Quester project.