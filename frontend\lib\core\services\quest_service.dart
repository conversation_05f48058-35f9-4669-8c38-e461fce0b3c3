import '../models/quest_model.dart';
import 'base_api_service.dart';

class QuestService extends BaseApiService {
  QuestService(super.dio);

  // Get all quests with filtering and pagination
  Future<List<Quest>> getQuests({
    QuestType? type,
    QuestCategory? category,
    QuestDifficulty? difficulty,
    bool? isActive,
    bool? isFeatured,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (type != null) queryParams['type'] = type.name;
      if (category != null) queryParams['category'] = category.name;
      if (difficulty != null) queryParams['difficulty'] = difficulty.name;
      if (isActive != null) queryParams['is_active'] = isActive;
      if (isFeatured != null) queryParams['is_featured'] = isFeatured;

      final response = await dio.get('/quests', queryParameters: queryParams);
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => Quest.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get quest by ID
  Future<Quest> getQuest(String id) async {
    try {
      final response = await dio.get('/quests/$id');
      return Quest.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get quests by category
  Future<List<Quest>> getQuestsByCategory(QuestCategory category, {
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await dio.get('/quests/category/${category.name}', 
        queryParameters: {'page': page, 'limit': limit});
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => Quest.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get recommended quests
  Future<List<Quest>> getRecommendedQuests({int limit = 10}) async {
    try {
      final response = await dio.get('/quests/recommended', 
        queryParameters: {'limit': limit});
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => Quest.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get featured quests
  Future<List<Quest>> getFeaturedQuests({int limit = 5}) async {
    try {
      final response = await dio.get('/quests', 
        queryParameters: {'is_featured': true, 'limit': limit});
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => Quest.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Start a quest
  Future<QuestProgress> startQuest(String questId) async {
    try {
      final response = await dio.post('/quests/$questId/start');
      return QuestProgress.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Update quest progress
  Future<QuestProgress> updateQuestProgress(String questId, {
    required int currentStep,
    List<String>? completedSteps,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final data = <String, dynamic>{
        'current_step': currentStep,
      };
      
      if (completedSteps != null) data['completed_steps'] = completedSteps;
      if (metadata != null) data['metadata'] = metadata;

      final response = await dio.post('/quests/$questId/progress', data: data);
      return QuestProgress.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Complete a quest
  Future<QuestProgress> completeQuest(String questId) async {
    try {
      final response = await dio.post('/quests/$questId/complete');
      return QuestProgress.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Claim quest reward
  Future<Map<String, dynamic>> claimQuestReward(String questId) async {
    try {
      final response = await dio.post('/quests/$questId/claim');
      return response.data;
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get user's quest progress
  Future<List<QuestProgress>> getUserQuestProgress({
    QuestStatus? status,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (status != null) queryParams['status'] = status.name;

      final response = await dio.get('/user/quests', queryParameters: queryParams);
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => QuestProgress.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get quest categories
  Future<List<QuestCategory>> getQuestCategories() async {
    try {
      final response = await dio.get('/quests/categories');
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((categoryName) {
        return QuestCategory.values.firstWhere(
          (category) => category.name == categoryName,
          orElse: () => QuestCategory.skill,
        );
      }).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Create a new quest (admin/creator functionality)
  Future<Quest> createQuest({
    required String title,
    required String description,
    String? image,
    required QuestType type,
    required QuestCategory category,
    required QuestDifficulty difficulty,
    required int points,
    required int experience,
    required int estimatedDuration,
    int? maxParticipants,
    DateTime? startDate,
    DateTime? endDate,
    List<QuestStep>? steps,
    List<QuestReward>? rewards,
  }) async {
    try {
      final data = <String, dynamic>{
        'title': title,
        'description': description,
        'type': type.name,
        'category': category.name,
        'difficulty': difficulty.name,
        'points': points,
        'experience': experience,
        'estimated_duration': estimatedDuration,
        'is_active': true,
        'is_featured': false,
      };

      if (image != null) data['image'] = image;
      if (maxParticipants != null) data['max_participants'] = maxParticipants;
      if (startDate != null) data['start_date'] = startDate.toIso8601String();
      if (endDate != null) data['end_date'] = endDate.toIso8601String();
      if (steps != null) data['steps'] = steps.map((step) => step.toJson()).toList();
      if (rewards != null) data['rewards'] = rewards.map((reward) => reward.toJson()).toList();

      final response = await dio.post('/quests', data: data);
      return Quest.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Update a quest (admin/creator functionality)
  Future<Quest> updateQuest(String questId, {
    String? title,
    String? description,
    String? image,
    QuestType? type,
    QuestCategory? category,
    QuestDifficulty? difficulty,
    int? points,
    int? experience,
    int? estimatedDuration,
    int? maxParticipants,
    bool? isActive,
    bool? isFeatured,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final data = <String, dynamic>{};

      if (title != null) data['title'] = title;
      if (description != null) data['description'] = description;
      if (image != null) data['image'] = image;
      if (type != null) data['type'] = type.name;
      if (category != null) data['category'] = category.name;
      if (difficulty != null) data['difficulty'] = difficulty.name;
      if (points != null) data['points'] = points;
      if (experience != null) data['experience'] = experience;
      if (estimatedDuration != null) data['estimated_duration'] = estimatedDuration;
      if (maxParticipants != null) data['max_participants'] = maxParticipants;
      if (isActive != null) data['is_active'] = isActive;
      if (isFeatured != null) data['is_featured'] = isFeatured;
      if (startDate != null) data['start_date'] = startDate.toIso8601String();
      if (endDate != null) data['end_date'] = endDate.toIso8601String();

      final response = await dio.put('/quests/$questId', data: data);
      return Quest.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Delete a quest (admin functionality)
  Future<void> deleteQuest(String questId) async {
    try {
      await dio.delete('/quests/$questId');
    } catch (e) {
      throw handleError(e);
    }
  }
}
