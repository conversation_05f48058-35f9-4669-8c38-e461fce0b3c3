import '../models/wallet_model.dart';
import 'base_api_service.dart';

class WalletService extends BaseApiService {
  WalletService(super.dio);

  // Get user's wallet
  Future<Wallet> getWallet() async {
    try {
      final response = await dio.get('/user/wallet');
      return Wallet.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get wallet transactions with filtering and pagination
  Future<List<WalletTransaction>> getWalletTransactions({
    TransactionType? type,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (type != null) queryParams['type'] = type.name;

      final response = await dio.get('/user/wallet/transactions', queryParameters: queryParams);
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => WalletTransaction.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get wallet transaction by ID
  Future<WalletTransaction> getWalletTransaction(String transactionId) async {
    try {
      final response = await dio.get('/user/wallet/transactions/$transactionId');
      return WalletTransaction.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get wallet summary with statistics
  Future<WalletSummary> getWalletSummary() async {
    try {
      final response = await dio.get('/user/wallet/summary');
      return WalletSummary.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get recent transactions
  Future<List<WalletTransaction>> getRecentTransactions({int limit = 10}) async {
    try {
      final response = await dio.get('/user/wallet/transactions', 
        queryParameters: {'limit': limit, 'sort': 'created_at', 'order': 'desc'});
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => WalletTransaction.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get transactions by type
  Future<List<WalletTransaction>> getTransactionsByType(TransactionType type, {
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await dio.get('/user/wallet/transactions', queryParameters: {
        'type': type.name,
        'page': page,
        'limit': limit,
      });
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => WalletTransaction.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get credit transactions (earnings)
  Future<List<WalletTransaction>> getCreditTransactions({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await dio.get('/user/wallet/transactions/credits', 
        queryParameters: {'page': page, 'limit': limit});
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => WalletTransaction.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get debit transactions (spending)
  Future<List<WalletTransaction>> getDebitTransactions({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await dio.get('/user/wallet/transactions/debits', 
        queryParameters: {'page': page, 'limit': limit});
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => WalletTransaction.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get transactions for a specific date range
  Future<List<WalletTransaction>> getTransactionsByDateRange({
    required DateTime startDate,
    required DateTime endDate,
    TransactionType? type,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'start_date': startDate.toIso8601String(),
        'end_date': endDate.toIso8601String(),
        'page': page,
        'limit': limit,
      };

      if (type != null) queryParams['type'] = type.name;

      final response = await dio.get('/user/wallet/transactions', queryParameters: queryParams);
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => WalletTransaction.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get daily earnings
  Future<Map<String, dynamic>> getDailyEarnings() async {
    try {
      final response = await dio.get('/user/wallet/earnings/daily');
      return response.data['data'] ?? response.data;
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get weekly earnings
  Future<Map<String, dynamic>> getWeeklyEarnings() async {
    try {
      final response = await dio.get('/user/wallet/earnings/weekly');
      return response.data['data'] ?? response.data;
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get monthly earnings
  Future<Map<String, dynamic>> getMonthlyEarnings() async {
    try {
      final response = await dio.get('/user/wallet/earnings/monthly');
      return response.data['data'] ?? response.data;
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get spending statistics
  Future<Map<String, dynamic>> getSpendingStatistics() async {
    try {
      final response = await dio.get('/user/wallet/spending/statistics');
      return response.data['data'] ?? response.data;
    } catch (e) {
      throw handleError(e);
    }
  }

  // Check if user can afford an amount
  Future<bool> canAfford(int amount) async {
    try {
      final wallet = await getWallet();
      return wallet.canAfford(amount);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Admin operations
  // Add funds to user wallet (admin functionality)
  Future<WalletTransaction> addFunds({
    required String userId,
    required int amount,
    required String description,
    String? referenceId,
    String? referenceType,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final data = <String, dynamic>{
        'user_id': userId,
        'amount': amount,
        'description': description,
        'type': TransactionType.adminAdjustment.name,
      };

      if (referenceId != null) data['reference_id'] = referenceId;
      if (referenceType != null) data['reference_type'] = referenceType;
      if (metadata != null) data['metadata'] = metadata;

      final response = await dio.post('/admin/wallet/add-funds', data: data);
      return WalletTransaction.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Deduct funds from user wallet (admin functionality)
  Future<WalletTransaction> deductFunds({
    required String userId,
    required int amount,
    required String description,
    String? referenceId,
    String? referenceType,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final data = <String, dynamic>{
        'user_id': userId,
        'amount': amount,
        'description': description,
        'type': TransactionType.penalty.name,
      };

      if (referenceId != null) data['reference_id'] = referenceId;
      if (referenceType != null) data['reference_type'] = referenceType;
      if (metadata != null) data['metadata'] = metadata;

      final response = await dio.post('/admin/wallet/deduct-funds', data: data);
      return WalletTransaction.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get wallet statistics (admin functionality)
  Future<Map<String, dynamic>> getWalletStatistics() async {
    try {
      final response = await dio.get('/admin/wallet/statistics');
      return response.data['data'] ?? response.data;
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get all user wallets (admin functionality)
  Future<List<Wallet>> getAllWallets({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await dio.get('/admin/wallets', queryParameters: {
        'page': page,
        'limit': limit,
      });
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => Wallet.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get user wallet by user ID (admin functionality)
  Future<Wallet> getUserWallet(String userId) async {
    try {
      final response = await dio.get('/admin/users/$userId/wallet');
      return Wallet.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }
}
