// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_settings_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NotificationSettings _$NotificationSettingsFromJson(
        Map<String, dynamic> json) =>
    NotificationSettings(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      pushEnabled: json['push_enabled'] as bool,
      emailEnabled: json['email_enabled'] as bool,
      questNotifications: json['quest_notifications'] as bool,
      achievementNotifications: json['achievement_notifications'] as bool,
      socialNotifications: json['social_notifications'] as bool,
      marketingNotifications: json['marketing_notifications'] as bool,
      quietHoursEnabled: json['quiet_hours_enabled'] as bool,
      quietHoursStart: json['quiet_hours_start'] as String?,
      quietHoursEnd: json['quiet_hours_end'] as String?,
      frequency: $enumDecode(
          _$NotificationFrequencyEnumMap, json['notification_frequency']),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$NotificationSettingsToJson(
        NotificationSettings instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'push_enabled': instance.pushEnabled,
      'email_enabled': instance.emailEnabled,
      'quest_notifications': instance.questNotifications,
      'achievement_notifications': instance.achievementNotifications,
      'social_notifications': instance.socialNotifications,
      'marketing_notifications': instance.marketingNotifications,
      'quiet_hours_enabled': instance.quietHoursEnabled,
      'quiet_hours_start': instance.quietHoursStart,
      'quiet_hours_end': instance.quietHoursEnd,
      'notification_frequency':
          _$NotificationFrequencyEnumMap[instance.frequency]!,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

const _$NotificationFrequencyEnumMap = {
  NotificationFrequency.minimal: 'minimal',
  NotificationFrequency.normal: 'normal',
  NotificationFrequency.frequent: 'frequent',
};
