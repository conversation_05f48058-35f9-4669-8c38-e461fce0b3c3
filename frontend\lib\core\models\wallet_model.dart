import 'package:json_annotation/json_annotation.dart';

part 'wallet_model.g.dart';

@JsonSerializable()
class Wallet {
  final String id;
  @JsonKey(name: 'user_id')
  final String userId;
  final int balance;
  final String currency;
  @JsonKey(name: 'total_earned')
  final int totalEarned;
  @Json<PERSON>ey(name: 'total_spent')
  final int totalSpent;
  final List<WalletTransaction> transactions;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @Json<PERSON>ey(name: 'updated_at')
  final DateTime updatedAt;

  const Wallet({
    required this.id,
    required this.userId,
    required this.balance,
    required this.currency,
    required this.totalEarned,
    required this.totalSpent,
    required this.transactions,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Wallet.fromJson(Map<String, dynamic> json) => _$WalletFromJson(json);
  Map<String, dynamic> toJson() => _$WalletToJson(this);

  /// Check if the wallet can afford a specific amount
  bool canAfford(int amount) => balance >= amount;

  /// Get available balance
  int get availableBalance => balance;
}

@JsonSerializable()
class WalletTransaction {
  final String id;
  @JsonKey(name: 'wallet_id')
  final String walletId;
  @JsonKey(name: 'user_id')
  final String userId;
  final TransactionType type;
  final int amount;
  final String currency;
  final String description;
  final WalletTransactionStatus status;
  @JsonKey(name: 'reference_id')
  final String? referenceId;
  @JsonKey(name: 'reference_type')
  final String? referenceType;
  final Map<String, dynamic>? metadata;
  @JsonKey(name: 'balance_before')
  final int balanceBefore;
  @JsonKey(name: 'balance_after')
  final int balanceAfter;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  const WalletTransaction({
    required this.id,
    required this.walletId,
    required this.userId,
    required this.type,
    required this.amount,
    required this.currency,
    required this.description,
    required this.status,
    this.referenceId,
    this.referenceType,
    this.metadata,
    required this.balanceBefore,
    required this.balanceAfter,
    required this.createdAt,
  });

  factory WalletTransaction.fromJson(Map<String, dynamic> json) => _$WalletTransactionFromJson(json);
  Map<String, dynamic> toJson() => _$WalletTransactionToJson(this);
}

@JsonEnum()
enum TransactionType {
  @JsonValue('quest_reward')
  questReward,
  @JsonValue('achievement_reward')
  achievementReward,
  @JsonValue('daily_bonus')
  dailyBonus,
  @JsonValue('purchase')
  purchase,
  @JsonValue('refund')
  refund,
  @JsonValue('admin_adjustment')
  adminAdjustment,
  @JsonValue('bonus')
  bonus,
  @JsonValue('penalty')
  penalty,
}

extension TransactionTypeExtension on TransactionType {
  String get displayName {
    switch (this) {
      case TransactionType.questReward:
        return 'Quest Reward';
      case TransactionType.achievementReward:
        return 'Achievement Reward';
      case TransactionType.dailyBonus:
        return 'Daily Bonus';
      case TransactionType.purchase:
        return 'Purchase';
      case TransactionType.refund:
        return 'Refund';
      case TransactionType.adminAdjustment:
        return 'Admin Adjustment';
      case TransactionType.bonus:
        return 'Bonus';
      case TransactionType.penalty:
        return 'Penalty';
    }
  }

  bool get isCredit {
    switch (this) {
      case TransactionType.questReward:
      case TransactionType.achievementReward:
      case TransactionType.dailyBonus:
      case TransactionType.refund:
      case TransactionType.bonus:
        return true;
      case TransactionType.purchase:
      case TransactionType.penalty:
        return false;
      case TransactionType.adminAdjustment:
        return true; // Can be either, but default to credit
    }
  }

  bool get isDebit => !isCredit;

  String get iconName {
    switch (this) {
      case TransactionType.questReward:
        return 'assignment_turned_in';
      case TransactionType.achievementReward:
        return 'emoji_events';
      case TransactionType.dailyBonus:
        return 'today';
      case TransactionType.purchase:
        return 'shopping_cart';
      case TransactionType.refund:
        return 'undo';
      case TransactionType.adminAdjustment:
        return 'admin_panel_settings';
      case TransactionType.bonus:
        return 'card_giftcard';
      case TransactionType.penalty:
        return 'warning';
    }
  }

  bool get isPositive => isCredit;
}

// Additional enums for compatibility with existing code
@JsonEnum()
enum WalletTransactionType {
  @JsonValue('credit')
  credit,
  @JsonValue('debit')
  debit,
  @JsonValue('reward')
  reward,
  @JsonValue('purchase')
  purchase,
  @JsonValue('transfer')
  transfer,
  @JsonValue('refund')
  refund,
}

@JsonEnum()
enum WalletTransactionStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('completed')
  completed,
  @JsonValue('failed')
  failed,
  @JsonValue('cancelled')
  cancelled,
}

@JsonSerializable()
class WalletSummary {
  final String id;
  @JsonKey(name: 'user_id')
  final String userId;
  final Wallet wallet;
  @JsonKey(name: 'recent_transactions')
  final List<WalletTransaction> recentTransactions;
  @JsonKey(name: 'monthly_earned')
  final int monthlyEarned;
  @JsonKey(name: 'monthly_spent')
  final int monthlySpent;
  @JsonKey(name: 'weekly_earned')
  final int weeklyEarned;
  @JsonKey(name: 'weekly_spent')
  final int weeklySpent;
  @JsonKey(name: 'total_transactions')
  final int totalTransactions;
  @JsonKey(name: 'average_transaction')
  final double averageTransaction;
  @JsonKey(name: 'largest_transaction')
  final int largestTransaction;
  @JsonKey(name: 'earning_streak')
  final int earningStreak; // days
  @JsonKey(name: 'last_transaction_at')
  final DateTime? lastTransactionAt;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  const WalletSummary({
    required this.id,
    required this.userId,
    required this.wallet,
    required this.recentTransactions,
    required this.monthlyEarned,
    required this.monthlySpent,
    required this.weeklyEarned,
    required this.weeklySpent,
    required this.totalTransactions,
    required this.averageTransaction,
    required this.largestTransaction,
    required this.earningStreak,
    this.lastTransactionAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory WalletSummary.fromJson(Map<String, dynamic> json) => _$WalletSummaryFromJson(json);
  Map<String, dynamic> toJson() => _$WalletSummaryToJson(this);

  /// Get net monthly change (earned - spent)
  int get monthlyNetChange => monthlyEarned - monthlySpent;

  /// Get net weekly change (earned - spent)
  int get weeklyNetChange => weeklyEarned - weeklySpent;

  /// Check if user is on an earning streak
  bool get hasEarningStreak => earningStreak > 0;

  /// Get spending ratio (spent / earned) for the month
  double get monthlySpendingRatio {
    if (monthlyEarned == 0) return 0.0;
    return monthlySpent / monthlyEarned;
  }

  /// Check if user is a big spender (spending > 80% of earnings)
  bool get isBigSpender => monthlySpendingRatio > 0.8;

  /// Check if user is a saver (spending < 20% of earnings)
  bool get isSaver => monthlySpendingRatio < 0.2;
}
