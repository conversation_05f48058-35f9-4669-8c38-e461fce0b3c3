# Quester Root Environment Variables

# Application
APP_ENV=development
APP_NAME=quester

# PostgreSQL
DB_USER=keshabalive
DB_PASSWORD=9871
DB_NAME=quester
DB_HOST=postgres
DB_PORT=5432
DB_SSL_MODE=disable

# Redis
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# Backend
JWT_SECRET=your-secret-key-here
CORS_ALLOW_ORIGINS=http://localhost:9000
CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOW_HEADERS=Origin,Content-Type,Accept,Authorization
CORS_ALLOW_CREDENTIALS=true
CORS_EXPOSE_HEADERS=
CORS_MAX_AGE=86400
API_VERSION=1

# Frontend
VITE_API_BASE_URL=http://localhost:8080/api/v1
VITE_WS_BASE_URL=ws://localhost:8080/ws
