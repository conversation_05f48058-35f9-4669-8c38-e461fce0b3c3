import 'package:flutter/material.dart';
import '../../core/utils/responsive_helper.dart';
import '../../core/theme/app_theme.dart';

/// Adaptive card widget that adjusts its layout based on screen size
/// Following <PERSON>lut<PERSON>'s responsive design best practices
class AdaptiveCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final double? elevation;
  final Color? color;
  final BorderRadius? borderRadius;
  final bool adaptivePadding;
  final bool adaptiveElevation;

  const AdaptiveCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.elevation,
    this.color,
    this.borderRadius,
    this.adaptivePadding = true,
    this.adaptiveElevation = true,
  });

  @override
  Widget build(BuildContext context) {
    // Adaptive padding based on screen size
    EdgeInsets cardPadding;
    if (adaptivePadding && padding == null) {
      cardPadding = ResponsiveHelper.getResponsiveValue(
        context,
        mobile: const EdgeInsets.all(16),
        tablet: const EdgeInsets.all(20),
        desktop: const EdgeInsets.all(24),
        large: const EdgeInsets.all(28),
      );
    } else {
      cardPadding = padding ?? const EdgeInsets.all(16);
    }

    // Adaptive elevation based on screen size
    double cardElevation;
    if (adaptiveElevation && elevation == null) {
      cardElevation = ResponsiveHelper.getResponsiveValue(
        context,
        mobile: 2.0,
        tablet: 4.0,
        desktop: 6.0,
        large: 8.0,
      );
    } else {
      cardElevation = elevation ?? 2.0;
    }

    // Adaptive border radius
    BorderRadius cardBorderRadius = borderRadius ?? ResponsiveHelper.getResponsiveValue(
      context,
      mobile: BorderRadius.circular(12),
      tablet: BorderRadius.circular(16),
      desktop: BorderRadius.circular(20),
      large: BorderRadius.circular(24),
    );

    return Container(
      margin: margin ?? ResponsiveHelper.getResponsiveMargin(context),
      child: Card(
        elevation: cardElevation,
        color: color,
        shape: RoundedRectangleBorder(borderRadius: cardBorderRadius),
        child: Padding(
          padding: cardPadding,
          child: child,
        ),
      ),
    );
  }
}

/// Adaptive grid card for displaying items in a responsive grid
class AdaptiveGridCard extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final String? heroTag;
  final double? aspectRatio;

  const AdaptiveGridCard({
    super.key,
    required this.child,
    this.onTap,
    this.heroTag,
    this.aspectRatio,
  });

  @override
  Widget build(BuildContext context) {
    final cardWidth = ResponsiveHelper.getCardWidth(context);
    final cardAspectRatio = aspectRatio ?? ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 1.2,
      tablet: 1.1,
      desktop: 1.0,
      large: 0.9,
    );

    Widget cardContent = AdaptiveCard(
      child: AspectRatio(
        aspectRatio: cardAspectRatio ?? 1.0,
        child: child,
      ),
    );

    if (heroTag != null) {
      cardContent = Hero(
        tag: heroTag!,
        child: cardContent,
      );
    }

    if (onTap != null) {
      cardContent = InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: cardContent,
      );
    }

    return SizedBox(
      width: cardWidth,
      child: cardContent,
    );
  }
}

/// Adaptive list tile that adjusts its layout for different screen sizes
class AdaptiveListTile extends StatelessWidget {
  final Widget? leading;
  final Widget title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final bool dense;

  const AdaptiveListTile({
    super.key,
    this.leading,
    required this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.dense = false,
  });

  @override
  Widget build(BuildContext context) {
    final isDesktop = ResponsiveHelper.isDesktop(context);

    // Adaptive content padding
    final contentPadding = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      tablet: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      desktop: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
    );

    // Adaptive leading size
    final leadingSize = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 40.0,
      tablet: 48.0,
      desktop: 56.0,
    );

    Widget? adaptiveLeading;
    if (leading != null) {
      adaptiveLeading = SizedBox(
        width: leadingSize,
        height: leadingSize,
        child: leading,
      );
    }

    return ListTile(
      leading: adaptiveLeading,
      title: title,
      subtitle: subtitle,
      trailing: trailing,
      onTap: onTap,
      dense: dense && !isDesktop, // Don't use dense on desktop
      contentPadding: contentPadding,
      visualDensity: isDesktop 
          ? VisualDensity.comfortable 
          : VisualDensity.compact,
    );
  }
}

/// Adaptive button that adjusts its size based on screen size
class AdaptiveButton extends StatelessWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final ButtonStyle? style;
  final bool isPrimary;

  const AdaptiveButton({
    super.key,
    required this.child,
    this.onPressed,
    this.style,
    this.isPrimary = true,
  });

  @override
  Widget build(BuildContext context) {
    final adaptiveComponentSizes = Theme.of(context).extension<AdaptiveComponentSizes>();

    // Adaptive button height
    final buttonHeight = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: adaptiveComponentSizes?.buttonHeightCompact ?? 48.0,
      tablet: adaptiveComponentSizes?.buttonHeightMedium ?? 52.0,
      desktop: adaptiveComponentSizes?.buttonHeightExpanded ?? 56.0,
    );

    // Adaptive padding
    final buttonPadding = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: const EdgeInsets.symmetric(horizontal: 16),
      tablet: const EdgeInsets.symmetric(horizontal: 20),
      desktop: const EdgeInsets.symmetric(horizontal: 24),
    );

    final adaptiveStyle = (style ?? (isPrimary 
        ? ElevatedButton.styleFrom() 
        : OutlinedButton.styleFrom())).copyWith(
      minimumSize: WidgetStateProperty.all(Size(0, buttonHeight)),
      padding: WidgetStateProperty.all(buttonPadding),
    );

    if (isPrimary) {
      return ElevatedButton(
        onPressed: onPressed,
        style: adaptiveStyle,
        child: child,
      );
    } else {
      return OutlinedButton(
        onPressed: onPressed,
        style: adaptiveStyle,
        child: child,
      );
    }
  }
}

