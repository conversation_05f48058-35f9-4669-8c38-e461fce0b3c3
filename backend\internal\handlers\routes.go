package handlers

import (
	"quester-backend/internal/config"
	"quester-backend/internal/middleware"

	"github.com/gofiber/fiber/v2"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// SetupRoutes registers all API routes
func SetupRoutes(app *fiber.App, db *gorm.DB, redis *redis.Client, cfg *config.Config) {
	// User management
	app.Post("/api/register", RegisterUser)
	app.Post("/api/login", LoginUser)
	app.Get("/api/profile", middleware.AuthMiddleware, GetProfile)
	app.Put("/api/profile", middleware.AuthMiddleware, UpdateProfile)
	app.Get("/api/users", middleware.AuthMiddleware, ListUsers)

	// Role & permission
	app.Get("/api/roles", middleware.AuthMiddleware, ListRoles)
	app.Get("/api/permissions", middleware.AuthMiddleware, ListPermissions)

	// Quest system
	app.Get("/api/quests", middleware.AuthMiddleware, ListQuests)
	app.Post("/api/quests", middleware.AuthMiddleware, CreateQuest)
	app.Get("/api/quests/:id", middleware.AuthMiddleware, GetQuest)
	app.Put("/api/quests/:id", middleware.AuthMiddleware, UpdateQuest)
	app.Delete("/api/quests/:id", middleware.AuthMiddleware, DeleteQuest)
	app.Post("/api/quests/:id/start", middleware.AuthMiddleware, StartQuest)
	app.Post("/api/quests/:id/complete", middleware.AuthMiddleware, CompleteQuest)

	// Achievements
	app.Get("/api/achievements", middleware.AuthMiddleware, ListAchievements)
	app.Post("/api/achievements/claim", middleware.AuthMiddleware, ClaimAchievement)

	// Wallet & transactions
	app.Get("/api/wallet", middleware.AuthMiddleware, GetWallet)
	app.Post("/api/wallet/transfer", middleware.AuthMiddleware, TransferCurrency)
	app.Get("/api/wallet/transactions", middleware.AuthMiddleware, ListTransactions)

	// Marketplace
	app.Get("/api/marketplace", middleware.AuthMiddleware, ListMarketplaceItems)
	app.Post("/api/marketplace", middleware.AuthMiddleware, CreateMarketplaceItem)
	app.Post("/api/marketplace/:id/buy", middleware.AuthMiddleware, BuyMarketplaceItem)
	app.Post("/api/marketplace/:id/trade", middleware.AuthMiddleware, TradeMarketplaceItem)

	// Notifications
	app.Get("/api/notifications", middleware.AuthMiddleware, ListNotifications)
	app.Post("/api/notifications/read", middleware.AuthMiddleware, MarkNotificationRead)

	// Leaderboard
	app.Get("/api/leaderboard", middleware.AuthMiddleware, GetLeaderboard)
}
