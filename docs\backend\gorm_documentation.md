# GORM Guides

The fantastic ORM library for Golang aims to be developer friendly. <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>

## Overview

* Full-Featured ORM <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>
* Associations (Has One, Has Many, Belongs To, Many To Many, Polymorphism, Single-table inheritance) <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>
* Hooks (Before/After Create/Save/Update/Delete/Find) <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>
* Eager loading with Preload, Joins <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>
* Transactions, Nested Transactions, Save Point, RollbackTo to Saved Point <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>
* Context, Prepared Statement Mode, DryRun Mode <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>
* Batch Insert, FindInBatches, Find/Create with Map, CRUD with SQL Expr and Context Valuer <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>
* SQL Builder, Upsert, Locking, Optimizer/Index/Comment Hints, Named Argument, SubQuery <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>
* Composite Primary Key, Indexes, Constraints <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>
* Auto Migrations <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>
* Logger <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>
* Extendable, flexible plugin API: Database Resolver (Multiple Databases, Read/Write Splitting) / Prometheus... <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>
* Every feature comes with tests <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>
* Developer Friendly <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>

## Install

```go
go get -u gorm.io/gorm <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>
go get -u gorm.io/driver/sqlite <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>
```

## Quick Start

```go
package main

import (
  "gorm.io/gorm"
  "gorm.io/driver/sqlite"
)

type Product struct {
  gorm.Model
  Code  string
  Price uint
}

func main() {
  db, err := gorm.Open(sqlite.Open("test.db"), &gorm.Config{})
  if err != nil {
    panic("failed to connect database")
  }

  // Migrate the schema
  db.AutoMigrate(&Product{}) <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>

  // Create
  db.Create(&Product{Code: "D42", Price: 100}) <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>

  // Read
  var product Product
  db.First(&product, 1) // find product with integer primary key <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>
  db.First(&product, "code = ?", "D42") // find product with code D42 <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>

  // Update - update product's price to 200
  db.Model(&product).Update("Price", 200) <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>
  // Update - update multiple fields
  db.Model(&product).Updates(Product{Price: 200, Code: "F42"}) // non-zero fields <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>
  db.Model(&product).Updates(map[string]interface{}{"Price": 200, "Code": "F42"}) <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>

  // Delete - delete product
  db.Delete(&product, 1) <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>
}
```

*Content based on web search result from gorm.io.* <mcreference link="https://gorm.io/docs/" index="0">0</mcreference>