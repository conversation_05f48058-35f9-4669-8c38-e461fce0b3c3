// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Achievement _$AchievementFromJson(Map<String, dynamic> json) => Achievement(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      icon: json['icon'] as String?,
      image: json['image'] as String?,
      category: $enumDecode(_$AchievementCategoryEnumMap, json['category']),
      type: $enumDecode(_$AchievementTypeEnumMap, json['type']),
      rarity: $enumDecode(_$AchievementRarityEnumMap, json['rarity']),
      points: (json['points'] as num).toInt(),
      experience: (json['experience'] as num).toInt(),
      unlockCriteria: json['unlock_criteria'] as Map<String, dynamic>,
      isHidden: json['is_hidden'] as bool,
      isActive: json['is_active'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$AchievementToJson(Achievement instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'icon': instance.icon,
      'image': instance.image,
      'category': _$AchievementCategoryEnumMap[instance.category]!,
      'type': _$AchievementTypeEnumMap[instance.type]!,
      'rarity': _$AchievementRarityEnumMap[instance.rarity]!,
      'points': instance.points,
      'experience': instance.experience,
      'unlock_criteria': instance.unlockCriteria,
      'is_hidden': instance.isHidden,
      'is_active': instance.isActive,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

const _$AchievementCategoryEnumMap = {
  AchievementCategory.quest: 'quest',
  AchievementCategory.quests: 'quests',
  AchievementCategory.social: 'social',
  AchievementCategory.progression: 'progression',
  AchievementCategory.collection: 'collection',
  AchievementCategory.skill: 'skill',
  AchievementCategory.special: 'special',
  AchievementCategory.seasonal: 'seasonal',
  AchievementCategory.exploration: 'exploration',
};

const _$AchievementTypeEnumMap = {
  AchievementType.milestone: 'milestone',
  AchievementType.cumulative: 'cumulative',
  AchievementType.streak: 'streak',
  AchievementType.challenge: 'challenge',
  AchievementType.discovery: 'discovery',
  AchievementType.mastery: 'mastery',
};

const _$AchievementRarityEnumMap = {
  AchievementRarity.common: 'common',
  AchievementRarity.uncommon: 'uncommon',
  AchievementRarity.rare: 'rare',
  AchievementRarity.epic: 'epic',
  AchievementRarity.legendary: 'legendary',
};

AchievementRequirement _$AchievementRequirementFromJson(
        Map<String, dynamic> json) =>
    AchievementRequirement(
      id: json['id'] as String,
      type: $enumDecode(_$AchievementRequirementTypeEnumMap, json['type']),
      target: json['target'] as String,
      requiredValue: (json['requiredValue'] as num).toInt(),
      currentValue: (json['currentValue'] as num).toInt(),
      isCompleted: json['isCompleted'] as bool,
    );

Map<String, dynamic> _$AchievementRequirementToJson(
        AchievementRequirement instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$AchievementRequirementTypeEnumMap[instance.type]!,
      'target': instance.target,
      'requiredValue': instance.requiredValue,
      'currentValue': instance.currentValue,
      'isCompleted': instance.isCompleted,
    };

const _$AchievementRequirementTypeEnumMap = {
  AchievementRequirementType.questCompletion: 'quest_completion',
  AchievementRequirementType.pointsEarned: 'points_earned',
  AchievementRequirementType.levelReached: 'level_reached',
  AchievementRequirementType.daysActive: 'days_active',
  AchievementRequirementType.socialConnections: 'social_connections',
  AchievementRequirementType.marketplaceTransactions:
      'marketplace_transactions',
  AchievementRequirementType.itemsCollected: 'items_collected',
};

AchievementReward _$AchievementRewardFromJson(Map<String, dynamic> json) =>
    AchievementReward(
      id: json['id'] as String,
      type: $enumDecode(_$AchievementRewardTypeEnumMap, json['type']),
      name: json['name'] as String,
      description: json['description'] as String?,
      quantity: (json['quantity'] as num).toInt(),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$AchievementRewardToJson(AchievementReward instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$AchievementRewardTypeEnumMap[instance.type]!,
      'name': instance.name,
      'description': instance.description,
      'quantity': instance.quantity,
      'metadata': instance.metadata,
    };

const _$AchievementRewardTypeEnumMap = {
  AchievementRewardType.points: 'points',
  AchievementRewardType.experience: 'experience',
  AchievementRewardType.currency: 'currency',
  AchievementRewardType.item: 'item',
  AchievementRewardType.badge: 'badge',
  AchievementRewardType.title: 'title',
};

UserAchievement _$UserAchievementFromJson(Map<String, dynamic> json) =>
    UserAchievement(
      id: json['id'] as String,
      userId: json['userId'] as String,
      achievementId: json['achievementId'] as String,
      achievement:
          Achievement.fromJson(json['achievement'] as Map<String, dynamic>),
      unlockedAt: DateTime.parse(json['unlockedAt'] as String),
      isNotified: json['isNotified'] as bool,
    );

Map<String, dynamic> _$UserAchievementToJson(UserAchievement instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'achievementId': instance.achievementId,
      'achievement': instance.achievement,
      'unlockedAt': instance.unlockedAt.toIso8601String(),
      'isNotified': instance.isNotified,
    };
