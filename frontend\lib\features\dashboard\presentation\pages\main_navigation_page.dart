import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/services/app_router.dart';
import '../../../../shared/widgets/adaptive_navigation.dart';
import '../../../../shared/widgets/theme_toggle.dart';
import '../../../../shared/widgets/websocket_status.dart';
import '../../../../shared/widgets/real_time_listeners.dart';

class MainNavigationPage extends ConsumerStatefulWidget {
  final Widget child;

  const MainNavigationPage({
    super.key,
    required this.child,
  });

  @override
  ConsumerState<MainNavigationPage> createState() => _MainNavigationPageState();
}

class _MainNavigationPageState extends ConsumerState<MainNavigationPage> {
  int _selectedIndex = 0;

  static const List<NavigationItem> _navigationItems = [
    NavigationItem(
      icon: Icons.dashboard_outlined,
      selectedIcon: Icons.dashboard,
      label: 'Dashboard',
      route: AppRoutes.dashboard,
    ),
    NavigationItem(
      icon: Icons.explore_outlined,
      selectedIcon: Icons.explore,
      label: 'Quests',
      route: AppRoutes.quests,
    ),
    NavigationItem(
      icon: Icons.emoji_events_outlined,
      selectedIcon: Icons.emoji_events,
      label: 'Achievements',
      route: AppRoutes.achievements,
    ),
    NavigationItem(
      icon: Icons.account_balance_wallet_outlined,
      selectedIcon: Icons.account_balance_wallet,
      label: 'Wallet',
      route: AppRoutes.wallet,
    ),
    NavigationItem(
      icon: Icons.store_outlined,
      selectedIcon: Icons.store,
      label: 'Market',
      route: AppRoutes.marketplace,
    ),
  ];

  void _onItemTapped(int index) {
    if (index != _selectedIndex) {
      setState(() {
        _selectedIndex = index;
      });
      context.go(_navigationItems[index].route);
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateSelectedIndex();
  }

  void _updateSelectedIndex() {
    final String currentRoute = GoRouterState.of(context).uri.path;
    
    for (int i = 0; i < _navigationItems.length; i++) {
      if (currentRoute.startsWith(_navigationItems[i].route)) {
        if (_selectedIndex != i) {
          setState(() {
            _selectedIndex = i;
          });
        }
        break;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return RealTimeDataSync(
      child: AdaptiveNavigation(
        selectedIndex: _selectedIndex,
        onDestinationSelected: _onItemTapped,
        items: _navigationItems,
        actions: [
          // WebSocket status indicator
          const AppBarWebSocketStatus(),
          // Notifications button (replaces FAB for better adaptive design)
          IconButton(
            onPressed: () => context.push(AppRoutes.notifications),
            icon: const Icon(Icons.notifications_outlined),
            tooltip: 'Notifications',
          ),
          // Leaderboard button
          IconButton(
            onPressed: () => context.push(AppRoutes.leaderboard),
            icon: const Icon(Icons.leaderboard_outlined),
            tooltip: 'Leaderboard',
          ),
          // Theme toggle button
          const ThemeToggleButton(),
          // Profile button
          IconButton(
            onPressed: () => context.push(AppRoutes.profile),
            icon: const Icon(Icons.person_outline),
            tooltip: 'Profile',
          ),
          // Settings button
          IconButton(
            onPressed: () => context.push(AppRoutes.settings),
            icon: const Icon(Icons.settings_outlined),
            tooltip: 'Settings',
          ),
        ],
      child: Scaffold(
        appBar: AdaptiveAppBar(
          title: _getPageTitle(),
          actions: [
            // WebSocket status indicator
            const AppBarWebSocketStatus(),
            // Notifications button (replaces FAB for better adaptive design)
            IconButton(
              onPressed: () => context.push(AppRoutes.notifications),
              icon: const Icon(Icons.notifications_outlined),
              tooltip: 'Notifications',
            ),
            // Leaderboard button
            IconButton(
              onPressed: () => context.push(AppRoutes.leaderboard),
              icon: const Icon(Icons.leaderboard_outlined),
              tooltip: 'Leaderboard',
            ),
            // Theme toggle button
            const ThemeToggleButton(),
            // Profile button
            IconButton(
              onPressed: () => context.push(AppRoutes.profile),
              icon: const Icon(Icons.person_outline),
              tooltip: 'Profile',
            ),
            // Settings button
            IconButton(
              onPressed: () => context.push(AppRoutes.settings),
              icon: const Icon(Icons.settings_outlined),
              tooltip: 'Settings',
            ),
          ],
        ),
        body: widget.child,
      ),
    ),
    );
  }

  String _getPageTitle() {
    switch (_selectedIndex) {
      case 0:
        return 'Dashboard';
      case 1:
        return 'Quests';
      case 2:
        return 'Achievements';
      case 3:
        return 'Wallet';
      case 4:
        return 'Marketplace';
      default:
        return 'Quester';
    }
  }
}


