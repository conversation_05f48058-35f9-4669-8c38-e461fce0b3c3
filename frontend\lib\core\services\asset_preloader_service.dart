import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../assets/app_assets.dart';
import '../logging/app_logger.dart';
import '../utils/performance_monitor.dart';

/// Service for preloading assets to improve app performance
class AssetPreloaderService {
  static final AssetPreloaderService _instance = AssetPreloaderService._internal();
  factory AssetPreloaderService() => _instance;
  AssetPreloaderService._internal();

  final Set<String> _preloadedAssets = {};
  bool _isPreloading = false;

  /// Check if an asset is preloaded
  bool isPreloaded(String assetPath) => _preloadedAssets.contains(assetPath);

  /// Check if preloading is in progress
  bool get isPreloading => _isPreloading;

  /// Get preloading progress (0.0 to 1.0)
  double get preloadingProgress {
    if (!_isPreloading) return 1.0;
    final totalAssets = AppAssets.getAllAssets().length;
    return _preloadedAssets.length / totalAssets;
  }

  /// Preload critical assets (should be called during app initialization)
  Future<void> preloadCriticalAssets(BuildContext context) async {
    if (_isPreloading) return;

    _isPreloading = true;
    AppLogger.info('Starting critical asset preloading');

    try {
      await PerformanceMonitor.timeAsync('preload_critical_assets', () async {
        // Preload critical images
        await _preloadImages(context, AppAssets.criticalImages);
        
        // Preload critical SVG icons
        await _preloadSvgIcons(AppAssets.criticalIcons);
        
        AppLogger.info('Critical assets preloaded successfully');
      });
    } catch (error, stackTrace) {
      AppLogger.error('Failed to preload critical assets', error: error, stackTrace: stackTrace);
    } finally {
      _isPreloading = false;
    }
  }

  /// Preload assets by category
  Future<void> preloadAssetsByCategory(BuildContext context, AssetCategory category) async {
    final assets = AppAssets.getAssetsByCategory(category);
    AppLogger.info('Preloading ${category.name} assets (${assets.length} items)');

    try {
      await PerformanceMonitor.timeAsync('preload_${category.name}_assets', () async {
        final images = assets.where((asset) => asset.endsWith('.png') || asset.endsWith('.jpg')).toList();
        final svgs = assets.where((asset) => asset.endsWith('.svg')).toList();

        await Future.wait([
          _preloadImages(context, images),
          _preloadSvgIcons(svgs),
        ]);

        AppLogger.info('${category.name} assets preloaded successfully');
      });
    } catch (error, stackTrace) {
      AppLogger.error('Failed to preload ${category.name} assets', error: error, stackTrace: stackTrace);
    }
  }

  /// Preload all assets (use sparingly, preferably in background)
  Future<void> preloadAllAssets(BuildContext context) async {
    if (_isPreloading) return;

    _isPreloading = true;
    AppLogger.info('Starting full asset preloading');

    try {
      await PerformanceMonitor.timeAsync('preload_all_assets', () async {
        final allAssets = AppAssets.getAllAssets();
        final images = allAssets.where((asset) => asset.endsWith('.png') || asset.endsWith('.jpg')).toList();
        final svgs = allAssets.where((asset) => asset.endsWith('.svg')).toList();

        await Future.wait([
          _preloadImages(context, images),
          _preloadSvgIcons(svgs),
        ]);

        AppLogger.info('All assets preloaded successfully (${allAssets.length} items)');
      });
    } catch (error, stackTrace) {
      AppLogger.error('Failed to preload all assets', error: error, stackTrace: stackTrace);
    } finally {
      _isPreloading = false;
    }
  }

  /// Preload specific assets
  Future<void> preloadAssets(BuildContext context, List<String> assetPaths) async {
    if (assetPaths.isEmpty) return;

    AppLogger.debug('Preloading ${assetPaths.length} specific assets');

    try {
      final images = assetPaths.where((asset) => asset.endsWith('.png') || asset.endsWith('.jpg')).toList();
      final svgs = assetPaths.where((asset) => asset.endsWith('.svg')).toList();

      await Future.wait([
        _preloadImages(context, images),
        _preloadSvgIcons(svgs),
      ]);

      AppLogger.debug('Specific assets preloaded successfully');
    } catch (error, stackTrace) {
      AppLogger.error('Failed to preload specific assets', error: error, stackTrace: stackTrace);
    }
  }

  /// Preload images using precacheImage
  Future<void> _preloadImages(BuildContext context, List<String> imagePaths) async {
    if (imagePaths.isEmpty) return;

    final futures = imagePaths.map((imagePath) async {
      try {
        await precacheImage(AssetImage(imagePath), context);
        _preloadedAssets.add(imagePath);
        AppLogger.debug('Preloaded image: $imagePath');
      } catch (error) {
        AppLogger.warning('Failed to preload image: $imagePath', error: error);
      }
    });

    await Future.wait(futures);
  }

  /// Preload SVG icons using flutter_svg
  Future<void> _preloadSvgIcons(List<String> svgPaths) async {
    if (svgPaths.isEmpty) return;

    final futures = svgPaths.map((svgPath) async {
      try {
        final loader = SvgAssetLoader(svgPath);
        await svg.cache.putIfAbsent(loader.cacheKey(null), () => loader.loadBytes(null));
        _preloadedAssets.add(svgPath);
        AppLogger.debug('Preloaded SVG: $svgPath');
      } catch (error) {
        AppLogger.warning('Failed to preload SVG: $svgPath', error: error);
      }
    });

    await Future.wait(futures);
  }

  /// Clear preloaded assets cache
  void clearCache() {
    _preloadedAssets.clear();
    // Clear image cache
    imageCache.clear();
    imageCache.clearLiveImages();
    // Clear SVG cache
    svg.cache.clear();
    AppLogger.info('Asset cache cleared');
  }

  /// Get cache statistics
  Map<String, dynamic> getCacheStats() {
    return {
      'preloaded_assets': _preloadedAssets.length,
      'image_cache_size': imageCache.currentSize,
      'image_cache_count': imageCache.currentSizeBytes,
      'svg_cache_size': svg.cache.count,
      'is_preloading': _isPreloading,
      'preloading_progress': preloadingProgress,
    };
  }

  /// Warm up asset cache with most commonly used assets
  Future<void> warmUpCache(BuildContext context) async {
    AppLogger.info('Warming up asset cache');
    
    // Preload only the most critical assets for immediate use
    final criticalAssets = [
      AppAssets.logo,
      AppAssets.iconDashboard,
      AppAssets.iconQuest,
      AppAssets.iconAchievement,
      AppAssets.iconProfile,
      AppAssets.animationLoading,
    ];

    await preloadAssets(context, criticalAssets);
    AppLogger.info('Asset cache warmed up');
  }
}
