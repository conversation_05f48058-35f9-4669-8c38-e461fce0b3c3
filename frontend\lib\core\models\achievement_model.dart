import 'package:json_annotation/json_annotation.dart';

part 'achievement_model.g.dart';

@JsonSerializable()
class Achievement {
  final String id;
  final String name;
  final String description;
  final String? icon;
  final String? image;
  final AchievementCategory category;
  final AchievementType type;
  final AchievementRarity rarity;
  final int points;
  final int experience;
  @Json<PERSON>ey(name: 'unlock_criteria')
  final Map<String, dynamic> unlockCriteria;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_hidden')
  final bool isHidden;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_active')
  final bool isActive;
  @Json<PERSON>ey(name: 'created_at')
  final DateTime createdAt;
  @Json<PERSON>ey(name: 'updated_at')
  final DateTime updatedAt;

  const Achievement({
    required this.id,
    required this.name,
    required this.description,
    this.icon,
    this.image,
    required this.category,
    required this.type,
    required this.rarity,
    required this.points,
    required this.experience,
    required this.unlockCriteria,
    required this.isHidden,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Achievement.fromJson(Map<String, dynamic> json) => _$AchievementFromJson(json);
  Map<String, dynamic> toJson() => _$AchievementToJson(this);
}

@JsonSerializable()
class AchievementRequirement {
  final String id;
  final AchievementRequirementType type;
  final String target;
  final int requiredValue;
  final int currentValue;
  final bool isCompleted;

  const AchievementRequirement({
    required this.id,
    required this.type,
    required this.target,
    required this.requiredValue,
    required this.currentValue,
    required this.isCompleted,
  });

  factory AchievementRequirement.fromJson(Map<String, dynamic> json) => _$AchievementRequirementFromJson(json);
  Map<String, dynamic> toJson() => _$AchievementRequirementToJson(this);

  double get progressPercentage => (currentValue / requiredValue).clamp(0.0, 1.0);
}

@JsonSerializable()
class AchievementReward {
  final String id;
  final AchievementRewardType type;
  final String name;
  final String? description;
  final int quantity;
  final Map<String, dynamic>? metadata;

  const AchievementReward({
    required this.id,
    required this.type,
    required this.name,
    this.description,
    required this.quantity,
    this.metadata,
  });

  factory AchievementReward.fromJson(Map<String, dynamic> json) => _$AchievementRewardFromJson(json);
  Map<String, dynamic> toJson() => _$AchievementRewardToJson(this);
}

@JsonSerializable()
class UserAchievement {
  final String id;
  final String userId;
  final String achievementId;
  final Achievement achievement;
  final DateTime unlockedAt;
  final bool isNotified;

  const UserAchievement({
    required this.id,
    required this.userId,
    required this.achievementId,
    required this.achievement,
    required this.unlockedAt,
    required this.isNotified,
  });

  factory UserAchievement.fromJson(Map<String, dynamic> json) => _$UserAchievementFromJson(json);
  Map<String, dynamic> toJson() => _$UserAchievementToJson(this);
}

@JsonEnum()
enum AchievementCategory {
  @JsonValue('quest')
  quest,
  @JsonValue('quests')
  quests,
  @JsonValue('social')
  social,
  @JsonValue('progression')
  progression,
  @JsonValue('collection')
  collection,
  @JsonValue('skill')
  skill,
  @JsonValue('special')
  special,
  @JsonValue('seasonal')
  seasonal,
  @JsonValue('exploration')
  exploration,
}

@JsonEnum()
enum AchievementType {
  @JsonValue('milestone')
  milestone,
  @JsonValue('cumulative')
  cumulative,
  @JsonValue('streak')
  streak,
  @JsonValue('challenge')
  challenge,
  @JsonValue('discovery')
  discovery,
  @JsonValue('mastery')
  mastery,
}

@JsonEnum()
enum AchievementRarity {
  @JsonValue('common')
  common,
  @JsonValue('uncommon')
  uncommon,
  @JsonValue('rare')
  rare,
  @JsonValue('epic')
  epic,
  @JsonValue('legendary')
  legendary,
}

@JsonEnum()
enum AchievementRequirementType {
  @JsonValue('quest_completion')
  questCompletion,
  @JsonValue('points_earned')
  pointsEarned,
  @JsonValue('level_reached')
  levelReached,
  @JsonValue('days_active')
  daysActive,
  @JsonValue('social_connections')
  socialConnections,
  @JsonValue('marketplace_transactions')
  marketplaceTransactions,
  @JsonValue('items_collected')
  itemsCollected,
}

@JsonEnum()
enum AchievementRewardType {
  @JsonValue('points')
  points,
  @JsonValue('experience')
  experience,
  @JsonValue('currency')
  currency,
  @JsonValue('item')
  item,
  @JsonValue('badge')
  badge,
  @JsonValue('title')
  title,
}

extension AchievementCategoryExtension on AchievementCategory {
  String get displayName {
    switch (this) {
      case AchievementCategory.quest:
      case AchievementCategory.quests:
        return 'Quest';
      case AchievementCategory.social:
        return 'Social';
      case AchievementCategory.progression:
        return 'Progression';
      case AchievementCategory.collection:
        return 'Collection';
      case AchievementCategory.skill:
        return 'Skill';
      case AchievementCategory.special:
        return 'Special';
      case AchievementCategory.seasonal:
        return 'Seasonal';
      case AchievementCategory.exploration:
        return 'Exploration';
    }
  }
}

extension AchievementTypeExtension on AchievementType {
  String get displayName {
    switch (this) {
      case AchievementType.milestone:
        return 'Milestone';
      case AchievementType.cumulative:
        return 'Cumulative';
      case AchievementType.streak:
        return 'Streak';
      case AchievementType.challenge:
        return 'Challenge';
      case AchievementType.discovery:
        return 'Discovery';
      case AchievementType.mastery:
        return 'Mastery';
    }
  }
}

extension AchievementRarityExtension on AchievementRarity {
  String get displayName {
    switch (this) {
      case AchievementRarity.common:
        return 'Common';
      case AchievementRarity.uncommon:
        return 'Uncommon';
      case AchievementRarity.rare:
        return 'Rare';
      case AchievementRarity.epic:
        return 'Epic';
      case AchievementRarity.legendary:
        return 'Legendary';
    }
  }

  int get pointsMultiplier {
    switch (this) {
      case AchievementRarity.common:
        return 1;
      case AchievementRarity.uncommon:
        return 2;
      case AchievementRarity.rare:
        return 3;
      case AchievementRarity.epic:
        return 5;
      case AchievementRarity.legendary:
        return 10;
    }
  }
}
