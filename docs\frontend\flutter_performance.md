# Flutter Performance Optimization Guide

## Table of Contents
1. [Performance Fundamentals](#performance-fundamentals)
2. [Build Optimization](#build-optimization)
3. [Memory Management](#memory-management)
4. [Rendering Performance](#rendering-performance)
5. [List and ScrollView Optimization](#list-and-scrollview-optimization)
6. [Image and Asset Optimization](#image-and-asset-optimization)
7. [Animation Performance](#animation-performance)
8. [App Size Optimization](#app-size-optimization)
9. [Profiling and Debugging](#profiling-and-debugging)
10. [Platform-Specific Optimizations](#platform-specific-optimizations)

## Performance Fundamentals

### Flutter's Performance Model
Flutter aims for 60fps (16.67ms per frame) or 120fps (8.33ms per frame) on supported devices:
- **Build phase**: Widget tree construction
- **Layout phase**: Size and position calculation
- **Paint phase**: Drawing to screen
- **Composite phase**: Layering and effects

### Performance Metrics
```dart
// Performance overlay
MaterialApp(
  showPerformanceOverlay: true, // Shows performance metrics
  home: MyHomePage(),
)

// Debug performance issues
import 'package:flutter/scheduler.dart';

void checkPerformance() {
  SchedulerBinding.instance.addTimingsCallback((timings) {
    for (final timing in timings) {
      print('Frame duration: ${timing.totalSpan}');
      if (timing.totalSpan > Duration(milliseconds: 16)) {
        print('Janky frame detected!');
      }
    }
  });
}
```

### Common Performance Issues
1. **Unnecessary rebuilds**
2. **Expensive build methods**
3. **Large widget trees**
4. **Inefficient list handling**
5. **Poor memory management**
6. **Blocking the main thread**

## Build Optimization

### Use const Constructors
```dart
// Bad - Creates new widget every rebuild
class BadExample extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Text('Hello World'),
    );
  }
}

// Good - Uses const constructor
class GoodExample extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return const Container(
      child: Text('Hello World'),
    );
  }
}
```

### Minimize Widget Rebuilds
```dart
// Bad - Entire widget rebuilds on state change
class BadCounterWidget extends StatefulWidget {
  @override
  _BadCounterWidgetState createState() => _BadCounterWidgetState();
}

class _BadCounterWidgetState extends State<BadCounterWidget> {
  int counter = 0;
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ExpensiveWidget(), // Rebuilds unnecessarily
        Text('Counter: $counter'),
        ElevatedButton(
          onPressed: () => setState(() => counter++),
          child: Text('Increment'),
        ),
      ],
    );
  }
}

// Good - Only counter text rebuilds
class GoodCounterWidget extends StatefulWidget {
  @override
  _GoodCounterWidgetState createState() => _GoodCounterWidgetState();
}

class _GoodCounterWidgetState extends State<GoodCounterWidget> {
  int counter = 0;
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const ExpensiveWidget(), // Const - won't rebuild
        CounterDisplay(counter: counter), // Separate widget
        ElevatedButton(
          onPressed: () => setState(() => counter++),
          child: const Text('Increment'),
        ),
      ],
    );
  }
}

class CounterDisplay extends StatelessWidget {
  final int counter;
  
  const CounterDisplay({Key? key, required this.counter}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Text('Counter: $counter');
  }
}
```

### Extract Widget Classes
```dart
// Bad - Inline widgets rebuilt every time
class BadExample extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: 200,
          height: 100,
          decoration: BoxDecoration(
            color: Colors.blue,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Text(
              'Header',
              style: TextStyle(color: Colors.white, fontSize: 18),
            ),
          ),
        ),
        // More complex inline widgets...
      ],
    );
  }
}

// Good - Extracted widget can be const
class GoodExample extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return const Column(
      children: [
        HeaderWidget(),
        // Other const widgets...
      ],
    );
  }
}

class HeaderWidget extends StatelessWidget {
  const HeaderWidget({Key? key}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 200,
      height: 100,
      decoration: BoxDecoration(
        color: Colors.blue,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Text(
          'Header',
          style: TextStyle(color: Colors.white, fontSize: 18),
        ),
      ),
    );
  }
}
```

### Use RepaintBoundary
```dart
// Isolate expensive widgets to prevent unnecessary repaints
class OptimizedWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        RepaintBoundary(
          child: ExpensiveCustomPaintWidget(),
        ),
        RegularWidget(),
      ],
    );
  }
}

// Custom widget with RepaintBoundary
class ExpensiveChart extends StatelessWidget {
  final List<DataPoint> data;
  
  const ExpensiveChart({Key? key, required this.data}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: CustomPaint(
        size: Size(double.infinity, 200),
        painter: ChartPainter(data),
      ),
    );
  }
}
```

## Memory Management

### Proper Resource Disposal
```dart
class ResourceWidget extends StatefulWidget {
  @override
  _ResourceWidgetState createState() => _ResourceWidgetState();
}

class _ResourceWidgetState extends State<ResourceWidget> {
  late StreamSubscription _subscription;
  late AnimationController _controller;
  Timer? _timer;
  
  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: Duration(seconds: 1),
      vsync: this,
    );
    
    _subscription = someStream.listen((data) {
      // Handle data
    });
    
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      // Periodic task
    });
  }
  
  @override
  void dispose() {
    _controller.dispose();
    _subscription.cancel();
    _timer?.cancel();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Container();
  }
}
```

### Memory-Efficient State Management
```dart
// Use ValueNotifier for simple state
class CounterNotifier extends ValueNotifier<int> {
  CounterNotifier() : super(0);
  
  void increment() {
    value = value + 1;
  }
}

class EfficientCounterWidget extends StatefulWidget {
  @override
  _EfficientCounterWidgetState createState() => _EfficientCounterWidgetState();
}

class _EfficientCounterWidgetState extends State<EfficientCounterWidget> {
  late CounterNotifier _counter;
  
  @override
  void initState() {
    super.initState();
    _counter = CounterNotifier();
  }
  
  @override
  void dispose() {
    _counter.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<int>(
      valueListenable: _counter,
      builder: (context, value, child) {
        return Column(
          children: [
            Text('Counter: $value'),
            child!, // Cached widget
          ],
        );
      },
      child: const ElevatedButton(
        onPressed: null,
        child: Text('Increment'),
      ),
    );
  }
}
```

### Weak References and Caching
```dart
class ImageCache {
  static final Map<String, WeakReference<ui.Image>> _cache = {};
  
  static Future<ui.Image?> getImage(String url) async {
    final weakRef = _cache[url];
    final cachedImage = weakRef?.target;
    
    if (cachedImage != null) {
      return cachedImage;
    }
    
    final image = await loadImageFromNetwork(url);
    _cache[url] = WeakReference(image);
    return image;
  }
  
  static void clearCache() {
    _cache.clear();
  }
}
```

## Rendering Performance

### Optimize CustomPainter
```dart
class OptimizedPainter extends CustomPainter {
  final List<Point> points;
  Paint? _paint;
  
  OptimizedPainter({required this.points});
  
  @override
  void paint(Canvas canvas, Size size) {
    // Reuse paint object
    _paint ??= Paint()
      ..color = Colors.blue
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;
    
    // Use efficient drawing methods
    final path = Path();
    if (points.isNotEmpty) {
      path.moveTo(points.first.x, points.first.y);
      for (int i = 1; i < points.length; i++) {
        path.lineTo(points[i].x, points[i].y);
      }
    }
    
    canvas.drawPath(path, _paint!);
  }
  
  @override
  bool shouldRepaint(OptimizedPainter oldDelegate) {
    // Only repaint if data actually changed
    return points != oldDelegate.points;
  }
}

// Use with RepaintBoundary
Widget buildChart() {
  return RepaintBoundary(
    child: CustomPaint(
      painter: OptimizedPainter(points: chartData),
      size: Size(double.infinity, 200),
    ),
  );
}
```

### Layer Optimization
```dart
// Bad - Multiple overlapping containers
Widget badLayering() {
  return Stack(
    children: [
      Container(color: Colors.red),
      Container(color: Colors.blue.withOpacity(0.5)),
      Container(color: Colors.green.withOpacity(0.3)),
    ],
  );
}

// Good - Single container with decoration
Widget goodLayering() {
  return Container(
    decoration: BoxDecoration(
      gradient: LinearGradient(
        colors: [
          Colors.red,
          Colors.blue.withOpacity(0.5),
          Colors.green.withOpacity(0.3),
        ],
      ),
    ),
  );
}
```

## List and ScrollView Optimization

### ListView.builder for Large Lists
```dart
// Bad - Creates all widgets at once
Widget badList(List<Item> items) {
  return ListView(
    children: items.map((item) => ItemWidget(item: item)).toList(),
  );
}

// Good - Lazy loading with builder
Widget goodList(List<Item> items) {
  return ListView.builder(
    itemCount: items.length,
    itemBuilder: (context, index) {
      return ItemWidget(item: items[index]);
    },
  );
}

// Better - with caching and optimization
class OptimizedListView extends StatelessWidget {
  final List<Item> items;
  
  const OptimizedListView({Key? key, required this.items}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: items.length,
      cacheExtent: 1000, // Cache items outside viewport
      itemBuilder: (context, index) {
        return ItemWidget(
          key: ValueKey(items[index].id), // Stable keys
          item: items[index],
        );
      },
    );
  }
}
```

### Advanced List Optimizations
```dart
// Slivers for complex scrolling
class OptimizedScrollView extends StatelessWidget {
  final List<Item> items;
  
  const OptimizedScrollView({Key? key, required this.items}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      cacheExtent: 1000,
      slivers: [
        SliverAppBar(
          pinned: true,
          expandedHeight: 200,
          flexibleSpace: FlexibleSpaceBar(
            title: const Text('Optimized List'),
          ),
        ),
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              return ItemWidget(
                key: ValueKey(items[index].id),
                item: items[index],
              );
            },
            childCount: items.length,
          ),
        ),
      ],
    );
  }
}

// AutomaticKeepAlive for expensive list items
class ExpensiveListItem extends StatefulWidget {
  final Item item;
  
  const ExpensiveListItem({Key? key, required this.item}) : super(key: key);
  
  @override
  _ExpensiveListItemState createState() => _ExpensiveListItemState();
}

class _ExpensiveListItemState extends State<ExpensiveListItem>
    with AutomaticKeepAliveClientMixin {
  
  @override
  bool get wantKeepAlive => true;
  
  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAlive
    
    return Card(
      child: ExpensiveWidget(data: widget.item),
    );
  }
}
```

## Image and Asset Optimization

### Efficient Image Loading
```dart
// Optimize image loading
Widget optimizedImage(String imageUrl) {
  return Image.network(
    imageUrl,
    // Reduce memory usage
    cacheWidth: 300,
    cacheHeight: 200,
    // Handle loading states
    loadingBuilder: (context, child, loadingProgress) {
      if (loadingProgress == null) return child;
      return Container(
        width: 300,
        height: 200,
        alignment: Alignment.center,
        child: CircularProgressIndicator(
          value: loadingProgress.expectedTotalBytes != null
              ? loadingProgress.cumulativeBytesLoaded /
                  loadingProgress.expectedTotalBytes!
              : null,
        ),
      );
    },
    // Handle errors
    errorBuilder: (context, error, stackTrace) {
      return Container(
        width: 300,
        height: 200,
        color: Colors.grey[300],
        child: const Icon(Icons.error),
      );
    },
  );
}

// Use CachedNetworkImage for better caching
Widget cachedImage(String imageUrl) {
  return CachedNetworkImage(
    imageUrl: imageUrl,
    width: 300,
    height: 200,
    fit: BoxFit.cover,
    placeholder: (context, url) => const CircularProgressIndicator(),
    errorWidget: (context, url, error) => const Icon(Icons.error),
    memCacheWidth: 300,
    memCacheHeight: 200,
  );
}
```

### Asset Optimization
```dart
// Use vector images when possible
Widget vectorIcon() {
  return SvgPicture.asset(
    'assets/icons/star.svg',
    width: 24,
    height: 24,
    color: Colors.yellow,
  );
}

// Preload critical images
class ImagePreloader {
  static Future<void> preloadImages(BuildContext context) async {
    await Future.wait([
      precacheImage(const AssetImage('assets/images/logo.png'), context),
      precacheImage(const AssetImage('assets/images/background.jpg'), context),
    ]);
  }
}

// Use appropriate image formats
// - PNG for transparency
// - JPEG for photos
// - WebP for better compression
// - SVG for scalable graphics
```

## Animation Performance

### Efficient Animations
```dart
// Use AnimationController for complex animations
class OptimizedAnimation extends StatefulWidget {
  @override
  _OptimizedAnimationState createState() => _OptimizedAnimationState();
}

class _OptimizedAnimationState extends State<OptimizedAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.scale(
          scale: _animation.value,
          child: child,
        );
      },
      child: const FlutterLogo(size: 100), // Child won't rebuild
    );
  }
}

// Use built-in animated widgets when possible
Widget simpleAnimation() {
  return TweenAnimationBuilder<double>(
    tween: Tween(begin: 0, end: 1),
    duration: const Duration(seconds: 1),
    builder: (context, value, child) {
      return Opacity(
        opacity: value,
        child: child,
      );
    },
    child: const FlutterLogo(size: 100),
  );
}
```

### Animation Best Practices
```dart
// Prefer transforms over changing widget properties
// Good - GPU accelerated
Widget goodAnimation(bool isVisible) {
  return AnimatedOpacity(
    opacity: isVisible ? 1.0 : 0.0,
    duration: const Duration(milliseconds: 300),
    child: Transform.translate(
      offset: isVisible ? Offset.zero : const Offset(0, 50),
      child: const MyWidget(),
    ),
  );
}

// Bad - CPU intensive
Widget badAnimation(bool isVisible) {
  return AnimatedContainer(
    duration: const Duration(milliseconds: 300),
    width: isVisible ? 200 : 0,
    height: isVisible ? 100 : 0,
    child: const MyWidget(),
  );
}
```

## App Size Optimization

### Reduce Bundle Size
```yaml
# pubspec.yaml optimizations
flutter:
  uses-material-design: true
  
  # Only include used assets
  assets:
    - assets/images/logo.png
    - assets/icons/
  
  # Use system fonts when possible
  fonts:
    - family: CustomFont
      fonts:
        - asset: assets/fonts/CustomFont-Regular.ttf
          weight: 400
```

### Code Splitting and Deferred Loading
```dart
// Deferred library loading
import 'expensive_feature.dart' deferred as expensive;

Future<void> loadExpensiveFeature() async {
  await expensive.loadLibrary();
  // Use expensive feature
}

// Conditional imports
import 'web_implementation.dart' if (dart.library.io) 'mobile_implementation.dart';
```

### Tree Shaking Optimization
```dart
// Use specific imports instead of barrel imports
// Good
import 'package:flutter/material.dart' show Widget, StatelessWidget, BuildContext;

// Bad
import 'package:flutter/material.dart';

// Dead code elimination
class UnusedClass {
  // This will be removed in release builds if not used
  void unusedMethod() {}
}
```

## Profiling and Debugging

### Flutter Inspector
```dart
// Enable performance monitoring in debug mode
void main() {
  if (kDebugMode) {
    // Show performance overlay
    WidgetsApp.debugShowWidgetInspectorOverride = true;
  }
  runApp(MyApp());
}
```

### Performance Profiling Tools
```bash
# Flutter performance profiling commands
flutter run --profile
flutter run --release

# Memory profiling
flutter run --enable-software-rendering
flutter run --trace-startup

# Size analysis
flutter build apk --analyze-size
flutter build ios --analyze-size
```

### Custom Performance Monitoring
```dart
class PerformanceMonitor {
  static final Stopwatch _stopwatch = Stopwatch();
  
  static void startTiming(String operation) {
    _stopwatch.reset();
    _stopwatch.start();
    print('Starting: $operation');
  }
  
  static void endTiming(String operation) {
    _stopwatch.stop();
    final elapsed = _stopwatch.elapsedMilliseconds;
    print('Completed: $operation in ${elapsed}ms');
    
    if (elapsed > 100) {
      print('WARNING: Slow operation detected!');
    }
  }
}

// Usage
void expensiveOperation() {
  PerformanceMonitor.startTiming('Data processing');
  
  // Expensive work here
  
  PerformanceMonitor.endTiming('Data processing');
}
```

### Memory Leak Detection
```dart
class MemoryLeakDetector {
  static final Map<String, int> _widgetCounts = {};
  
  static void trackWidget(String widgetName) {
    _widgetCounts[widgetName] = (_widgetCounts[widgetName] ?? 0) + 1;
  }
  
  static void untrackWidget(String widgetName) {
    final count = _widgetCounts[widgetName] ?? 0;
    if (count > 0) {
      _widgetCounts[widgetName] = count - 1;
    }
  }
  
  static void printReport() {
    print('Widget instances:');
    _widgetCounts.forEach((widget, count) {
      if (count > 0) {
        print('$widget: $count instances');
      }
    });
  }
}
```

## Platform-Specific Optimizations

### Android Optimizations
```dart
// Platform-specific optimizations
class AndroidOptimizations {
  static void configureAndroid() {
    if (Platform.isAndroid) {
      // Enable hardware acceleration
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      
      // Optimize for different screen densities
      WidgetsFlutterBinding.ensureInitialized();
    }
  }
  
  // Use Android-specific widgets when beneficial
  static Widget platformButton({
    required VoidCallback onPressed,
    required String text,
  }) {
    if (Platform.isAndroid) {
      return ElevatedButton(
        onPressed: onPressed,
        child: Text(text),
      );
    } else {
      return CupertinoButton(
        onPressed: onPressed,
        child: Text(text),
      );
    }
  }
}
```

### iOS Optimizations
```dart
class IOSOptimizations {
  static void configureIOS() {
    if (Platform.isIOS) {
      // Configure for iOS-specific behaviors
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarBrightness: Brightness.light,
        ),
      );
    }
  }
  
  // Use iOS-specific optimizations
  static Widget iosOptimizedList<T>({
    required List<T> items,
    required Widget Function(T item) itemBuilder,
  }) {
    return CupertinoScrollbar(
      child: ListView.builder(
        physics: const BouncingScrollPhysics(), // iOS-style scrolling
        itemCount: items.length,
        itemBuilder: (context, index) => itemBuilder(items[index]),
      ),
    );
  }
}
```

## Best Practices Summary

### Build Performance
1. **Use const constructors** wherever possible
2. **Extract widgets** instead of building inline
3. **Minimize widget tree depth**
4. **Use RepaintBoundary** for expensive widgets
5. **Implement proper shouldRepaint/shouldRebuild** logic

### Memory Performance
1. **Dispose resources** properly
2. **Use weak references** for caches
3. **Implement lazy loading**
4. **Monitor memory usage** in development
5. **Use efficient data structures**

### Rendering Performance
1. **Optimize CustomPainter** implementations
2. **Use appropriate layer composition**
3. **Minimize overdraw**
4. **Batch expensive operations**
5. **Use GPU-accelerated transforms**

### List Performance
1. **Use ListView.builder** for large lists
2. **Implement stable keys**
3. **Use AutomaticKeepAlive** judiciously
4. **Configure appropriate cache extents**
5. **Consider pagination** for very large datasets

### General Guidelines
1. **Profile early and often**
2. **Test on real devices**
3. **Measure before optimizing**
4. **Consider the user experience**
5. **Balance performance with maintainability**
