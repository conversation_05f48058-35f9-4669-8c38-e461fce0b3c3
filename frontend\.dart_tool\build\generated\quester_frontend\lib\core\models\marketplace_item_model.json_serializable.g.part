// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MarketplaceItem _$MarketplaceItemFromJson(Map<String, dynamic> json) =>
    MarketplaceItem(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      imageUrl: json['imageUrl'] as String?,
      videoUrl: json['videoUrl'] as String?,
      category: $enumDecode(_$MarketplaceItemCategoryEnumMap, json['category']),
      rarity: $enumDecode(_$MarketplaceItemRarityEnumMap, json['rarity']),
      price: (json['price'] as num).toInt(),
      currency: json['currency'] as String,
      sellerId: json['sellerId'] as String,
      sellerName: json['sellerName'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      status: $enumDecode(_$MarketplaceItemStatusEnumMap, json['status']),
      quantity: (json['quantity'] as num).toInt(),
      maxQuantity: (json['maxQuantity'] as num).toInt(),
      tags: (json['tags'] as List<dynamic>).map((e) => e as String).toList(),
      metadata: json['metadata'] as Map<String, dynamic>?,
      isLimited: json['isLimited'] as bool,
      limitedUntil: json['limitedUntil'] == null
          ? null
          : DateTime.parse(json['limitedUntil'] as String),
      discountPercentage: (json['discountPercentage'] as num?)?.toDouble(),
      originalPrice: (json['originalPrice'] as num?)?.toInt(),
    );

Map<String, dynamic> _$MarketplaceItemToJson(MarketplaceItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'imageUrl': instance.imageUrl,
      'videoUrl': instance.videoUrl,
      'category': _$MarketplaceItemCategoryEnumMap[instance.category]!,
      'rarity': _$MarketplaceItemRarityEnumMap[instance.rarity]!,
      'price': instance.price,
      'currency': instance.currency,
      'sellerId': instance.sellerId,
      'sellerName': instance.sellerName,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'status': _$MarketplaceItemStatusEnumMap[instance.status]!,
      'quantity': instance.quantity,
      'maxQuantity': instance.maxQuantity,
      'tags': instance.tags,
      'metadata': instance.metadata,
      'isLimited': instance.isLimited,
      'limitedUntil': instance.limitedUntil?.toIso8601String(),
      'discountPercentage': instance.discountPercentage,
      'originalPrice': instance.originalPrice,
    };

const _$MarketplaceItemCategoryEnumMap = {
  MarketplaceItemCategory.avatar: 'avatar',
  MarketplaceItemCategory.badge: 'badge',
  MarketplaceItemCategory.consumable: 'consumable',
  MarketplaceItemCategory.decoration: 'decoration',
  MarketplaceItemCategory.boost: 'boost',
  MarketplaceItemCategory.theme: 'theme',
  MarketplaceItemCategory.special: 'special',
};

const _$MarketplaceItemRarityEnumMap = {
  MarketplaceItemRarity.common: 'common',
  MarketplaceItemRarity.uncommon: 'uncommon',
  MarketplaceItemRarity.rare: 'rare',
  MarketplaceItemRarity.epic: 'epic',
  MarketplaceItemRarity.legendary: 'legendary',
};

const _$MarketplaceItemStatusEnumMap = {
  MarketplaceItemStatus.available: 'available',
  MarketplaceItemStatus.soldOut: 'sold_out',
  MarketplaceItemStatus.comingSoon: 'coming_soon',
  MarketplaceItemStatus.discontinued: 'discontinued',
};

MarketplacePurchase _$MarketplacePurchaseFromJson(Map<String, dynamic> json) =>
    MarketplacePurchase(
      id: json['id'] as String,
      userId: json['userId'] as String,
      itemId: json['itemId'] as String,
      item: MarketplaceItem.fromJson(json['item'] as Map<String, dynamic>),
      quantity: (json['quantity'] as num).toInt(),
      totalPrice: (json['totalPrice'] as num).toInt(),
      currency: json['currency'] as String,
      purchasedAt: DateTime.parse(json['purchasedAt'] as String),
      status: $enumDecode(_$MarketplacePurchaseStatusEnumMap, json['status']),
      transactionId: json['transactionId'] as String?,
    );

Map<String, dynamic> _$MarketplacePurchaseToJson(
        MarketplacePurchase instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'itemId': instance.itemId,
      'item': instance.item,
      'quantity': instance.quantity,
      'totalPrice': instance.totalPrice,
      'currency': instance.currency,
      'purchasedAt': instance.purchasedAt.toIso8601String(),
      'status': _$MarketplacePurchaseStatusEnumMap[instance.status]!,
      'transactionId': instance.transactionId,
    };

const _$MarketplacePurchaseStatusEnumMap = {
  MarketplacePurchaseStatus.pending: 'pending',
  MarketplacePurchaseStatus.completed: 'completed',
  MarketplacePurchaseStatus.failed: 'failed',
  MarketplacePurchaseStatus.refunded: 'refunded',
};
