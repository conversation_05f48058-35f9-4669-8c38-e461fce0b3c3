import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hive_flutter/hive_flutter.dart';

import '../services/storage_service.dart';

/// Application configuration and initialization
class AppConfig {
  static const String appName = 'Quester';
  static const String appVersion = '1.0.0';
  static const String buildNumber = '1';
  
  // API Configuration
  static const String baseUrl = kDebugMode 
      ? 'http://localhost:8080/api'
      : 'https://api.quester.app';
  
  static const String wsUrl = kDebugMode
      ? 'ws://localhost:8080/ws'
      : 'wss://api.quester.app/ws';
  
  // Feature Flags
  static const bool enableWebSocket = true;
  static const bool enableNotifications = true;
  static const bool enableAnalytics = !kDebugMode;
  static const bool enableCrashReporting = !kDebugMode;
  
  // Performance Configuration
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB
  static const Duration apiTimeout = Duration(seconds: 30);
  static const Duration wsReconnectDelay = Duration(seconds: 5);
  static const int maxRetryAttempts = 3;
  
  /// Initialize the application
  static Future<void> initialize() async {
    // Initialize Hive for local storage
    await Hive.initFlutter();
    await StorageService.init();
    
    // Set system UI overlay style
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
    
    // Set preferred orientations
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }
}
