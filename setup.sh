#!/bin/bash

# Quester Project Docker Setup Script
# This script helps with Docker setup and management for the Quester project

# Colors for better readability
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Enable Docker Buildkit for better build performance
export DOCKER_BUILDKIT=1
export COMPOSE_DOCKER_CLI_BUILD=1

# Global OS variable
OS=""

# Detect OS
detect_os() {
  case "$(uname -s)" in
    Linux*)     OS="Linux";;
    Darwin*)    OS="Mac";;
    CYGWIN*)    OS="Windows";;
    MINGW64*)   OS="Windows";;
    MINGW*)     OS="Windows";;
    MSYS*)      OS="Windows";;
    *)          OS="UNKNOWN";;
  esac
  echo -e "${GREEN}Detected OS: $OS${NC}"
}

# Function to set the correct Docker Compose command
set_docker_compose_cmd() {
  if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE="docker-compose"
  elif command -v docker &> /dev/null && docker compose version &> /dev/null; then
    DOCKER_COMPOSE="docker compose"
  else
    echo -e "${RED}Neither docker-compose nor docker compose is available${NC}"
    exit 1
  fi
  echo -e "${GREEN}Using Docker Compose command: $DOCKER_COMPOSE${NC}"
}

# Function to check Docker installation
check_docker() {
  if ! command -v docker &> /dev/null; then
    echo -e "${RED}Docker is not installed. Please install Docker first.${NC}"
    exit 1
  fi

  set_docker_compose_cmd
}

# Function to display help information
show_help() {
  echo -e "${YELLOW}Quester Docker Setup Script${NC}"
  echo "Usage: ./setup.sh [command]"
  echo ""
  echo "Commands:"
  echo "  start       - Start all containers"
  echo "  stop        - Stop all containers"
  echo "  restart     - Restart all containers"
  echo "  build       - Build all containers"
  echo "  logs        - View logs from all containers"
  echo "  frontend    - Access frontend container shell"
  echo "  backend     - Access backend container shell"
  echo "  db          - Access database container shell"
  echo "  redis       - Access redis container shell"
  echo "  status      - Check status of all containers"
  echo "  clean       - Remove all containers and volumes (caution!)"
  echo "  help        - Show this help message"
}

# Function to start containers
start_containers() {
  echo -e "${GREEN}Starting Quester containers...${NC}"
  $DOCKER_COMPOSE -f docker-compose.yml up -d
  echo -e "${GREEN}Containers started successfully!${NC}"
}

# Function to stop containers
stop_containers() {
  echo -e "${YELLOW}Stopping Quester containers...${NC}"
  $DOCKER_COMPOSE -f docker-compose.yml down
  echo -e "${GREEN}Containers stopped successfully!${NC}"
}

# Function to restart containers
restart_containers() {
  echo -e "${YELLOW}Restarting Quester containers...${NC}"
  $DOCKER_COMPOSE -f docker-compose.yml restart
  echo -e "${GREEN}Containers restarted successfully!${NC}"
}

# Function to build containers
build_containers() {
  echo -e "${GREEN}Building Quester containers...${NC}"
  $DOCKER_COMPOSE -f docker-compose.yml build
  echo -e "${GREEN}Containers built successfully!${NC}"
}

# Function to view logs
view_logs() {
  echo -e "${GREEN}Viewing logs from all containers...${NC}"
  $DOCKER_COMPOSE -f docker-compose.yml logs -f
}

# Function to access container shell
access_shell() {
  local container=$1
  local shell_cmd="sh"
  
  # Use bash for backend container if available
  if [ "$container" = "backend" ]; then
    shell_cmd="bash"
  fi
  
  echo -e "${GREEN}Accessing $container shell...${NC}"
  $DOCKER_COMPOSE -f docker-compose.yml exec $container $shell_cmd
}

# Function to check container status
check_status() {
  echo -e "${GREEN}Checking container status...${NC}"
  $DOCKER_COMPOSE -f docker-compose.yml ps
}

# Function to clean up containers and volumes
clean_containers() {
  echo -e "${RED}WARNING: This will remove all containers and volumes managed by Docker Compose!${NC}"
  read -p "Are you sure you want to continue? (y/n) " -n 1 -r
  echo
  if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}Removing all containers and volumes managed by Docker Compose...${NC}"
    # Remove containers and volumes managed by Docker Compose
    $DOCKER_COMPOSE -f docker-compose.yml down -v
    # Remove related containers, networks, images, and volumes that are not managed by Docker Compose
    $DOCKER_COMPOSE -f docker-compose.yml rm -f
    # Remove related images
    docker image prune -a -f
    docker network prune -f
    docker volume prune -f
    echo -e "${GREEN}Cleanup completed successfully!${NC}"
  else
    echo -e "${YELLOW}Cleanup cancelled.${NC}"
  fi
}

# Main script execution
detect_os
check_docker

case "$1" in
  start)
    start_containers
    ;;
  stop)
    stop_containers
    ;;
  restart)
    restart_containers
    ;;
  build)
    build_containers
    ;;
  logs)
    view_logs
    ;;
  frontend)
    access_shell "frontend"
    ;;
  backend)
    access_shell "backend"
    ;;
  db)
    access_shell "postgres"
    ;;
  redis)
    access_shell "redis"
    ;;
  status)
    check_status
    ;;
  clean)
    clean_containers
    ;;
  help|*)
    show_help
    ;;
esac