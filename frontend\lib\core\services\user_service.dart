import 'package:dio/dio.dart';
import '../models/user_model.dart';
import 'base_api_service.dart';

class UserService extends BaseApiService {
  UserService(super.dio);

  // Get current user profile
  Future<User> getCurrentUser() async {
    try {
      final response = await dio.get('/users/profile');
      return User.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Update user profile
  Future<User> updateProfile({
    String? firstName,
    String? lastName,
    String? username,
    String? bio,
    String? avatar,
  }) async {
    try {
      final data = <String, dynamic>{};

      if (firstName != null) data['first_name'] = firstName;
      if (lastName != null) data['last_name'] = lastName;
      if (username != null) data['username'] = username;
      if (bio != null) data['bio'] = bio;
      if (avatar != null) data['avatar'] = avatar;

      final response = await dio.put('/users/profile', data: data);
      return User.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get user statistics
  Future<Map<String, dynamic>> getUserStatistics() async {
    try {
      final response = await dio.get('/users/statistics');
      return response.data['data'] ?? response.data;
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get user activity
  Future<List<Map<String, dynamic>>> getUserActivity({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await dio.get('/users/activity', queryParameters: {
        'page': page,
        'limit': limit,
      });
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.cast<Map<String, dynamic>>();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get user leaderboard position
  Future<Map<String, dynamic>> getLeaderboardPosition() async {
    try {
      final response = await dio.get('/users/leaderboard/position');
      return response.data['data'] ?? response.data;
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get leaderboard
  Future<List<Map<String, dynamic>>> getLeaderboard({
    String type = 'points', // points, level, quests_completed
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await dio.get('/users/leaderboard', queryParameters: {
        'type': type,
        'page': page,
        'limit': limit,
      });
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.cast<Map<String, dynamic>>();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get user friends
  Future<List<User>> getFriends({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await dio.get('/users/friends', queryParameters: {
        'page': page,
        'limit': limit,
      });
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => User.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Add friend
  Future<void> addFriend(String userId) async {
    try {
      await dio.post('/users/friends/$userId');
    } catch (e) {
      throw handleError(e);
    }
  }

  // Remove friend
  Future<void> removeFriend(String userId) async {
    try {
      await dio.delete('/users/friends/$userId');
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get friend requests
  Future<List<Map<String, dynamic>>> getFriendRequests({
    bool? incoming = true,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (incoming != null) queryParams['incoming'] = incoming;

      final response = await dio.get('/users/friend-requests', queryParameters: queryParams);
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.cast<Map<String, dynamic>>();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Send friend request
  Future<void> sendFriendRequest(String userId) async {
    try {
      await dio.post('/users/friend-requests', data: {'user_id': userId});
    } catch (e) {
      throw handleError(e);
    }
  }

  // Accept friend request
  Future<void> acceptFriendRequest(String requestId) async {
    try {
      await dio.put('/users/friend-requests/$requestId/accept');
    } catch (e) {
      throw handleError(e);
    }
  }

  // Reject friend request
  Future<void> rejectFriendRequest(String requestId) async {
    try {
      await dio.put('/users/friend-requests/$requestId/reject');
    } catch (e) {
      throw handleError(e);
    }
  }

  // Search users
  Future<List<User>> searchUsers(String query, {
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await dio.get('/users/search', queryParameters: {
        'q': query,
        'page': page,
        'limit': limit,
      });
      final List<dynamic> data = response.data['data'] ?? response.data;
      return data.map((json) => User.fromJson(json)).toList();
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get user by ID
  Future<User> getUserById(String userId) async {
    try {
      final response = await dio.get('/users/$userId');
      return User.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get user's public profile
  Future<Map<String, dynamic>> getUserPublicProfile(String userId) async {
    try {
      final response = await dio.get('/users/$userId/profile');
      return response.data['data'] ?? response.data;
    } catch (e) {
      throw handleError(e);
    }
  }

  // Update user preferences
  Future<Map<String, dynamic>> updatePreferences({
    String? language,
    String? timezone,
    String? theme,
    bool? emailNotifications,
    bool? pushNotifications,
    Map<String, dynamic>? customSettings,
  }) async {
    try {
      final data = <String, dynamic>{};

      if (language != null) data['language'] = language;
      if (timezone != null) data['timezone'] = timezone;
      if (theme != null) data['theme'] = theme;
      if (emailNotifications != null) data['email_notifications'] = emailNotifications;
      if (pushNotifications != null) data['push_notifications'] = pushNotifications;
      if (customSettings != null) data['custom_settings'] = customSettings;

      final response = await dio.put('/users/preferences', data: data);
      return response.data['data'] ?? response.data;
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get user preferences
  Future<Map<String, dynamic>> getUserPreferences() async {
    try {
      final response = await dio.get('/users/preferences');
      return response.data['data'] ?? response.data;
    } catch (e) {
      throw handleError(e);
    }
  }

  // Upload avatar
  Future<User> uploadAvatar(String filePath) async {
    try {
      final formData = FormData.fromMap({
        'avatar': await MultipartFile.fromFile(filePath),
      });

      final response = await dio.post('/users/avatar', data: formData);
      return User.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Delete avatar
  Future<User> deleteAvatar() async {
    try {
      final response = await dio.delete('/users/avatar');
      return User.fromJson(response.data['data'] ?? response.data);
    } catch (e) {
      throw handleError(e);
    }
  }

  // Change password
  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      await dio.put('/users/password', data: {
        'current_password': currentPassword,
        'new_password': newPassword,
      });
    } catch (e) {
      throw handleError(e);
    }
  }

  // Deactivate account
  Future<void> deactivateAccount() async {
    try {
      await dio.put('/users/deactivate');
    } catch (e) {
      throw handleError(e);
    }
  }

  // Delete account
  Future<void> deleteAccount() async {
    try {
      await dio.delete('/users/account');
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get user dashboard data
  Future<Map<String, dynamic>> getDashboardData() async {
    try {
      final response = await dio.get('/users/dashboard');
      return response.data['data'] ?? response.data;
    } catch (e) {
      throw handleError(e);
    }
  }

  // Get user progress summary
  Future<Map<String, dynamic>> getProgressSummary() async {
    try {
      final response = await dio.get('/users/progress');
      return response.data['data'] ?? response.data;
    } catch (e) {
      throw handleError(e);
    }
  }
}
