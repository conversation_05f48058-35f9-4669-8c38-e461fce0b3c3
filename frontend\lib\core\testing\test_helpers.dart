import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../theme/app_theme.dart';

/// Test helpers and utilities for widget testing
class TestHelpers {
  /// Create a test widget with proper providers and theme
  static Widget createTestWidget({
    required Widget child,
    List<Override>? overrides,
    ThemeMode themeMode = ThemeMode.light,
    Locale locale = const Locale('en', 'US'),
  }) {
    return ProviderScope(
      overrides: overrides ?? [],
      child: MaterialApp(
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: themeMode,
        locale: locale,
        home: child,
      ),
    );
  }

  /// Create a test widget with navigation
  static Widget createTestWidgetWithNavigation({
    required Widget child,
    List<Override>? overrides,
    ThemeMode themeMode = ThemeMode.light,
    String initialRoute = '/',
    Map<String, WidgetBuilder>? routes,
  }) {
    return ProviderScope(
      overrides: overrides ?? [],
      child: MaterialApp(
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: themeMode,
        initialRoute: initialRoute,
        routes: routes ?? {'/': (context) => child},
      ),
    );
  }

  /// Pump and settle with custom duration
  static Future<void> pumpAndSettleWithTimeout(
    WidgetTester tester, {
    Duration timeout = const Duration(seconds: 10),
    Duration interval = const Duration(milliseconds: 100),
  }) async {
    await tester.pumpAndSettle(timeout, interval);
  }

  /// Find widget by type and optional key
  static Finder findWidgetByType<T extends Widget>({Key? key}) {
    if (key != null) {
      return find.byKey(key);
    }
    return find.byType(T);
  }

  /// Find text widget with specific text
  static Finder findTextWidget(String text) {
    return find.text(text);
  }

  /// Find widget by icon
  static Finder findIconWidget(IconData icon) {
    return find.byIcon(icon);
  }

  /// Tap widget and pump
  static Future<void> tapAndPump(WidgetTester tester, Finder finder) async {
    await tester.tap(finder);
    await tester.pump();
  }

  /// Enter text and pump
  static Future<void> enterTextAndPump(
    WidgetTester tester,
    Finder finder,
    String text,
  ) async {
    await tester.enterText(finder, text);
    await tester.pump();
  }

  /// Scroll until visible
  static Future<void> scrollUntilVisible(
    WidgetTester tester,
    Finder finder,
    Finder scrollable, {
    double delta = 100.0,
    AxisDirection scrollDirection = AxisDirection.down,
  }) async {
    await tester.scrollUntilVisible(
      finder,
      delta,
      scrollable: scrollable,
      scrollDirection: scrollDirection,
    );
  }

  /// Verify widget exists
  static void verifyWidgetExists(Finder finder) {
    expect(finder, findsOneWidget);
  }

  /// Verify widget doesn't exist
  static void verifyWidgetNotExists(Finder finder) {
    expect(finder, findsNothing);
  }

  /// Verify multiple widgets exist
  static void verifyMultipleWidgetsExist(Finder finder, int count) {
    expect(finder, findsNWidgets(count));
  }

  /// Verify text exists
  static void verifyTextExists(String text) {
    expect(find.text(text), findsOneWidget);
  }

  /// Verify icon exists
  static void verifyIconExists(IconData icon) {
    expect(find.byIcon(icon), findsOneWidget);
  }

  /// Wait for condition with timeout
  static Future<void> waitForCondition(
    WidgetTester tester,
    bool Function() condition, {
    Duration timeout = const Duration(seconds: 5),
    Duration interval = const Duration(milliseconds: 100),
  }) async {
    final stopwatch = Stopwatch()..start();
    
    while (!condition() && stopwatch.elapsed < timeout) {
      await tester.pump(interval);
    }
    
    if (!condition()) {
      throw TimeoutException('Condition not met within timeout', timeout);
    }
  }

  /// Mock network image
  static void mockNetworkImages() {
    // This would typically be implemented with a mock HTTP client
    // For now, it's a placeholder for future implementation
  }

  /// Create mock user data
  static Map<String, dynamic> createMockUser({
    String id = 'test-user-id',
    String email = '<EMAIL>',
    String name = 'Test User',
    int level = 1,
    int experience = 0,
    int coins = 100,
  }) {
    return {
      'id': id,
      'email': email,
      'name': name,
      'level': level,
      'experience': experience,
      'coins': coins,
      'createdAt': DateTime.now().toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
    };
  }

  /// Create mock quest data
  static Map<String, dynamic> createMockQuest({
    String id = 'test-quest-id',
    String title = 'Test Quest',
    String description = 'Test quest description',
    int reward = 50,
    String status = 'available',
    String difficulty = 'easy',
  }) {
    return {
      'id': id,
      'title': title,
      'description': description,
      'reward': reward,
      'status': status,
      'difficulty': difficulty,
      'createdAt': DateTime.now().toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
    };
  }

  /// Create mock achievement data
  static Map<String, dynamic> createMockAchievement({
    String id = 'test-achievement-id',
    String title = 'Test Achievement',
    String description = 'Test achievement description',
    String category = 'beginner',
    bool unlocked = false,
  }) {
    return {
      'id': id,
      'title': title,
      'description': description,
      'category': category,
      'unlocked': unlocked,
      'unlockedAt': unlocked ? DateTime.now().toIso8601String() : null,
      'createdAt': DateTime.now().toIso8601String(),
    };
  }

  /// Create mock notification data
  static Map<String, dynamic> createMockNotification({
    String id = 'test-notification-id',
    String title = 'Test Notification',
    String message = 'Test notification message',
    String type = 'info',
    bool read = false,
  }) {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type,
      'read': read,
      'createdAt': DateTime.now().toIso8601String(),
    };
  }

  /// Simulate network delay
  static Future<void> simulateNetworkDelay({
    Duration delay = const Duration(milliseconds: 500),
  }) async {
    await Future.delayed(delay);
  }

  /// Create responsive test sizes
  static List<Size> getTestSizes() {
    return [
      const Size(360, 640), // Mobile portrait
      const Size(640, 360), // Mobile landscape
      const Size(768, 1024), // Tablet portrait
      const Size(1024, 768), // Tablet landscape
      const Size(1920, 1080), // Desktop
    ];
  }

  /// Test widget at different screen sizes
  static Future<void> testResponsiveWidget(
    WidgetTester tester,
    Widget widget,
    void Function(Size size) testFunction,
  ) async {
    for (final size in getTestSizes()) {
      await tester.binding.setSurfaceSize(size);
      await tester.pumpWidget(widget);
      testFunction(size);
    }
  }
}

/// Custom timeout exception for testing
class TimeoutException implements Exception {
  const TimeoutException(this.message, this.timeout);
  
  final String message;
  final Duration timeout;
  
  @override
  String toString() => 'TimeoutException: $message (timeout: $timeout)';
}
