package handlers

import "github.com/gofiber/fiber/v2"

// ListAchievements retrieves all achievements
func ListAchievements(c *fiber.Ctx) error {
	// TODO: Implement list achievements logic
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "List achievements endpoint - not implemented yet",
	})
}

// ClaimAchievement claims an achievement for a user
func ClaimAchievement(c *fiber.Ctx) error {
	userID := c.Locals("user_id")
	// TODO: Implement claim achievement logic
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "Claim achievement endpoint - not implemented yet",
		"user_id": userID,
	})
}
