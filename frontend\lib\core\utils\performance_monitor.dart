import 'package:flutter/foundation.dart';
import '../logging/app_logger.dart';

/// Performance monitoring utility for tracking app performance
class PerformanceMonitor {
  static final Map<String, Stopwatch> _stopwatches = {};
  static final Map<String, List<Duration>> _metrics = {};

  /// Start timing an operation
  static void startTimer(String operation) {
    if (!kDebugMode) return;
    
    final stopwatch = Stopwatch()..start();
    _stopwatches[operation] = stopwatch;
    AppLogger.debug('Started timing: $operation', tag: 'PERF');
  }

  /// Stop timing an operation and log the result
  static Duration? stopTimer(String operation, {Map<String, dynamic>? context}) {
    if (!kDebugMode) return null;
    
    final stopwatch = _stopwatches.remove(operation);
    if (stopwatch == null) {
      AppLogger.warning('No timer found for operation: $operation', tag: 'PERF');
      return null;
    }

    stopwatch.stop();
    final duration = stopwatch.elapsed;
    
    // Store metric
    _metrics.putIfAbsent(operation, () => []).add(duration);
    
    // Log performance
    AppLogger.performance(operation, duration, context: context);
    
    return duration;
  }

  /// Time a synchronous operation
  static T timeSync<T>(String operation, T Function() function, {Map<String, dynamic>? context}) {
    if (!kDebugMode) return function();
    
    startTimer(operation);
    try {
      final result = function();
      stopTimer(operation, context: context);
      return result;
    } catch (error) {
      stopTimer(operation, context: context);
      rethrow;
    }
  }

  /// Time an asynchronous operation
  static Future<T> timeAsync<T>(
    String operation,
    Future<T> Function() function, {
    Map<String, dynamic>? context,
  }) async {
    if (!kDebugMode) return await function();
    
    startTimer(operation);
    try {
      final result = await function();
      stopTimer(operation, context: context);
      return result;
    } catch (error) {
      stopTimer(operation, context: context);
      rethrow;
    }
  }

  /// Get performance statistics for an operation
  static PerformanceStats? getStats(String operation) {
    if (!kDebugMode) return null;
    
    final durations = _metrics[operation];
    if (durations == null || durations.isEmpty) return null;

    final totalMs = durations.map((d) => d.inMilliseconds).reduce((a, b) => a + b);
    final avgMs = totalMs / durations.length;
    final minMs = durations.map((d) => d.inMilliseconds).reduce((a, b) => a < b ? a : b);
    final maxMs = durations.map((d) => d.inMilliseconds).reduce((a, b) => a > b ? a : b);

    return PerformanceStats(
      operation: operation,
      count: durations.length,
      totalMs: totalMs,
      averageMs: avgMs,
      minMs: minMs,
      maxMs: maxMs,
    );
  }

  /// Get all performance statistics
  static Map<String, PerformanceStats> getAllStats() {
    if (!kDebugMode) return {};
    
    final stats = <String, PerformanceStats>{};
    for (final operation in _metrics.keys) {
      final stat = getStats(operation);
      if (stat != null) {
        stats[operation] = stat;
      }
    }
    return stats;
  }

  /// Clear all performance data
  static void clear() {
    _stopwatches.clear();
    _metrics.clear();
    AppLogger.debug('Cleared performance data', tag: 'PERF');
  }

  /// Log all performance statistics
  static void logAllStats() {
    if (!kDebugMode) return;
    
    final stats = getAllStats();
    if (stats.isEmpty) {
      AppLogger.info('No performance statistics available', tag: 'PERF');
      return;
    }

    AppLogger.info('Performance Statistics:', tag: 'PERF');
    for (final stat in stats.values) {
      AppLogger.info(stat.toString(), tag: 'PERF');
    }
  }

  /// Monitor widget build performance
  static void monitorWidgetBuild(String widgetName, VoidCallback buildFunction) {
    if (!kDebugMode) {
      buildFunction();
      return;
    }
    
    timeSync('widget_build_$widgetName', buildFunction, context: {
      'widget': widgetName,
      'type': 'build',
    });
  }

  /// Monitor navigation performance
  static Future<void> monitorNavigation(String route, Future<void> Function() navigationFunction) async {
    await timeAsync('navigation_$route', navigationFunction, context: {
      'route': route,
      'type': 'navigation',
    });
  }

  /// Monitor API call performance
  static Future<T> monitorApiCall<T>(String endpoint, Future<T> Function() apiCall) async {
    return await timeAsync('api_$endpoint', apiCall, context: {
      'endpoint': endpoint,
      'type': 'api',
    });
  }
}

/// Performance statistics for an operation
class PerformanceStats {
  const PerformanceStats({
    required this.operation,
    required this.count,
    required this.totalMs,
    required this.averageMs,
    required this.minMs,
    required this.maxMs,
  });

  final String operation;
  final int count;
  final int totalMs;
  final double averageMs;
  final int minMs;
  final int maxMs;

  @override
  String toString() {
    return '$operation: ${count}x, avg: ${averageMs.toStringAsFixed(1)}ms, '
           'min: ${minMs}ms, max: ${maxMs}ms, total: ${totalMs}ms';
  }
}
