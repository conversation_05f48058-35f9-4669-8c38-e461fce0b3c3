# Quester Backend API

## Overview
This is the backend API for the Quester gamification platform, built with Go and Fiber framework.

## Features Implemented

### Core Structure
- ✅ Go module with proper dependencies
- ✅ Fiber web framework setup
- ✅ GORM database integration with PostgreSQL
- ✅ Redis integration for caching/sessions
- ✅ JWT authentication middleware
- ✅ Error handling middleware
- ✅ CORS configuration

### Database Models
- ✅ User management (User, Role, Permission, UserRole, RolePermission)
- ✅ Quest system (Quest, QuestStep, UserQuest, UserQuestStep)
- ✅ Achievement system (Achievement, UserAchievement)
- ✅ Wallet & transactions (Wallet, Transaction)
- ✅ Marketplace (MarketplaceItem)
- ✅ Notifications (Notification)
- ✅ User statistics (UserStats)
- ✅ Social features (FriendRequest)

### API Endpoints (Scaffolded)
- ✅ User management (`/api/register`, `/api/login`, `/api/profile`, etc.)
- ✅ Role & permission management (`/api/roles`, `/api/permissions`)
- ✅ Quest system (`/api/quests`, with CRUD operations)
- ✅ Achievement system (`/api/achievements`)
- ✅ Wallet & transactions (`/api/wallet`)
- ✅ Marketplace (`/api/marketplace`)
- ✅ Notifications (`/api/notifications`)
- ✅ Leaderboard (`/api/leaderboard`)

## Setup Instructions

### Prerequisites
- Go 1.22 or later
- PostgreSQL database
- Redis server

### Installation
1. Copy environment variables:
   ```bash
   cp .env.example .env
   ```

2. Update the `.env` file with your database and Redis configuration

3. Install dependencies:
   ```bash
   go mod download
   ```

4. Run the application:
   ```bash
   go run ./cmd/api/main.go
   ```

5. Or build and run:
   ```bash
   go build -o api.exe ./cmd/api
   ./api.exe
   ```

## Architecture

### Directory Structure
```
backend/
├── cmd/api/                 # Application entry point
├── internal/
│   ├── config/             # Configuration management
│   ├── database/           # Database connection & initialization
│   ├── handlers/           # HTTP request handlers
│   ├── middleware/         # Custom middleware
│   ├── models/             # Database models
│   └── services/           # Business logic services
└── pkg/                    # Public packages
```

### Current Status
- ✅ **Infrastructure**: Complete
- ✅ **Models**: Complete
- ✅ **Routes**: Complete
- ✅ **Authentication**: Basic middleware implemented
- 🔄 **Business Logic**: Handler stubs implemented, full logic pending
- 🔄 **Database Seeding**: Not implemented
- 🔄 **API Documentation**: Not implemented
- 🔄 **Testing**: Not implemented

## Dependencies
- **Fiber v2**: Web framework
- **GORM**: ORM for database operations
- **PostgreSQL Driver**: Database driver
- **Redis Go**: Redis client
- **JWT-Go**: JWT token handling
- **GoDotEnv**: Environment variable loading

## Next Steps
1. Implement business logic in handler functions
2. Connect handlers to services and database operations
3. Add input validation and error handling
4. Implement proper JWT secret management
5. Add API documentation (Swagger)
6. Add unit and integration tests
7. Set up database migrations and seeding
