import 'package:flutter/material.dart';
import '../../core/theme/app_theme.dart';
import 'custom_card.dart';

class QuestCard extends StatelessWidget {
  final String title;
  final String description;
  final double progress;
  final String reward;
  final String? difficulty;
  final String? timeLimit;
  final List<String>? tags;
  final VoidCallback? onTap;
  final bool isCompleted;
  final bool isLocked;

  const QuestCard({
    super.key,
    required this.title,
    required this.description,
    required this.progress,
    required this.reward,
    this.difficulty,
    this.timeLimit,
    this.tags,
    this.onTap,
    this.isCompleted = false,
    this.isLocked = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return CustomCard(
      onTap: isLocked ? null : onTap,
      backgroundColor: isLocked 
          ? Colors.grey.shade100 
          : isCompleted 
              ? AppTheme.successColor.withValues(alpha: 0.1)
              : Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Row
          Row(
            children: [
              // Quest Icon
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: isLocked
                      ? Colors.grey.shade300
                      : isCompleted
                          ? AppTheme.successColor
                          : AppTheme.primaryColor,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  isLocked
                      ? Icons.lock
                      : isCompleted
                          ? Icons.check
                          : Icons.flag,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              
              const SizedBox(width: 12),
              
              // Title and Difficulty
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isLocked 
                            ? Colors.grey.shade600 
                            : AppTheme.textPrimaryLight,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (difficulty != null)
                      Text(
                        difficulty!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: _getDifficultyColor(difficulty!),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                  ],
                ),
              ),
              
              // Status Indicator
              if (isCompleted)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.successColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Complete',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Description
          Text(
            description,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isLocked 
                  ? Colors.grey.shade500 
                  : AppTheme.textSecondaryLight,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          
          const SizedBox(height: 16),
          
          // Progress Bar (if not locked)
          if (!isLocked) ...[
            Row(
              children: [
                Expanded(
                  child: LinearProgressIndicator(
                    value: progress.clamp(0.0, 1.0),
                    backgroundColor: Colors.grey.shade200,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      isCompleted ? AppTheme.successColor : AppTheme.primaryColor,
                    ),
                    minHeight: 6,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  '${(progress * 100).toInt()}%',
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textSecondaryLight,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
          ],
          
          // Footer Row
          Row(
            children: [
              // Reward
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppTheme.accentColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.star,
                      size: 16,
                      color: AppTheme.accentColor,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      reward,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: AppTheme.accentColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Time Limit
              if (timeLimit != null) ...[
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.warningColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.timer,
                        size: 16,
                        color: AppTheme.warningColor,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        timeLimit!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: AppTheme.warningColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              
              const Spacer(),
              
              // Action Icon
              if (!isLocked)
                const Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: AppTheme.textSecondaryLight,
                ),
            ],
          ),
          
          // Tags
          if (tags != null && tags!.isNotEmpty) ...[
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: tags!.map((tag) => _buildTag(tag, theme)).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTag(String tag, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        tag,
        style: theme.textTheme.bodySmall?.copyWith(
          color: AppTheme.primaryColor,
          fontSize: 10,
        ),
      ),
    );
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return AppTheme.successColor;
      case 'medium':
        return AppTheme.warningColor;
      case 'hard':
        return AppTheme.errorColor;
      default:
        return AppTheme.mediumColor;
    }
  }
}

// Compact Quest Card for list views
class CompactQuestCard extends StatelessWidget {
  final String title;
  final String reward;
  final double progress;
  final VoidCallback? onTap;
  final bool isCompleted;

  const CompactQuestCard({
    super.key,
    required this.title,
    required this.reward,
    required this.progress,
    this.onTap,
    this.isCompleted = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return CustomCard(
      onTap: onTap,
      padding: const EdgeInsets.all(12),
      child: Row(
        children: [
          // Quest Icon
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: isCompleted ? AppTheme.successColor : AppTheme.primaryColor,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              isCompleted ? Icons.check : Icons.flag,
              color: Colors.white,
              size: 16,
            ),
          ),
          
          const SizedBox(width: 12),
          
          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryLight,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                if (!isCompleted)
                  LinearProgressIndicator(
                    value: progress.clamp(0.0, 1.0),
                    backgroundColor: Colors.grey.shade200,
                    valueColor: const AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                    minHeight: 3,
                  ),
              ],
            ),
          ),
          
          const SizedBox(width: 12),
          
          // Reward
          Text(
            reward,
            style: theme.textTheme.bodySmall?.copyWith(
              color: AppTheme.accentColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(width: 8),
          
          const Icon(
            Icons.arrow_forward_ios,
            size: 12,
            color: AppTheme.textSecondaryLight,
          ),
        ],
      ),
    );
  }
}
