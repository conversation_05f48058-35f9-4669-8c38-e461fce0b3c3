import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:quester_frontend/core/utils/responsive_helper.dart';
import 'package:quester_frontend/shared/widgets/adaptive_navigation.dart';
import 'package:quester_frontend/shared/widgets/adaptive_card.dart';

void main() {
  group('Responsive Design Tests', () {
    testWidgets('ResponsiveHelper returns correct device type for different screen sizes', (WidgetTester tester) async {
      // Test mobile size
      await tester.binding.setSurfaceSize(const Size(400, 800));
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              final deviceType = ResponsiveHelper.getDeviceType(context);
              expect(deviceType, DeviceType.compact);
              return Container();
            },
          ),
        ),
      );

      // Test tablet size
      await tester.binding.setSurfaceSize(const Size(700, 1000));
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              final deviceType = ResponsiveHelper.getDeviceType(context);
              expect(deviceType, DeviceType.medium);
              return Container();
            },
          ),
        ),
      );

      // Test desktop size
      await tester.binding.setSurfaceSize(const Size(1200, 800));
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              final deviceType = ResponsiveHelper.getDeviceType(context);
              expect(deviceType, DeviceType.expanded);
              return Container();
            },
          ),
        ),
      );
    });

    testWidgets('AdaptiveNavigation shows correct navigation type', (WidgetTester tester) async {
      const navigationItems = [
        NavigationItem(
          icon: Icons.home,
          selectedIcon: Icons.home,
          label: 'Home',
          route: '/home',
        ),
        NavigationItem(
          icon: Icons.search,
          selectedIcon: Icons.search,
          label: 'Search',
          route: '/search',
        ),
      ];

      // Test mobile navigation (bottom navigation)
      await tester.binding.setSurfaceSize(const Size(400, 800));
      await tester.pumpWidget(
        MaterialApp(
          home: AdaptiveNavigation(
            selectedIndex: 0,
            onDestinationSelected: (index) {},
            items: navigationItems,
            child: const Text('Content'),
          ),
        ),
      );

      expect(find.byType(NavigationBar), findsOneWidget);
      expect(find.byType(NavigationRail), findsNothing);

      // Test tablet navigation (navigation rail)
      await tester.binding.setSurfaceSize(const Size(700, 1000));
      await tester.pumpWidget(
        MaterialApp(
          home: AdaptiveNavigation(
            selectedIndex: 0,
            onDestinationSelected: (index) {},
            items: navigationItems,
            child: const Text('Content'),
          ),
        ),
      );

      expect(find.byType(NavigationRail), findsOneWidget);
      expect(find.byType(NavigationBar), findsNothing);
    });

    testWidgets('AdaptiveCard adjusts padding based on screen size', (WidgetTester tester) async {
      // Test mobile card
      await tester.binding.setSurfaceSize(const Size(400, 800));
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: AdaptiveCard(
              child: Text('Card Content'),
            ),
          ),
        ),
      );

      expect(find.byType(Card), findsOneWidget);
      expect(find.text('Card Content'), findsOneWidget);

      // Test desktop card
      await tester.binding.setSurfaceSize(const Size(1200, 800));
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: AdaptiveCard(
              child: Text('Card Content'),
            ),
          ),
        ),
      );

      expect(find.byType(Card), findsOneWidget);
      expect(find.text('Card Content'), findsOneWidget);
    });

    testWidgets('ResponsiveHelper provides correct navigation type', (WidgetTester tester) async {
      // Test mobile navigation type
      await tester.binding.setSurfaceSize(const Size(400, 800));
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              final navigationType = ResponsiveHelper.getNavigationType(context);
              expect(navigationType, NavigationType.bottom);
              return Container();
            },
          ),
        ),
      );

      // Test tablet navigation type
      await tester.binding.setSurfaceSize(const Size(700, 1000));
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              final navigationType = ResponsiveHelper.getNavigationType(context);
              expect(navigationType, NavigationType.rail);
              return Container();
            },
          ),
        ),
      );

      // Test desktop navigation type
      await tester.binding.setSurfaceSize(const Size(1200, 800));
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              final navigationType = ResponsiveHelper.getNavigationType(context);
              expect(navigationType, NavigationType.drawer);
              return Container();
            },
          ),
        ),
      );
    });

    testWidgets('ResponsiveHelper provides correct grid columns', (WidgetTester tester) async {
      // Test mobile grid columns
      await tester.binding.setSurfaceSize(const Size(400, 800));
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              final columns = ResponsiveHelper.getGridColumns(context);
              expect(columns, 1);
              return Container();
            },
          ),
        ),
      );

      // Test tablet grid columns
      await tester.binding.setSurfaceSize(const Size(700, 1000));
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              final columns = ResponsiveHelper.getGridColumns(context);
              expect(columns, 2);
              return Container();
            },
          ),
        ),
      );

      // Test desktop grid columns
      await tester.binding.setSurfaceSize(const Size(1200, 800));
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              final columns = ResponsiveHelper.getGridColumns(context);
              expect(columns, 3);
              return Container();
            },
          ),
        ),
      );
    });
  });
}
