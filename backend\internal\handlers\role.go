package handlers

import "github.com/gofiber/fiber/v2"

// ListRoles retrieves all available roles
func ListRoles(c *fiber.Ctx) error {
	// TODO: Implement list roles logic
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "List roles endpoint - not implemented yet",
	})
}

// ListPermissions retrieves all available permissions
func ListPermissions(c *fiber.Ctx) error {
	// TODO: Implement list permissions logic
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "List permissions endpoint - not implemented yet",
	})
}
