import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/achievement_model.dart';

class AchievementState {
  final bool isLoading;
  final List<Achievement> achievements;
  final List<UserAchievement> userAchievements;
  final Achievement? selectedAchievement;
  final String? error;

  const AchievementState({
    this.isLoading = false,
    this.achievements = const [],
    this.userAchievements = const [],
    this.selectedAchievement,
    this.error,
  });

  AchievementState copyWith({
    bool? isLoading,
    List<Achievement>? achievements,
    List<UserAchievement>? userAchievements,
    Achievement? selectedAchievement,
    String? error,
  }) {
    return AchievementState(
      isLoading: isLoading ?? this.isLoading,
      achievements: achievements ?? this.achievements,
      userAchievements: userAchievements ?? this.userAchievements,
      selectedAchievement: selectedAchievement ?? this.selectedAchievement,
      error: error,
    );
  }

  // Get unlocked achievements
  List<Achievement> get unlockedAchievements {
    return userAchievements.map((ua) => ua.achievement).toList();
  }

  // Get locked achievements
  List<Achievement> get lockedAchievements {
    final unlockedIds = userAchievements.map((ua) => ua.achievementId).toSet();
    return achievements.where((a) => !unlockedIds.contains(a.id)).toList();
  }
}

class AchievementNotifier extends StateNotifier<AchievementState> {
  AchievementNotifier() : super(const AchievementState());

  Future<void> loadAchievements({bool refresh = false}) async {
    if (refresh || state.achievements.isEmpty) {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      // For now, create mock data until we have the real API methods
      final achievements = await _getMockAchievements();
      final userAchievements = await _getMockUserAchievements();

      state = state.copyWith(
        isLoading: false,
        achievements: achievements,
        userAchievements: userAchievements,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> loadAchievementDetails(String achievementId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final achievement = state.achievements.firstWhere(
        (a) => a.id == achievementId,
        orElse: () => throw Exception('Achievement not found'),
      );
      
      state = state.copyWith(
        isLoading: false,
        selectedAchievement: achievement,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<bool> claimAchievement(String achievementId) async {
    try {
      // For now, simulate claiming an achievement
      final achievement = state.achievements.firstWhere((a) => a.id == achievementId);
      final newUserAchievement = UserAchievement(
        id: 'ua_${DateTime.now().millisecondsSinceEpoch}',
        userId: 'current_user',
        achievementId: achievementId,
        achievement: achievement,
        unlockedAt: DateTime.now(),
        isNotified: false,
      );

      final updatedUserAchievements = [...state.userAchievements, newUserAchievement];

      state = state.copyWith(
        userAchievements: updatedUserAchievements,
      );
      
      return true;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  void clearSelectedAchievement() {
    state = state.copyWith(selectedAchievement: null);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  // Get achievements by category
  List<Achievement> getAchievementsByCategory(AchievementCategory category) {
    return state.achievements.where((a) => a.category == category).toList();
  }

  // Get recent achievements (unlocked in last 7 days)
  List<UserAchievement> getRecentAchievements() {
    final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));
    return state.userAchievements
        .where((ua) => ua.unlockedAt.isAfter(sevenDaysAgo))
        .toList();
  }

  // Get achievement progress percentage
  double getProgressPercentage() {
    if (state.achievements.isEmpty) return 0.0;
    return state.userAchievements.length / state.achievements.length;
  }

  // Mock data methods - replace with real API calls
  Future<List<Achievement>> _getMockAchievements() async {
    await Future.delayed(const Duration(milliseconds: 500));
    return [
      Achievement(
        id: 'ach_1',
        name: 'First Steps',
        description: 'Complete your first quest',
        category: AchievementCategory.quests,
        type: AchievementType.milestone,
        rarity: AchievementRarity.common,
        points: 10,
        experience: 50,
        unlockCriteria: {'quests_completed': 1},
        isHidden: false,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Achievement(
        id: 'ach_2',
        name: 'Social Butterfly',
        description: 'Connect with 10 other questers',
        category: AchievementCategory.social,
        type: AchievementType.cumulative,
        rarity: AchievementRarity.uncommon,
        points: 25,
        experience: 100,
        unlockCriteria: {'friends_added': 10},
        isHidden: false,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  Future<List<UserAchievement>> _getMockUserAchievements() async {
    final achievements = await _getMockAchievements();
    return [
      UserAchievement(
        id: 'ua_1',
        userId: 'current_user',
        achievementId: 'ach_1',
        achievement: achievements.first,
        unlockedAt: DateTime.now().subtract(const Duration(days: 1)),
        isNotified: true,
      ),
    ];
  }
}

final achievementNotifierProvider = StateNotifierProvider<AchievementNotifier, AchievementState>((ref) {
  return AchievementNotifier();
});

// Filtered achievement providers
final unlockedAchievementsProvider = Provider<List<Achievement>>((ref) {
  return ref.watch(achievementNotifierProvider).unlockedAchievements;
});

final lockedAchievementsProvider = Provider<List<Achievement>>((ref) {
  return ref.watch(achievementNotifierProvider).lockedAchievements;
});

final recentAchievementsProvider = Provider<List<UserAchievement>>((ref) {
  final notifier = ref.watch(achievementNotifierProvider.notifier);
  return notifier.getRecentAchievements();
});

final achievementProgressProvider = Provider<double>((ref) {
  final notifier = ref.watch(achievementNotifierProvider.notifier);
  return notifier.getProgressPercentage();
});

// Achievement category providers
final questAchievementsProvider = Provider<List<Achievement>>((ref) {
  final notifier = ref.watch(achievementNotifierProvider.notifier);
  return notifier.getAchievementsByCategory(AchievementCategory.quests);
});

final socialAchievementsProvider = Provider<List<Achievement>>((ref) {
  final notifier = ref.watch(achievementNotifierProvider.notifier);
  return notifier.getAchievementsByCategory(AchievementCategory.social);
});

final explorationAchievementsProvider = Provider<List<Achievement>>((ref) {
  final notifier = ref.watch(achievementNotifierProvider.notifier);
  return notifier.getAchievementsByCategory(AchievementCategory.exploration);
});

// Achievement loading state providers
final achievementsLoadingProvider = Provider<bool>((ref) {
  return ref.watch(achievementNotifierProvider).isLoading;
});

final achievementErrorProvider = Provider<String?>((ref) {
  return ref.watch(achievementNotifierProvider).error;
});

final selectedAchievementProvider = Provider<Achievement?>((ref) {
  return ref.watch(achievementNotifierProvider).selectedAchievement;
});
